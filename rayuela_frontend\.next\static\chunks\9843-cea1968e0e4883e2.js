"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9843],{1950:(e,a,t)=>{t.d(a,{JT:()=>r,Nn:()=>i});let i={COMPONENT_TITLE:"Recomendaciones Inteligentes",COMPONENT_DESCRIPTION:"Basadas en el an\xe1lisis de {count} \xe1reas de mejora potencial",LOADING_TEXT:"Analizando m\xe9tricas para generar recomendaciones...",OPTIMIZED_TITLE:"Sistema Optimizado",OPTIMIZED_DESCRIPTION:"No se han detectado \xe1reas de mejora significativas en este momento.",OPTIMIZED_MESSAGE:"Las m\xe9tricas actuales indican que su sistema de recomendaci\xf3n est\xe1 funcionando de manera \xf3ptima. Contin\xfae monitoreando las m\xe9tricas para mantener este rendimiento.",RECOMMENDATION_COUNT:"{count} {count, plural, one {recomendaci\xf3n} other {recomendaciones}}",ACTIONS_TITLE:"Acciones recomendadas:",CATEGORY_ACCURACY:"Precisi\xf3n y Relevancia",CATEGORY_DIVERSITY:"Diversidad y Descubrimiento",CATEGORY_CONFIDENCE:"Confianza y Fiabilidad",CATEGORY_PERFORMANCE:"Rendimiento y Eficiencia",METRIC_PRECISION:"Precisi\xf3n",METRIC_DIVERSITY:"Diversidad",METRIC_CATALOG_COVERAGE:"Cobertura del Cat\xe1logo",METRIC_NOVELTY:"Novedad",METRIC_NDCG:"NDCG",METRIC_SERENDIPITY:"Serendipia",METRIC_AVG_CONFIDENCE:"Confianza Promedio",METRIC_CONFIDENCE_CHANGE:"Cambio en confianza",METRIC_TARGET:"Meta: {value}{unit}",METRIC_MODEL_COLLABORATIVE:"colaborativo",METRIC_MODEL_CONTENT:"basado en contenido",METRIC_MODEL_HYBRID:"h\xedbrido",METRIC_MODEL_CONFIDENCE:"Confianza {model}",METRIC_FACTOR_USER_HISTORY:"tama\xf1o del historial de usuario",METRIC_FACTOR_ITEM_POPULARITY:"popularidad del \xedtem",METRIC_FACTOR_CATEGORY_STRENGTH:"fuerza de categor\xeda",METRIC_FACTOR_MODEL_TYPE:"tipo de modelo",METRIC_FACTOR:"Factor {factor}",METRIC_REC_PRECISION_TITLE:"Mejorar la precisi\xf3n de las recomendaciones",METRIC_REC_DIVERSITY_TITLE:"Aumentar la diversidad de recomendaciones",METRIC_REC_NOVELTY_TITLE:"Incrementar la novedad de las recomendaciones",METRIC_REC_RANKING_TITLE:"Mejorar la calidad del ranking de recomendaciones",METRIC_REC_SERENDIPITY_TITLE:"Aumentar la serendipia en las recomendaciones",METRIC_REC_CONFIDENCE_TITLE:"Aumentar la confianza en las recomendaciones",METRIC_REC_MODEL_TITLE:"Mejorar el rendimiento del modelo {model}",METRIC_REC_CATEGORIES_TITLE:"Mejorar recomendaciones en categor\xedas de baja confianza",METRIC_REC_FACTOR_TITLE:"Mejorar el factor de {factor}",METRIC_REC_TREND_TITLE:"Abordar tendencia negativa en confianza",METRIC_REC_PRECISION_DESC:"La precisi\xf3n actual est\xe1 por debajo del umbral recomendado del 50%. Considere ajustar los modelos para mejorar la relevancia de las recomendaciones.",METRIC_REC_DIVERSITY_DESC:"Las recomendaciones actuales muestran poca diversidad, lo que puede llevar a una experiencia mon\xf3tona para los usuarios.",METRIC_REC_NOVELTY_DESC:"El sistema tiende a recomendar \xedtems muy populares, limitando el descubrimiento de nuevos productos.",METRIC_REC_RANKING_DESC:"El NDCG actual indica que el orden de las recomendaciones podr\xeda no ser \xf3ptimo, afectando la experiencia del usuario.",METRIC_REC_SERENDIPITY_DESC:"Las recomendaciones actuales podr\xedan ser demasiado predecibles, limitando el descubrimiento de \xedtems inesperados pero relevantes.",METRIC_REC_CONFIDENCE_DESC:"El nivel de confianza promedio est\xe1 por debajo del umbral recomendado del 60%, lo que puede indicar incertidumbre en las predicciones.",METRIC_REC_MODEL_DESC:"El modelo {model} muestra un nivel de confianza bajo, lo que reduce la efectividad general del sistema.",METRIC_REC_CATEGORIES_DESC:'Algunas categor\xedas de productos muestran niveles de confianza particularmente bajos, especialmente "{category}".',METRIC_REC_FACTOR_DESC:"El factor de {factor} tiene una contribuci\xf3n baja a la confianza general, lo que indica un \xe1rea de mejora.",METRIC_REC_TREND_DESC:"La confianza promedio ha disminuido significativamente en los \xfaltimos d\xedas, lo que podr\xeda indicar un problema emergente.",METRIC_REC_PRECISION_ACTION_1:"Ajustar los par\xe1metros del modelo colaborativo para dar m\xe1s peso a interacciones recientes",METRIC_REC_PRECISION_ACTION_2:"Aumentar el tama\xf1o del conjunto de entrenamiento con m\xe1s datos de interacciones",METRIC_REC_PRECISION_ACTION_3:"Implementar t\xe9cnicas de filtrado para eliminar outliers en los datos de entrenamiento",METRIC_REC_DIVERSITY_ACTION_1:"Implementar un algoritmo de re-ranking para diversificar los resultados",METRIC_REC_DIVERSITY_ACTION_2:"Ajustar los par\xe1metros del modelo para reducir la concentraci\xf3n en \xedtems populares",METRIC_REC_DIVERSITY_ACTION_3:"Introducir un factor de aleatoriedad controlada en las recomendaciones finales",METRIC_REC_DIVERSITY_ACTION_4:"Considerar categor\xedas menos representadas en las recomendaciones",METRIC_REC_NOVELTY_ACTION_1:"Ajustar el algoritmo para dar m\xe1s peso a \xedtems menos populares",METRIC_REC_NOVELTY_ACTION_2:"Implementar un factor de penalizaci\xf3n para \xedtems extremadamente populares",METRIC_REC_NOVELTY_ACTION_3:'Crear un segmento espec\xedfico de "descubrimientos" con \xedtems de baja popularidad pero alta relevancia',METRIC_REC_NOVELTY_ACTION_4:"Considerar t\xe9cnicas de filtrado colaborativo basadas en vecindad para encontrar \xedtems nicho",METRIC_REC_RANKING_ACTION_1:"Implementar o mejorar algoritmos de Learning-to-Rank",METRIC_REC_RANKING_ACTION_2:"Ajustar los factores de relevancia en el c\xe1lculo del ranking",METRIC_REC_RANKING_ACTION_3:"Considerar se\xf1ales adicionales como recencia o tendencia para el ranking",METRIC_REC_RANKING_ACTION_4:"Experimentar con diferentes funciones de p\xe9rdida optimizadas para NDCG",METRIC_REC_SERENDIPITY_ACTION_1:"Implementar un componente de serendipia que ocasionalmente introduzca \xedtems inesperados",METRIC_REC_SERENDIPITY_ACTION_2:"Explorar conexiones no obvias entre preferencias de usuario y productos",METRIC_REC_SERENDIPITY_ACTION_3:"Considerar t\xe9cnicas de recomendaci\xf3n basadas en conocimiento para descubrir relaciones no evidentes",METRIC_REC_SERENDIPITY_ACTION_4:"Experimentar con modelos de grafos para encontrar conexiones de segundo o tercer grado",METRIC_REC_CONFIDENCE_ACTION_1:"Recopilar m\xe1s datos de interacciones para mejorar la base de predicciones",METRIC_REC_CONFIDENCE_ACTION_2:"Ajustar los umbrales de confianza para filtrar recomendaciones de baja calidad",METRIC_REC_CONFIDENCE_ACTION_3:"Implementar t\xe9cnicas de ensemble para combinar m\xfaltiples modelos",METRIC_REC_CONFIDENCE_ACTION_4:"Mejorar la calidad de los metadatos de productos para fortalecer el modelo basado en contenido",METRIC_REC_MODEL_COLLAB_ACTION_1:"Ajustar los par\xe1metros de similitud entre usuarios/\xedtems",METRIC_REC_MODEL_COLLAB_ACTION_2:"Implementar t\xe9cnicas de factorizaci\xf3n matricial m\xe1s avanzadas",METRIC_REC_MODEL_COLLAB_ACTION_3:"Aumentar el n\xfamero de vecinos considerados en el algoritmo KNN",METRIC_REC_MODEL_COLLAB_ACTION_4:"Reducir el umbral de filtrado para interacciones m\xednimas",METRIC_REC_MODEL_CONTENT_ACTION_1:"Mejorar la calidad y cantidad de atributos de los productos",METRIC_REC_MODEL_CONTENT_ACTION_2:"Implementar t\xe9cnicas de procesamiento de lenguaje natural m\xe1s avanzadas",METRIC_REC_MODEL_CONTENT_ACTION_3:"Ajustar los pesos de los diferentes atributos en el c\xe1lculo de similitud",METRIC_REC_MODEL_CONTENT_ACTION_4:"Considerar la incorporaci\xf3n de embeddings pre-entrenados para representar productos",METRIC_REC_MODEL_HYBRID_ACTION_1:"Ajustar los pesos relativos de los modelos colaborativo y basado en contenido",METRIC_REC_MODEL_HYBRID_ACTION_2:"Implementar un meta-modelo para seleccionar din\xe1micamente el mejor enfoque",METRIC_REC_MODEL_HYBRID_ACTION_3:"Considerar factores contextuales adicionales en la combinaci\xf3n de modelos",METRIC_REC_MODEL_HYBRID_ACTION_4:"Experimentar con diferentes estrategias de ensemble",METRIC_REC_CATEGORIES_ACTION_1:'Recopilar m\xe1s datos de interacciones para la categor\xeda "{category}"',METRIC_REC_CATEGORIES_ACTION_2:"Crear modelos espec\xedficos por categor\xeda para las categor\xedas problem\xe1ticas",METRIC_REC_CATEGORIES_ACTION_3:"Mejorar los metadatos de productos en estas categor\xedas",METRIC_REC_CATEGORIES_ACTION_4:"Considerar reglas de negocio espec\xedficas para complementar el algoritmo",METRIC_REC_FACTOR_USER_HISTORY_ACTION_1:"Implementar estrategias para aumentar la recopilaci\xf3n de interacciones de usuarios",METRIC_REC_FACTOR_USER_HISTORY_ACTION_2:"Mejorar el onboarding para capturar preferencias iniciales",METRIC_REC_FACTOR_USER_HISTORY_ACTION_3:"Considerar t\xe9cnicas de cold-start para usuarios con poco historial",METRIC_REC_FACTOR_USER_HISTORY_ACTION_4:"Implementar recomendaciones basadas en sesi\xf3n para usuarios nuevos",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1:"Balancear mejor las recomendaciones entre \xedtems populares y de nicho",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2:"Implementar un sistema de boosting temporal para nuevos productos",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3:"Crear segmentos de recomendaci\xf3n espec\xedficos para diferentes niveles de popularidad",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4:"Mejorar la estrategia de exploraci\xf3n vs. explotaci\xf3n",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1:"Mejorar la taxonom\xeda de categor\xedas para capturar mejor las preferencias",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2:"Implementar an\xe1lisis de afinidad de categor\xeda m\xe1s sofisticado",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3:"Considerar la jerarqu\xeda completa de categor\xedas en el c\xe1lculo de afinidad",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4:"Crear modelos espec\xedficos para categor\xedas principales",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1:"Experimentar con diferentes arquitecturas de modelos",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2:"Implementar un sistema de selecci\xf3n din\xe1mica de modelos basado en contexto",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3:"Considerar modelos m\xe1s avanzados como deep learning para recomendaciones",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4:"Mejorar la estrategia de ensemble para combinar modelos",METRIC_REC_TREND_ACTION_1:"Investigar cambios recientes en datos o modelos que podr\xedan haber afectado la confianza",METRIC_REC_TREND_ACTION_2:"Verificar la calidad de las interacciones recientes",METRIC_REC_TREND_ACTION_3:"Considerar un rollback a una versi\xf3n anterior del modelo si la tendencia persiste",METRIC_REC_TREND_ACTION_4:"Implementar monitoreo en tiempo real para detectar cambios abruptos en m\xe9tricas clave"};function r(e,a){return e.replace(/{([^}]+)}/g,(e,t)=>{let i=t.match(/^([^,]+),\s*plural,\s*one\s*{([^}]+)}\s*other\s*{([^}]+)}$/);if(i){let e=i[1].trim(),t=i[2].trim(),r=i[3].trim();return 1===a[e]?t:r}return void 0!==a[t]?a[t]:e})}},5524:(e,a,t)=>{t.d(a,{Vd:()=>E,qC:()=>c,_X:()=>C,Io:()=>s});var i=t(2115);let r=[{id:"improve-precision",metricPath:["summary","precision"],threshold:.5,comparison:"lt",title:"METRIC_REC_PRECISION_TITLE",description:"METRIC_REC_PRECISION_DESC",priority:"high",category:"accuracy",iconKey:"BarChart2Icon",actions:["METRIC_REC_PRECISION_ACTION_1","METRIC_REC_PRECISION_ACTION_2","METRIC_REC_PRECISION_ACTION_3"],metrics:[{name:"METRIC_PRECISION",valuePath:["summary","precision"],valueMultiplier:100,target:50,unit:"%"}]},{id:"improve-diversity",metricPath:["summary","diversity"],threshold:.4,comparison:"lt",title:"METRIC_REC_DIVERSITY_TITLE",description:"METRIC_REC_DIVERSITY_DESC",priority:"medium",category:"diversity",iconKey:"ShuffleIcon",actions:["METRIC_REC_DIVERSITY_ACTION_1","METRIC_REC_DIVERSITY_ACTION_2","METRIC_REC_DIVERSITY_ACTION_3","METRIC_REC_DIVERSITY_ACTION_4"],metrics:[{name:"METRIC_DIVERSITY",valuePath:["summary","diversity"],valueMultiplier:100,target:40,unit:"%"},{name:"METRIC_CATALOG_COVERAGE",valuePath:["summary","catalog_coverage"],valueMultiplier:100,target:30,unit:"%"}]},{id:"improve-novelty",metricPath:["summary","novelty"],threshold:.3,comparison:"lt",title:"METRIC_REC_NOVELTY_TITLE",description:"METRIC_REC_NOVELTY_DESC",priority:"medium",category:"diversity",iconKey:"ZapIcon",actions:["METRIC_REC_NOVELTY_ACTION_1","METRIC_REC_NOVELTY_ACTION_2","METRIC_REC_NOVELTY_ACTION_3","METRIC_REC_NOVELTY_ACTION_4"],metrics:[{name:"METRIC_NOVELTY",valuePath:["summary","novelty"],valueMultiplier:100,target:30,unit:"%"}]},{id:"improve-ranking",metricPath:["summary","ndcg"],threshold:.45,comparison:"lt",title:"METRIC_REC_RANKING_TITLE",description:"METRIC_REC_RANKING_DESC",priority:"high",category:"accuracy",iconKey:"BarChart2Icon",actions:["METRIC_REC_RANKING_ACTION_1","METRIC_REC_RANKING_ACTION_2","METRIC_REC_RANKING_ACTION_3","METRIC_REC_RANKING_ACTION_4"],metrics:[{name:"METRIC_NDCG",valuePath:["summary","ndcg"],valueMultiplier:100,target:45,unit:"%"}]},{id:"improve-serendipity",metricPath:["summary","serendipity"],threshold:.25,comparison:"lt",title:"METRIC_REC_SERENDIPITY_TITLE",description:"METRIC_REC_SERENDIPITY_DESC",priority:"low",category:"diversity",iconKey:"LightbulbIcon",actions:["METRIC_REC_SERENDIPITY_ACTION_1","METRIC_REC_SERENDIPITY_ACTION_2","METRIC_REC_SERENDIPITY_ACTION_3","METRIC_REC_SERENDIPITY_ACTION_4"],metrics:[{name:"METRIC_SERENDIPITY",valuePath:["summary","serendipity"],valueMultiplier:100,target:25,unit:"%"}]},{id:"improve-confidence",specialLogic:{type:"avgConfidence",params:{threshold:.6,paths:[["confidence_distribution","collaborative","avg"],["confidence_distribution","content","avg"],["confidence_distribution","hybrid","avg"]]}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_CONFIDENCE_TITLE",description:"METRIC_REC_CONFIDENCE_DESC",priority:"high",category:"confidence",iconKey:"AlertCircleIcon",actions:["METRIC_REC_CONFIDENCE_ACTION_1","METRIC_REC_CONFIDENCE_ACTION_2","METRIC_REC_CONFIDENCE_ACTION_3","METRIC_REC_CONFIDENCE_ACTION_4"],metrics:[{name:"METRIC_AVG_CONFIDENCE",valuePath:[],valueMultiplier:100,target:60,unit:"%"}]},{id:"improve-model-type",specialLogic:{type:"worstModel",params:{threshold:.5,models:{collaborative:{path:["confidence_distribution","collaborative","avg"],name:"METRIC_MODEL_COLLABORATIVE",actions:["METRIC_REC_MODEL_COLLAB_ACTION_1","METRIC_REC_MODEL_COLLAB_ACTION_2","METRIC_REC_MODEL_COLLAB_ACTION_3","METRIC_REC_MODEL_COLLAB_ACTION_4"]},content:{path:["confidence_distribution","content","avg"],name:"METRIC_MODEL_CONTENT",actions:["METRIC_REC_MODEL_CONTENT_ACTION_1","METRIC_REC_MODEL_CONTENT_ACTION_2","METRIC_REC_MODEL_CONTENT_ACTION_3","METRIC_REC_MODEL_CONTENT_ACTION_4"]},hybrid:{path:["confidence_distribution","hybrid","avg"],name:"METRIC_MODEL_HYBRID",actions:["METRIC_REC_MODEL_HYBRID_ACTION_1","METRIC_REC_MODEL_HYBRID_ACTION_2","METRIC_REC_MODEL_HYBRID_ACTION_3","METRIC_REC_MODEL_HYBRID_ACTION_4"]}}}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_MODEL_TITLE",description:"METRIC_REC_MODEL_DESC",priority:"medium",category:"confidence",iconKey:"DatabaseIcon",actions:[],metrics:[{name:"METRIC_MODEL_CONFIDENCE",valuePath:[],valueMultiplier:100,target:50,unit:"%"}]},{id:"improve-low-confidence-categories",specialLogic:{type:"lowConfidenceCategories",params:{threshold:.5,maxCategories:3}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_CATEGORIES_TITLE",description:"METRIC_REC_CATEGORIES_DESC",priority:"medium",category:"confidence",iconKey:"DatabaseIcon",actions:["METRIC_REC_CATEGORIES_ACTION_1","METRIC_REC_CATEGORIES_ACTION_2","METRIC_REC_CATEGORIES_ACTION_3","METRIC_REC_CATEGORIES_ACTION_4"],metrics:[]},{id:"improve-confidence-factors",specialLogic:{type:"lowestConfidenceFactor",params:{threshold:.4,factors:{user_history_size:{name:"METRIC_FACTOR_USER_HISTORY",actions:["METRIC_REC_FACTOR_USER_HISTORY_ACTION_1","METRIC_REC_FACTOR_USER_HISTORY_ACTION_2","METRIC_REC_FACTOR_USER_HISTORY_ACTION_3","METRIC_REC_FACTOR_USER_HISTORY_ACTION_4"]},item_popularity:{name:"METRIC_FACTOR_ITEM_POPULARITY",actions:["METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4"]},category_strength:{name:"METRIC_FACTOR_CATEGORY_STRENGTH",actions:["METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4"]},model_type:{name:"METRIC_FACTOR_MODEL_TYPE",actions:["METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4"]}}}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_FACTOR_TITLE",description:"METRIC_REC_FACTOR_DESC",priority:"medium",category:"confidence",iconKey:"UsersIcon",actions:[],metrics:[{name:"METRIC_FACTOR",valuePath:[],valueMultiplier:100,target:40,unit:"%"}]},{id:"address-negative-trend",specialLogic:{type:"negativeTrend",params:{minDays:3,threshold:.9,path:["confidence_trends","last_7_days"]}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_TREND_TITLE",description:"METRIC_REC_TREND_DESC",priority:"high",category:"confidence",iconKey:"TrendingUpIcon",actions:["METRIC_REC_TREND_ACTION_1","METRIC_REC_TREND_ACTION_2","METRIC_REC_TREND_ACTION_3","METRIC_REC_TREND_ACTION_4"],metrics:[{name:"METRIC_CONFIDENCE_CHANGE",valuePath:[],valueMultiplier:100,target:0,unit:"%"}]}];var o=t(1950);function n(e){var a;return null!=(a=o.Nn[e])?a:e}function _(e,a){return a.reduce((e,a)=>e&&"object"==typeof e&&null!==e&&a in e?e[a]:void 0,e)}function C(e,a,t){let C=[];r.forEach(r=>{if(r.specialLogic){let e=function(e,a,t,r){if(!e.specialLogic)return null;let{type:C,params:E}=e.specialLogic;switch(C){case"avgConfidence":{if(!(null==E?void 0:E.paths)||!E.threshold)return null;let a=E.paths.map(e=>_(t,e)||0),C=a.reduce((e,a)=>e+a,0)/a.length;if(C<E.threshold)return{id:e.id,title:(0,o.JT)(n(e.title),{}),description:(0,o.JT)(n(e.description),{}),priority:e.priority,category:e.category,icon:i.createElement(r[e.iconKey],{className:"h-5 w-5 text-red-500"}),actions:e.actions.map(e=>n(e)),metrics:e.metrics.map(e=>({name:(0,o.JT)(n(e.name),{}),value:C*(e.valueMultiplier||1),target:e.target,unit:e.unit}))};return null}case"worstModel":{if(!(null==E?void 0:E.models)||!E.threshold)return null;let a={};Object.entries(E.models).forEach(e=>{let[i,r]=e,n=_(t,r.path);a[i]={value:"number"==typeof n?n:0,name:o.Nn[r.name],actions:r.actions}});let n=Object.entries(a).sort((e,a)=>{let[,t]=e,[,i]=a;return t.value-i.value})[0];if(n&&n[1].value<E.threshold){let a=n[1].name;return{id:"".concat(e.id,"-").concat(n[0]),title:(0,o.JT)(o.Nn[e.title],{model:a}),description:(0,o.JT)(o.Nn[e.description],{model:a}),priority:e.priority,category:e.category,icon:i.createElement(r[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:n[1].actions.map(e=>o.Nn[e]),metrics:e.metrics.map(e=>({name:(0,o.JT)(o.Nn[e.name],{model:a}),value:n[1].value*(e.valueMultiplier||1),target:e.target,unit:e.unit}))}}return null}case"lowConfidenceCategories":{if(!(null==E?void 0:E.threshold)||!E.maxCategories||0===Object.keys(t.category_confidence).length)return null;let a=Object.entries(t.category_confidence).sort((e,a)=>{let[,t]=e,[,i]=a;return t-i}).slice(0,E.maxCategories);if(a[0][1]<E.threshold){let t=a[0][0];return{id:e.id,title:(0,o.JT)(o.Nn[e.title],{}),description:(0,o.JT)(o.Nn[e.description],{category:t}),priority:e.priority,category:e.category,icon:i.createElement(r[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:e.actions.map(e=>(0,o.JT)(o.Nn[e],{category:t})),metrics:a.map(e=>({name:"Confianza en ".concat(e[0]),value:100*e[1],target:100*E.threshold,unit:"%"}))}}return null}case"lowestConfidenceFactor":{if(!(null==E?void 0:E.factors)||!E.threshold)return null;let a=Object.entries(t.confidence_factors).sort((e,a)=>{let[,t]=e,[,i]=a;return t-i})[0];if(a[1]<E.threshold){let t=a[0],_=E.factors[t];if(!_)return null;let C=o.Nn[_.name];return{id:"".concat(e.id,"-").concat(t),title:(0,o.JT)(n(e.title),{factor:C}),description:(0,o.JT)(n(e.description),{factor:C}),priority:e.priority,category:e.category,icon:i.createElement(r[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:_.actions.map(e=>n(e)),metrics:e.metrics.map(e=>({name:(0,o.JT)(n(e.name),{factor:C}),value:a[1]*(e.valueMultiplier||1),target:e.target,unit:e.unit}))}}return null}case"negativeTrend":{if(!(null==E?void 0:E.minDays)||!E.threshold||!E.path)return null;let a=_(t,E.path);if(a&&Array.isArray(a)&&a.length>=E.minDays){var c,s;let t=a.slice(-E.minDays);if((null==(c=t[t.length-1])?void 0:c.avg_confidence)<(null==(s=t[0])?void 0:s.avg_confidence)*E.threshold){let a=(t[t.length-1].avg_confidence/t[0].avg_confidence-1)*100;return{id:e.id,title:(0,o.JT)(n(e.title),{}),description:(0,o.JT)(n(e.description),{}),priority:e.priority,category:e.category,icon:i.createElement(r[e.iconKey],{className:"h-5 w-5 text-red-500"}),actions:e.actions.map(e=>n(e)),metrics:e.metrics.map(e=>({name:(0,o.JT)(n(e.name),{}),value:a,target:e.target,unit:e.unit}))}}}return null}default:return null}}(r,0,a,t);e&&C.push(e);return}let E=_("summary"===r.metricPath[0]?e:a,r.metricPath);void 0!==E&&"number"==typeof E&&function(e,a,t){switch(t){case"lt":return e<a;case"gt":return e>a;case"eq":return e===a;case"lte":return e<=a;case"gte":return e>=a;default:return!1}}(E,r.threshold,r.comparison)&&C.push({id:r.id,title:(0,o.JT)(n(r.title),{}),description:(0,o.JT)(n(r.description),{}),priority:r.priority,category:r.category,icon:i.createElement(t[r.iconKey],{className:"h-5 w-5 ".concat("high"===r.priority?"text-red-500":"medium"===r.priority?"text-amber-500":"text-blue-500")}),actions:r.actions.map(e=>n(e)),metrics:r.metrics.map(t=>{let i=_("summary"===t.valuePath[0]?e:a,t.valuePath);return{name:(0,o.JT)(n(t.name),{}),value:i*(t.valueMultiplier||1),target:t.target,unit:t.unit}})})});let E={high:0,medium:1,low:2};return C.sort((e,a)=>E[e.priority]-E[a.priority]),C}function E(e,a,t,i){var r,o,n,_,C,E,c;let s=!!i&&!!i,T="true"===localStorage.getItem("seenPostModalHighlight"),l=!1;if(i&&i.created_at&&("string"==typeof i.created_at||"number"==typeof i.created_at)){let e=new Date(i.created_at);l=(new Date().getTime()-e.getTime())/36e5<24}let d=null!=(C=null!=(_=a.storage_used)?_:null==(r=a.storage)?void 0:r.usedBytes)?C:0,R=d>0,I=d>0,m=!!(null==(o=a.training)?void 0:o.lastTrainingDate),u=(null!=(c=null!=(E=a.api_calls_count)?E:null==(n=a.apiCalls)?void 0:n.used)?c:0)>0;return{updatedItems:t.map(e=>"generate_key"===e.id&&e.autoDetect?{...e,completed:s||T}:"send_catalog_data"===e.id&&e.autoDetect?{...e,completed:R}:"send_interaction_data"===e.id&&e.autoDetect?{...e,completed:I}:"train_model"===e.id&&e.autoDetect?{...e,completed:m}:"first_recommendation"===e.id&&e.autoDetect?{...e,completed:u}:e),isNewApiKey:l,hasApiKey:s,hasSentCatalogData:R,hasSentInteractionData:I,hasTrainedModel:m,hasMadeApiCalls:u}}function c(e,a,t,i,r,o,n){let _={};return e.forEach(e=>{"generate_key"===e.id&&e.autoDetect?_[e.id]=a||t:"send_catalog_data"===e.id&&e.autoDetect?_[e.id]=i:"send_interaction_data"===e.id&&e.autoDetect?_[e.id]=r:"train_model"===e.id&&e.autoDetect?_[e.id]=o:"first_recommendation"===e.id&&e.autoDetect?_[e.id]=n:_[e.id]=e.completed}),_}function s(e,a){return e&&!localStorage.getItem("checklistHighlighted")}},6102:(e,a,t)=>{t.d(a,{Bc:()=>n,ZI:()=>E,k$:()=>C,m_:()=>_});var i=t(5155);t(2115);var r=t(3815),o=t(9434);function n(e){let{delayDuration:a=0,...t}=e;return(0,i.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:a,...t})}function _(e){let{...a}=e;return(0,i.jsx)(n,{children:(0,i.jsx)(r.bL,{"data-slot":"tooltip",...a})})}function C(e){let{...a}=e;return(0,i.jsx)(r.l9,{"data-slot":"tooltip-trigger",...a})}function E(e){let{className:a,sideOffset:t=0,children:n,..._}=e;return(0,i.jsx)(r.ZL,{children:(0,i.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,o.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",a),..._,children:[n,(0,i.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}}}]);