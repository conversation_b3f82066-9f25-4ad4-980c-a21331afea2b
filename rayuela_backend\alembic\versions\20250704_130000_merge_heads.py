"""Merge branch heads 20250704_120003 and b2c3d4e5f6g7

Revision ID: 20250704_130000
Revises: 20250704_120003, b2c3d4e5f6g7
Create Date: 2025-07-04 13:30:00
"""

from typing import Sequence, Union
from alembic import op, context

revision: str = "20250704_130000"
down_revision: Union[str, tuple[str, ...]] = ("20250704_120003", "b2c3d4e5f6g7")
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # No schema changes; this migration only resolves multiple heads.
    pass


def downgrade() -> None:
    # Downgrade simply returns to previous branch points.
    pass 