{"/_not-found/page": "/_not-found", "/api/health/route": "/api/health", "/sitemap.xml/route": "/sitemap.xml", "/page": "/", "/register/page": "/register", "/(dashboard)/billing/page": "/billing", "/(dashboard)/api-keys/page": "/api-keys", "/(dashboard)/page": "/", "/(dashboard)/models/page": "/models", "/(dashboard)/pipeline/training-jobs/page": "/pipeline/training-jobs", "/(dashboard)/dashboard/page": "/dashboard", "/(dashboard)/recommendation-metrics/page": "/recommendation-metrics", "/(dashboard)/usage/page": "/usage", "/(dashboard)/pipeline/ingestion-jobs/page": "/pipeline/ingestion-jobs", "/(dashboard)/pipeline/page": "/pipeline", "/(dashboard)/settings/page": "/settings", "/(public)/login/page": "/login", "/(public)/verify-email/[token]/page": "/verify-email/[token]", "/(public)/contact-sales/page": "/contact-sales", "/(public)/docs/page": "/docs", "/(public)/features/page": "/features", "/(public)/docs/quickstart/python/page": "/docs/quickstart/python", "/(public)/home/<USER>": "/home", "/(public)/pricing/page": "/pricing", "/(public)/legal/cookies/page": "/legal/cookies", "/(public)/legal/notice/page": "/legal/notice", "/(public)/legal/dpa/page": "/legal/dpa", "/(public)/legal/terms/page": "/legal/terms", "/(public)/legal/privacy/page": "/legal/privacy"}