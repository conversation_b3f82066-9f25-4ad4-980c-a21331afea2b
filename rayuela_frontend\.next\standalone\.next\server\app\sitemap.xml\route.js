(()=>{var e={};e.id=5475,e.ids=[5475],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveManifest:function(){return a},resolveRobots:function(){return o},resolveRouteData:function(){return l},resolveSitemap:function(){return n}});let i=r(77341);function o(e){let t="";for(let r of Array.isArray(e.rules)?e.rules:[e.rules]){for(let e of(0,i.resolveArray)(r.userAgent||["*"]))t+=`User-Agent: ${e}
`;if(r.allow)for(let e of(0,i.resolveArray)(r.allow))t+=`Allow: ${e}
`;if(r.disallow)for(let e of(0,i.resolveArray)(r.disallow))t+=`Disallow: ${e}
`;r.crawlDelay&&(t+=`Crawl-delay: ${r.crawlDelay}
`),t+="\n"}return e.host&&(t+=`Host: ${e.host}
`),e.sitemap&&(0,i.resolveArray)(e.sitemap).forEach(e=>{t+=`Sitemap: ${e}
`}),t}function n(e){let t=e.some(e=>Object.keys(e.alternates??{}).length>0),r=e.some(e=>{var t;return!!(null==(t=e.images)?void 0:t.length)}),i=e.some(e=>{var t;return!!(null==(t=e.videos)?void 0:t.length)}),o="";for(let s of(o+='<?xml version="1.0" encoding="UTF-8"?>\n',o+='<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',r&&(o+=' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'),i&&(o+=' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"'),t?o+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':o+=">\n",e)){var n,a,l;o+="<url>\n",o+=`<loc>${s.url}</loc>
`;let e=null==(n=s.alternates)?void 0:n.languages;if(e&&Object.keys(e).length)for(let t in e)o+=`<xhtml:link rel="alternate" hreflang="${t}" href="${e[t]}" />
`;if(null==(a=s.images)?void 0:a.length)for(let e of s.images)o+=`<image:image>
<image:loc>${e}</image:loc>
</image:image>
`;if(null==(l=s.videos)?void 0:l.length)for(let e of s.videos)o+=["<video:video>",`<video:title>${e.title}</video:title>`,`<video:thumbnail_loc>${e.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${e.description}</video:description>`,e.content_loc&&`<video:content_loc>${e.content_loc}</video:content_loc>`,e.player_loc&&`<video:player_loc>${e.player_loc}</video:player_loc>`,e.duration&&`<video:duration>${e.duration}</video:duration>`,e.view_count&&`<video:view_count>${e.view_count}</video:view_count>`,e.tag&&`<video:tag>${e.tag}</video:tag>`,e.rating&&`<video:rating>${e.rating}</video:rating>`,e.expiration_date&&`<video:expiration_date>${e.expiration_date}</video:expiration_date>`,e.publication_date&&`<video:publication_date>${e.publication_date}</video:publication_date>`,e.family_friendly&&`<video:family_friendly>${e.family_friendly}</video:family_friendly>`,e.requires_subscription&&`<video:requires_subscription>${e.requires_subscription}</video:requires_subscription>`,e.live&&`<video:live>${e.live}</video:live>`,e.restriction&&`<video:restriction relationship="${e.restriction.relationship}">${e.restriction.content}</video:restriction>`,e.platform&&`<video:platform relationship="${e.platform.relationship}">${e.platform.content}</video:platform>`,e.uploader&&`<video:uploader${e.uploader.info&&` info="${e.uploader.info}"`}>${e.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(s.lastModified){let e=s.lastModified instanceof Date?s.lastModified.toISOString():s.lastModified;o+=`<lastmod>${e}</lastmod>
`}s.changeFrequency&&(o+=`<changefreq>${s.changeFrequency}</changefreq>
`),"number"==typeof s.priority&&(o+=`<priority>${s.priority}</priority>
`),o+="</url>\n"}return o+"</urlset>\n"}function a(e){return JSON.stringify(e)}function l(e,t){return"robots"===t?o(e):"sitemap"===t?n(e):"manifest"===t?a(e):""}},13233:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>h,serverHooks:()=>$,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>w});var i={};r.r(i),r.d(i,{default:()=>y});var o={};r.r(o),r.d(o,{GET:()=>g});var n=r(96559),a=r(48088),l=r(37719),s=r(32190),u=r(29021),d=r.n(u),c=r(33873),p=r.n(c);let f=process.env.NEXT_PUBLIC_SITE_URL||"https://rayuela.ai";function y(){let e=new Date;return[{url:f,lastModified:e,changeFrequency:"weekly",priority:1},{url:`${f}/features`,lastModified:e,changeFrequency:"monthly",priority:.9},{url:`${f}/pricing`,lastModified:e,changeFrequency:"monthly",priority:.9},{url:`${f}/docs`,lastModified:e,changeFrequency:"weekly",priority:.8},{url:`${f}/legal/privacy`,lastModified:e,changeFrequency:"yearly",priority:.3},{url:`${f}/legal/terms`,lastModified:e,changeFrequency:"yearly",priority:.3},{url:`${f}/legal/notice`,lastModified:e,changeFrequency:"yearly",priority:.3},{url:`${f}/legal/cookies`,lastModified:e,changeFrequency:"yearly",priority:.3},{url:`${f}/legal/dpa`,lastModified:e,changeFrequency:"yearly",priority:.3},...(function(){let e=p().join(process.cwd(),"../rayuela_backend/docs"),t=[];try{["api/index","api/recommendations","api/pipeline","quickstart/README","quickstart/python","quickstart/nodejs","quickstart/php","guides/data_ingestion_guide"].forEach(r=>{let i=p().join(e,`${r}.md`),o=new Date;try{o=d().statSync(i).mtime}catch{}let n=r.replace(/\/README$/,"").replace(/\.md$/,"");t.push({url:`${f}/docs/${n}`,lastModified:o,priority:.8})})}catch(e){console.warn("Could not read documentation files:",e)}return t})().map(e=>({...e,changeFrequency:"weekly"}))]}var v=r(12127);let m={...i}.default;if("function"!=typeof m)throw Error('Default export is missing in "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\sitemap.ts"');async function g(e,t){let{__metadata_id__:r,...i}=await t.params||{},o=!!r&&r.endsWith(".xml");if(r&&!o)return new s.NextResponse("Not Found",{status:404});let n=r&&o?r.slice(0,-4):void 0,a=await m({id:n}),l=(0,v.resolveRouteData)(a,"sitemap");return new s.NextResponse(l,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let h=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?filePath=D%3A%5Cvscode_workspace%5Ccloned_repos%5Crayuela%5Crayuela_frontend%5Csrc%5Capp%5Csitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:_,workUnitAsyncStorage:w,serverHooks:$}=h;function x(){return(0,l.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:w})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77341:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e:[e]}function i(e){if(null!=e)return r(e)}function o(e){let t;if("string"==typeof e)try{t=(e=new URL(e)).origin}catch{}return t}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getOrigin:function(){return o},resolveArray:function(){return r},resolveAsArrayOrUndefined:function(){return i}})},78335:()=>{},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580],()=>r(13233));module.exports=i})();