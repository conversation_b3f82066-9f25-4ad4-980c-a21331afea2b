"""merge_multiple_heads

Revision ID: db98aeee1b35
Revises: 20250704_120000, b2c3d4e5f6g7
Create Date: 2025-07-05 02:47:18.303754

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'db98aeee1b35'
down_revision: Union[str, None] = ('20250704_120000', 'b2c3d4e5f6g7')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
