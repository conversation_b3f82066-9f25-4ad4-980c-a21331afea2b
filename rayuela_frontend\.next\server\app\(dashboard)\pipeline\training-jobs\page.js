(()=>{var e={};e.id=5507,e.ids=[5507],e.modules={2844:(e,s,r)=>{Promise.resolve().then(r.bind(r,75754))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5085:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(65239),n=r(48088),a=r(88170),i=r.n(a),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["(dashboard)",{children:["pipeline",{children:["training-jobs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55435)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/pipeline/training-jobs/page",pathname:"/pipeline/training-jobs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16412:(e,s,r)=>{Promise.resolve().then(r.bind(r,55435))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25475:(e,s,r)=>{"use strict";r.d(s,{G:()=>a});var t=r(43210),n=r(81184);function a(){let[e,s]=(0,t.useState)([]),[r,a]=(0,t.useState)(!0),[i,l]=(0,t.useState)(null),o=async()=>{try{a(!0),l(null);try{let e=(await (0,n._C)().listTrainingJobsApiV1PipelineJobsGet()).data.map(e=>{let s={...e,model_name:e.model?.artifact_name??"Recommendation Model",model_version:e.model?.artifact_version??"v1.0",status:e.status.toUpperCase(),parameters:e.parameters?Object.fromEntries(Object.entries(e.parameters).filter(([,e])=>"number"==typeof e||"string"==typeof e)):void 0,metrics:e.metrics?Object.fromEntries(Object.entries(e.metrics).filter(([,e])=>"number"==typeof e)):void 0};if(s.started_at&&s.completed_at){let e=new Date(s.started_at).getTime(),r=new Date(s.completed_at).getTime();s.duration=Math.round((r-e)/1e3)}return s});s(e);return}catch(e){l("Error fetching training jobs"),console.error("Error fetching training jobs:",e)}}catch(e){l(e instanceof Error?e.message:"Error loading training jobs"),console.error("Error loading training jobs:",e)}finally{a(!1)}},d=async e=>{try{let e=await (0,n._C)().trainModelsApiV1PipelineTrainPost();return await o(),e.data}catch(e){throw console.error("Error starting training:",e),e}};return{jobs:e,isLoading:r,error:i,fetchJobs:o,getJobStatus:async e=>{try{return(await (0,n._C)().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(e)).data}catch(e){throw console.error("Error fetching training job status:",e),e}},startTraining:d}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55435:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\pipeline\\\\training-jobs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75754:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>k});var t=r(60687),n=r(43210),a=r(44493),i=r(29523),l=r(85726),o=r(6211),d=r(91821),c=r(97840),m=r(78200),x=r(93613),h=r(80462),u=r(99270),p=r(13861),j=r(13943),g=r(85650),b=r(41585),v=r(89667),f=r(80013),N=r(15079),y=r(63503),_=r(25475),w=r(62796),E=r(5336),C=r(84027),A=r(41862);function P({onTrainingStart:e,trigger:s}){let[r,a]=(0,n.useState)(!1),[l,o]=(0,n.useState)("hybrid"),[h,u]=(0,n.useState)(!1),[p,j]=(0,n.useState)({learning_rate:.001,epochs:50,batch_size:32,embedding_dim:64,regularization:.001}),[g,b]=(0,n.useState)(!1),[_,w]=(0,n.useState)(null),[P,F]=(0,n.useState)(!1),z=(e,s)=>{j(r=>({...r,[e]:s}))},k=async()=>{b(!0),w(null);try{let s={model_type:l,force:!1};h&&(s.hyperparameters={learning_rate:p.learning_rate,epochs:p.epochs,batch_size:p.batch_size,embedding_dim:p.embedding_dim,regularization:p.regularization});let r=await e(s);console.log("Training started:",r),F(!0),setTimeout(()=>{a(!1),F(!1),u(!1),o("hybrid")},3e3)}catch(e){w(e instanceof Error?e.message:"Error iniciando entrenamiento")}finally{b(!1)}};return(0,t.jsxs)(y.lG,{open:r,onOpenChange:a,children:[(0,t.jsx)(y.zM,{asChild:!0,children:s||(0,t.jsxs)(i.Button,{children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Nuevo Entrenamiento"]})}),(0,t.jsxs)(y.Cf,{className:"sm:max-w-lg",children:[(0,t.jsxs)(y.c7,{children:[(0,t.jsx)(y.L3,{children:"Nuevo Entrenamiento de Modelo"}),(0,t.jsx)(y.rr,{children:"Inicia el entrenamiento de un modelo de recomendaci\xf3n personalizado con tus datos"})]}),P?(0,t.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,t.jsx)(E.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,t.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Entrenamiento iniciado!"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu modelo est\xe1 siendo entrenado"})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{htmlFor:"modelType",children:"Tipo de modelo"}),(0,t.jsxs)(N.l6,{value:l,onValueChange:e=>o(e),children:[(0,t.jsx)(N.bq,{children:(0,t.jsx)(N.yv,{placeholder:"Selecciona el tipo de modelo"})}),(0,t.jsxs)(N.gC,{children:[(0,t.jsx)(N.eb,{value:"hybrid",children:"H\xedbrido (recomendado)"}),(0,t.jsx)(N.eb,{value:"collaborative",children:"Filtrado colaborativo"}),(0,t.jsx)(N.eb,{value:"content",children:"Basado en contenido"})]})]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["hybrid"===l&&"Combina m\xfaltiples t\xe9cnicas para mejores resultados","collaborative"===l&&"Basado en comportamiento de usuarios similares","content"===l&&"Basado en caracter\xedsticas de productos"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{id:"advanced",type:"checkbox",className:"rounded border-gray-300",checked:h,onChange:e=>u(e.target.checked)}),(0,t.jsxs)(f.J,{htmlFor:"advanced",className:"flex items-center",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"Configuraci\xf3n avanzada"]})]}),h&&(0,t.jsxs)("div",{className:"space-y-3 p-4 border rounded-lg bg-muted/50",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{htmlFor:"learning_rate",children:"Learning Rate"}),(0,t.jsx)(v.p,{id:"learning_rate",type:"number",step:"0.0001",value:p.learning_rate,onChange:e=>z("learning_rate",parseFloat(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{htmlFor:"epochs",children:"\xc9pocas"}),(0,t.jsx)(v.p,{id:"epochs",type:"number",value:p.epochs,onChange:e=>z("epochs",parseInt(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{htmlFor:"batch_size",children:"Batch Size"}),(0,t.jsx)(v.p,{id:"batch_size",type:"number",value:p.batch_size,onChange:e=>z("batch_size",parseInt(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{htmlFor:"embedding_dim",children:"Embedding Dim"}),(0,t.jsx)(v.p,{id:"embedding_dim",type:"number",value:p.embedding_dim,onChange:e=>z("embedding_dim",parseInt(e.target.value))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{htmlFor:"regularization",children:"Regularizaci\xf3n"}),(0,t.jsx)(v.p,{id:"regularization",type:"number",step:"0.0001",value:p.regularization,onChange:e=>z("regularization",parseFloat(e.target.value))})]})]}),_&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:_})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,t.jsx)("p",{children:"El entrenamiento puede tomar varios minutos dependiendo del volumen de datos"}),(0,t.jsx)("p",{children:"Se requiere un m\xednimo de 100 interacciones para entrenar un modelo"})]})]}),(0,t.jsxs)(y.Es,{children:[(0,t.jsx)(i.Button,{variant:"outline",onClick:()=>{g||(a(!1),w(null),F(!1),u(!1),o("hybrid"))},disabled:g,children:"Cancelar"}),!P&&(0,t.jsx)(i.Button,{onClick:k,disabled:g,children:g?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Entrenando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Iniciar Entrenamiento"]})})]})]})]})}var F=r(13668),z=r(58133);function k(){let{jobs:e,isLoading:s,error:r,startTraining:E}=(0,_.G)(),[C,A]=(0,n.useState)(null),{filteredJobs:k,searchQuery:S,setSearchQuery:q,statusFilter:J,setStatusFilter:T,clearFilters:I}=(0,w.x)(e,(e,s)=>{let r=s.toLowerCase();return e.model_name.toLowerCase().includes(r)||e.model_version.toLowerCase().includes(r)||e.job_id.toString().includes(s)}),D=e=>"FAILED"===e.status,M=e=>{console.log("Retrying job:",e)};return s?(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,t.jsx)(l.E,{className:"h-8 w-64 mb-2"}),(0,t.jsx)(l.E,{className:"h-4 w-96"})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(l.E,{className:"h-6 w-48"}),(0,t.jsx)(l.E,{className:"h-4 w-32"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsx)(l.E,{className:"h-64 w-full"})})]})]}):(0,t.jsxs)(z.hI,{title:"Historial de Entrenamientos",description:"Seguimiento completo de todos tus trabajos de entrenamiento de modelos",actions:(0,t.jsx)(P,{onTrainingStart:E,trigger:(0,t.jsxs)(i.Button,{children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Nuevo Entrenamiento"]})}),children:[(0,t.jsx)(z.os,{title:"Resumen",icon:(0,t.jsx)(m.A,{className:"h-6 w-6 text-purple-500"}),children:(0,t.jsxs)("div",{className:"flex gap-4 text-sm text-muted-foreground p-6",children:[(0,t.jsxs)("span",{children:["Total: ",e.length]}),(0,t.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,t.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,t.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})}),r&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(d.XL,{children:"Error"}),(0,t.jsx)(d.TN,{children:r})]}),(0,t.jsx)(z.os,{title:"Filtros",icon:(0,t.jsx)(h.A,{className:"h-5 w-5"}),children:(0,t.jsxs)(a.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(v.p,{placeholder:"Buscar por modelo, versi\xf3n o ID...",value:S,onChange:e=>q(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsx)(f.J,{htmlFor:"status-filter",className:"text-sm font-medium whitespace-nowrap",children:"Estado:"}),(0,t.jsxs)(N.l6,{value:J,onValueChange:e=>T(e),children:[(0,t.jsx)(N.bq,{id:"status-filter",className:"w-40",children:(0,t.jsx)(N.yv,{})}),(0,t.jsxs)(N.gC,{children:[(0,t.jsx)(N.eb,{value:"all",children:"Todos"}),(0,t.jsx)(N.eb,{value:"pending",children:"Pendientes"}),(0,t.jsx)(N.eb,{value:"processing",children:"Procesando"}),(0,t.jsx)(N.eb,{value:"completed",children:"Completados"}),(0,t.jsx)(N.eb,{value:"failed",children:"Fallidos"})]})]}),(S||"all"!==J)&&(0,t.jsx)(i.Button,{variant:"outline",size:"sm",onClick:I,children:"Limpiar"})]})]}),(S||"all"!==J)&&(0,t.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:["Mostrando ",k.length," de ",e.length," trabajos"]})]})}),(0,t.jsx)(z.os,{title:"Trabajos de Entrenamiento",description:"Lista completa de entrenamientos con detalles y m\xe9tricas",children:(0,t.jsx)(a.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-hidden",children:(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{className:"bg-muted/10",children:(0,t.jsxs)(o.Hj,{className:"border-b border-border/30",children:[(0,t.jsx)(o.nd,{className:"font-semibold",children:"Job ID"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"Modelo / Versi\xf3n"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"Estado"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"M\xe9tricas"}),(0,t.jsx)(o.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,t.jsx)(o.BF,{children:k.length>0?k.map((e,s)=>(0,t.jsxs)(z.AP,{index:s,children:[(0,t.jsx)(o.nA,{className:"font-medium py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,F.cR)(e.status),"#",e.job_id]})}),(0,t.jsx)(o.nA,{className:"py-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.model_name}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:e.model_version})]})}),(0,t.jsx)(o.nA,{className:"py-4",children:(0,F.KC)(e.status)}),(0,t.jsx)(o.nA,{className:"py-4 text-muted-foreground",children:(0,g.GP)(new Date(e.created_at),"d 'de' MMM, yyyy HH:mm",{locale:b.es})}),(0,t.jsx)(o.nA,{className:"py-4 text-muted-foreground",children:e.duration?(0,F.a3)(e.duration):"PROCESSING"===e.status?"⏳ En curso":"—"}),(0,t.jsx)(o.nA,{className:"py-4",children:e.metrics?(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Acc: ",(100*e.metrics.accuracy).toFixed(1),"%"]}),(0,t.jsxs)("div",{className:"text-muted-foreground",children:["F1: ",(100*e.metrics.f1_score).toFixed(1),"%"]})]}):(0,t.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,t.jsx)(o.nA,{className:"text-right py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,t.jsxs)(y.lG,{children:[(0,t.jsx)(y.zM,{asChild:!0,children:(0,t.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>A(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(y.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(y.c7,{children:[(0,t.jsxs)(y.L3,{children:["Detalles del Job #",e.job_id]}),(0,t.jsx)(y.rr,{children:"Informaci\xf3n completa del trabajo de entrenamiento"})]}),C&&(0,t.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{className:"text-sm font-medium",children:"Modelo"}),(0,t.jsxs)("p",{className:"text-sm",children:[C.model_name," ",C.model_version]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{className:"text-sm font-medium",children:"Estado"}),(0,t.jsx)("div",{className:"mt-1",children:(0,F.KC)(C.status)})]})]}),C.parameters&&(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{className:"text-sm font-medium",children:"Par\xe1metros"}),(0,t.jsx)("pre",{className:"text-xs bg-muted p-2 rounded mt-1 overflow-x-auto",children:JSON.stringify(C.parameters,null,2)})]}),C.metrics&&(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{className:"text-sm font-medium",children:"M\xe9tricas"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-2 mt-1",children:Object.entries(C.metrics).map(([e,s])=>(0,t.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,t.jsx)("div",{className:"text-xs font-medium",children:e}),(0,t.jsxs)("div",{className:"text-sm",children:[(100*s).toFixed(2),"%"]})]},e))})]}),C.error_message&&(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,t.jsxs)(d.Fc,{variant:"destructive",className:"mt-1",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{className:"text-sm",children:C.error_message})]})]}),C.task_id&&(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{className:"text-sm font-medium",children:"Task ID"}),(0,t.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:C.task_id})]})]})]})]}),D(e)&&(0,t.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>M(e.job_id),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]})})]},e.job_id)):(0,t.jsx)(z.AP,{index:0,children:(0,t.jsx)(o.nA,{colSpan:7,className:"text-center py-8",children:(0,t.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-8 w-8"}),(0,t.jsx)("p",{children:"No hay trabajos de entrenamiento a\xfan"}),(0,t.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando inicies entrenamientos"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"h-8 w-8"}),(0,t.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,t.jsx)(i.Button,{variant:"outline",size:"sm",onClick:I,children:"Limpiar filtros"})]})})})})})]})})})}),(0,t.jsxs)(d.Fc,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(d.XL,{children:"Informaci\xf3n sobre entrenamientos"}),(0,t.jsx)(d.TN,{children:(0,t.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,t.jsx)("p",{children:"Los trabajos de entrenamiento pueden tomar desde minutos hasta horas dependiendo del tama\xf1o de los datos y la complejidad del modelo."}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Pendiente:"})," El trabajo est\xe1 en cola esperando recursos"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Procesando:"})," El entrenamiento est\xe1 en curso"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Completado:"})," El modelo se entren\xf3 exitosamente"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Fallido:"})," Ocurri\xf3 un error durante el entrenamiento"]})]})]})})]})]})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,2713,5814,5423,1576,7400,6920,2807,5320,3302],()=>r(5085));module.exports=t})();