"use strict";exports.id=1576,exports.ids=[1576],exports.modules={73393:(e,t,n)=>{n.d(t,{Mz:()=>eK,i3:()=>e0,UC:()=>eZ,bL:()=>eJ,Bk:()=>eD});var r=n(43210);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>c[e])}function x(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function v(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function R(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),f=g(a),s=p(t),u="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,w=i[f]/2-o[f]/2;switch(s){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=w*(n&&u?-1:1);break;case"end":r[a]+=w*(n&&u?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:u,y:c}=R(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:x}=await m({x:u,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});u=null!=g?g:u,c=null!=y?y:c,p={...p,[o]:{...p[o],...w}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(s=!0===x.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):x.rects),{x:u,y:c}=R(s,d,f)),n=-1)}return{x:u,y:c,placement:d,strategy:i,middlewareData:p}};async function L(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=e,{boundary:s="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=v(h),g=a[p?"floating"===c?"reference":"floating":c],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:u,strategy:f})),w="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),R=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},A=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:f}):w);return{top:(y.top-A.top+m.top)/R.y,bottom:(A.bottom-y.bottom+m.bottom)/R.y,left:(y.left-A.left+m.left)/R.x,right:(A.right-y.right+m.right)/R.x}}function T(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return i.some(t=>e[t]>=0)}async function S(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===y(n),s=["left","top"].includes(l)?-1:1,u=o&&f?-1:1,c=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),f?{x:g*u,y:m*s}:{x:m*s,y:g*u}}function E(){return"undefined"!=typeof window}function O(e){return H(e)?(e.nodeName||"").toLowerCase():"#document"}function C(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(H(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function H(e){return!!E()&&(e instanceof Node||e instanceof C(e).Node)}function $(e){return!!E()&&(e instanceof Element||e instanceof C(e).Element)}function j(e){return!!E()&&(e instanceof HTMLElement||e instanceof C(e).HTMLElement)}function D(e){return!!E()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof C(e).ShadowRoot)}function N(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=V(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function M(e){let t=W(),n=$(e)?V(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function W(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(O(e))}function V(e){return C(e).getComputedStyle(e)}function z(e){return $(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function I(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||k(e);return D(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=I(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:j(n)&&N(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=C(i);if(o){let e=Y(l);return t.concat(l,l.visualViewport||[],N(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){let t=V(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=j(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function _(e){return $(e)?e:e.contextElement}function Q(e){let t=_(e);if(!j(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=q(t),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let U=s(0);function G(e){let t=C(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function J(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=_(e),a=s(1);t&&(r?$(r)&&(a=Q(r)):a=Q(e));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===C(l))&&i)?G(l):s(0),u=(o.left+f.x)/a.x,c=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=C(l),t=r&&$(r)?C(r):r,n=e,i=Y(n);for(;i&&r&&t!==n;){let e=Q(i),t=i.getBoundingClientRect(),r=V(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,p*=e.y,u+=o,c+=l,i=Y(n=C(i))}}return b({width:d,height:p,x:u,y:c})}function K(e,t){let n=z(e).scrollLeft;return t?t.left+n:J(k(e)).left+n}function Z(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:K(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=C(e),r=k(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let e=W();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(e,n);else if("document"===t)r=function(e){let t=k(e),n=z(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+K(e),f=-n.scrollTop;return"rtl"===V(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(k(e));else if($(t))r=function(e,t){let n=J(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=j(e)?Q(e):s(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=G(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===V(e).position}function en(e,t){if(!j(e)||"fixed"===V(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=C(e);if(F(e))return n;if(!j(e)){let t=I(e);for(;t&&!B(t);){if($(t)&&!et(t))return t;t=I(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(O(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!M(r)?n:r||function(e){let t=I(e);for(;j(t)&&!B(t);){if(M(t))return t;if(F(t))break;t=I(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=j(t),i=k(t),o="fixed"===n,l=J(e,!0,o,t),a={scrollLeft:0,scrollTop:0},f=s(0);if(r||!r&&!o)if(("body"!==O(t)||N(i))&&(a=z(t)),r){let e=J(t,!0,o,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else i&&(f.x=K(i));let u=!i||r||o?s(0):Z(i,a);return{x:l.left+a.scrollLeft-f.x-u.x,y:l.top+a.scrollTop-f.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=k(r),a=!!t&&F(t.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},u=s(1),c=s(0),d=j(r);if((d||!d&&!o)&&(("body"!==O(r)||N(l))&&(f=z(r)),j(r))){let e=J(r);u=Q(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let p=!l||d||o?s(0):Z(l,f,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-f.scrollLeft*u.x+c.x+p.x,y:n.y*u.y-f.scrollTop*u.y+c.y+p.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>$(e)&&"body"!==O(e)),i=null,o="fixed"===V(e).position,l=o?I(e):e;for(;$(l)&&!B(l);){let t=V(l),n=M(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||N(l)&&!n&&function e(t,n){let r=I(t);return!(r===n||!$(r)||B(r))&&("fixed"===V(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=I(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],f=a[0],s=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,f,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=q(e);return{width:t,height:n}},getScale:Q,isElement:$,isRTL:function(e){return"rtl"===V(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:f,elements:s,middlewareData:u}=t,{element:c,padding:p=0}=d(e,t)||{};if(null==c)return{};let w=v(p),x={x:n,y:r},b=m(y(i)),R=g(b),A=await f.getDimensions(c),L="y"===b,T=L?"clientHeight":"clientWidth",P=a.reference[R]+a.reference[b]-x[b]-a.floating[R],S=x[b]-a.reference[b],E=await (null==f.getOffsetParent?void 0:f.getOffsetParent(c)),O=E?E[T]:0;O&&await (null==f.isElement?void 0:f.isElement(E))||(O=s.floating[T]||a.floating[R]);let C=O/2-A[R]/2-1,k=o(w[L?"top":"left"],C),H=o(w[L?"bottom":"right"],C),$=O-A[R]-H,j=O/2-A[R]/2+(P/2-S/2),D=l(k,o(j,$)),N=!u.arrow&&null!=h(i)&&j!==D&&a.reference[R]/2-(j<k?k:H)-A[R]/2<0,F=N?j<k?j-k:j-$:0;return{[b]:x[b]+F,data:{[b]:D,centerOffset:j-D-F,...N&&{alignmentOffset:F}},reset:N}}}),ef=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return A(e,t,{...i,platform:o})};var es=n(51215),eu="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ec(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ec(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ec(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return eu(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await S(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=d(e,t),c={x:n,y:r},h=await L(t,u),g=y(p(i)),w=m(g),x=c[w],v=c[g];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}if(f){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=v+h[e],r=v-h[t];v=l(n,o(v,r))}let b=s.fn({...t,[w]:x,[g]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:f}}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(e,t),u={x:n,y:r},c=y(i),h=m(c),g=u[h],w=u[c],x=d(a,t),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+v.mainAxis,n=o.reference[h]+o.reference[e]-v.mainAxis;g<t?g=t:g>n&&(g=n)}if(s){var b,R;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[c]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[c])||0)+(t?0:v.crossAxis),r=o.reference[c]+o.reference[e]+(t?0:(null==(R=l.offset)?void 0:R[c])||0)-(t?v.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[c]:w}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:u,platform:c,elements:v}=t,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:A,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:S=!0,...E}=d(e,t);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let O=p(a),C=y(u),k=p(u)===u,H=await (null==c.isRTL?void 0:c.isRTL(v.floating)),$=A||(k||!S?[x(u)]:function(e){let t=x(e);return[w(e),t,w(t)]}(u)),j="none"!==P;!A&&j&&$.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(u,S,P,H));let D=[u,...$],N=await L(t,E),F=[],M=(null==(r=f.flip)?void 0:r.overflows)||[];if(b&&F.push(N[O]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=x(l)),[l,x(l)]}(a,s,H);F.push(N[e[0]],N[e[1]])}if(M=[...M,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(i=f.flip)?void 0:i.index)||0)+1,t=D[e];if(t)return{data:{index:e,overflows:M},reset:{placement:t}};let n=null==(o=M.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(T){case"bestFit":{let e=null==(l=M.filter(e=>{if(j){let t=y(e.placement);return t===C||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:f,rects:s,platform:u,elements:c}=t,{apply:m=()=>{},...g}=d(e,t),w=await L(t,g),x=p(f),v=h(f),b="y"===y(f),{width:R,height:A}=s.floating;"top"===x||"bottom"===x?(i=x,a=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(a=x,i="end"===v?"top":"bottom");let T=A-w.top-w.bottom,P=R-w.left-w.right,S=o(A-w[i],T),E=o(R-w[a],P),O=!t.middlewareData.shift,C=S,k=E;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=P),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=T),O&&!v){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?k=R-2*(0!==e||0!==t?e+t:l(w.left,w.right)):C=A-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:k,availableHeight:C});let H=await u.getDimensions(c.floating);return R!==H.width||A!==H.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=T(await L(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{let e=T(await L(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}}(e),options:[e,t]}),eR=(e,t)=>({...em(e),options:[e,t]});var eA=n(8730),eL=n(60687),eT=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,eA.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,eL.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),eP=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eL.jsx)(eT.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eL.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eP.displayName="Arrow";var eS=n(98599),eE=n(11273),eO=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,eA.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,eL.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),eC=n(13495),ek=n(66156),eH=n(18853),e$="Popper",[ej,eD]=(0,eE.A)(e$),[eN,eF]=ej(e$),eM=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eL.jsx)(eN,{scope:t,anchor:i,onAnchorChange:o,children:n})};eM.displayName=e$;var eW="PopperAnchor",eB=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eF(eW,n),a=r.useRef(null),f=(0,eS.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(i?.current||a.current)}),i?null:(0,eL.jsx)(eO.div,{...o,ref:f})});eB.displayName=eW;var eV="PopperContent",[ez,eI]=ej(eV),eX=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:a=0,align:s="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:y="optimized",onPlaced:w,...x}=e,v=eF(eV,n),[b,R]=r.useState(null),A=(0,eS.s)(t,e=>R(e)),[L,T]=r.useState(null),P=(0,eH.X)(L),S=P?.width??0,E=P?.height??0,O="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},C=Array.isArray(p)?p:[p],H=C.length>0,$={padding:O,boundary:C.filter(eQ),altBoundary:H},{refs:j,floatingStyles:D,placement:N,isPositioned:F,middlewareData:M}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:u}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);ec(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(e=>{e!==A.current&&(A.current=e,g(e))},[]),v=r.useCallback(e=>{e!==L.current&&(L.current=e,w(e))},[]),b=l||m,R=a||y,A=r.useRef(null),L=r.useRef(null),T=r.useRef(c),P=null!=s,S=eh(s),E=eh(o),O=eh(u),C=r.useCallback(()=>{if(!A.current||!L.current)return;let e={placement:t,strategy:n,middleware:p};E.current&&(e.platform=E.current),ef(A.current,L.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};k.current&&!ec(T.current,t)&&(T.current=t,es.flushSync(()=>{d(t)}))})},[p,t,n,E,O]);eu(()=>{!1===u&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[u]);let k=r.useRef(!1);eu(()=>(k.current=!0,()=>{k.current=!1}),[]),eu(()=>{if(b&&(A.current=b),R&&(L.current=R),b&&R){if(S.current)return S.current(b,R,C);C()}},[b,R,C,S,P]);let H=r.useMemo(()=>({reference:A,floating:L,setReference:x,setFloating:v}),[x,v]),$=r.useMemo(()=>({reference:b,floating:R}),[b,R]),j=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!$.floating)return e;let t=ep($.floating,c.x),r=ep($.floating,c.y);return f?{...e,transform:"translate("+t+"px, "+r+"px)",...ed($.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,f,$.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:C,refs:H,elements:$,floatingStyles:j}),[c,C,H,$,j])}({strategy:"fixed",placement:i+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=_(e),h=a||s?[...p?X(p):[],...X(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=p&&c?function(e,t){let n,r=null,i=k(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(u,c){void 0===u&&(u=!1),void 0===c&&(c=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(u||t(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),x={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,c))||1},v=!0;function b(t){let r=t[0].intersectionRatio;if(r!==c){if(!v)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||s(),v=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,x)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;u&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let w=d?J(e):null;return d&&function t(){let r=J(e);w&&!el(w,r)&&n(),w=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===y}),elements:{reference:v.anchor},middleware:[eg({mainAxis:a+E,alignmentAxis:u}),d&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ew():void 0,...$}),d&&ex({...$}),ev({...$,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:i,height:o}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${i}px`),l.setProperty("--radix-popper-anchor-height",`${o}px`)}}),L&&eR({element:L,padding:c}),eU({arrowWidth:S,arrowHeight:E}),g&&eb({strategy:"referenceHidden",...$})]}),[W,B]=eG(N),V=(0,eC.c)(w);(0,ek.N)(()=>{F&&V?.()},[F,V]);let z=M.arrow?.x,I=M.arrow?.y,Y=M.arrow?.centerOffset!==0,[q,Q]=r.useState();return(0,ek.N)(()=>{b&&Q(window.getComputedStyle(b).zIndex)},[b]),(0,eL.jsx)("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:F?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[M.transformOrigin?.x,M.transformOrigin?.y].join(" "),...M.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eL.jsx)(ez,{scope:n,placedSide:W,onArrowChange:T,arrowX:z,arrowY:I,shouldHideArrow:Y,children:(0,eL.jsx)(eO.div,{"data-side":W,"data-align":B,...x,ref:A,style:{...x.style,animation:F?void 0:"none"}})})})});eX.displayName=eV;var eY="PopperArrow",eq={top:"bottom",right:"left",bottom:"top",left:"right"},e_=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eI(eY,n),o=eq[i.placedSide];return(0,eL.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eL.jsx)(eP,{...r,ref:t,style:{...r.style,display:"block"}})})});function eQ(e){return null!==e}e_.displayName=eY;var eU=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,l=o?0:e.arrowWidth,a=o?0:e.arrowHeight,[f,s]=eG(n),u={start:"0%",center:"50%",end:"100%"}[s],c=(i.arrow?.x??0)+l/2,d=(i.arrow?.y??0)+a/2,p="",h="";return"bottom"===f?(p=o?u:`${c}px`,h=`${-a}px`):"top"===f?(p=o?u:`${c}px`,h=`${r.floating.height+a}px`):"right"===f?(p=`${-a}px`,h=o?u:`${d}px`):"left"===f&&(p=`${r.floating.width+a}px`,h=o?u:`${d}px`),{data:{x:p,y:h}}}});function eG(e){let[t,n="center"]=e.split("-");return[t,n]}var eJ=eM,eK=eB,eZ=eX,e0=e_},93613:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},97364:(e,t,n)=>{n.d(t,{bL:()=>s,Qg:()=>a});var r=n(43210);n(51215);var i=n(8730),o=n(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?n:t,{...l,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),f=r.forwardRef((e,t)=>(0,o.jsx)(l.span,{...e,ref:t,style:{...a,...e.style}}));f.displayName="VisuallyHidden";var s=f}};