(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1670],{1792:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>y});var s=r(5155),t=r(2115),n=r(6695),i=r(285),l=r(1284),o=r(1007),d=r(1264),c=r(9803),m=r(3861),u=r(9074),x=r(8313),h=r(2523),p=r(5057),f=r(7313),g=r(5365),v=r(8856),b=r(7588),j=r(3999),N=r(5731);function y(){var e;let{user:a,token:r,apiKey:y}=(0,j.A)(),[E,w]=(0,t.useState)(!0),[A,I]=(0,t.useState)(null),[k,C]=(0,t.useState)(null);return(0,t.useEffect)(()=>{(async()=>{if(!r||!y)return w(!1);try{let e=await (0,N._)();I(e)}catch(e){console.error("Error al obtener datos de la cuenta:",e),C(e.message||"Error al cargar los datos de la cuenta")}finally{w(!1)}})()},[r,y]),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6 text-gray-800 dark:text-white",children:"Settings"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Manage your account settings and preferences."}),(0,s.jsxs)(g.Fc,{variant:"warning",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(g.XL,{children:"Funcionalidad en desarrollo"}),(0,s.jsxs)(g.TN,{children:[(0,s.jsx)("p",{children:"Estamos trabajando en mejorar esta secci\xf3n. Algunas funcionalidades est\xe1n en desarrollo y estar\xe1n disponibles pr\xf3ximamente."}),(0,s.jsx)("p",{className:"mt-2",children:'Actualmente, solo la pesta\xf1a "Account" est\xe1 parcialmente funcional. Las dem\xe1s pesta\xf1as estar\xe1n disponibles en futuras actualizaciones.'})]})]}),(0,s.jsxs)(f.tU,{defaultValue:"account",className:"w-full",children:[(0,s.jsxs)(f.j7,{className:"mb-6",children:[(0,s.jsx)(f.Xi,{value:"account",children:"Account"}),(0,s.jsx)(f.Xi,{value:"notifications",children:"Notifications"}),(0,s.jsx)(f.Xi,{value:"api",children:"API Settings"})]}),(0,s.jsx)(f.av,{value:"account",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Account Information"}),(0,s.jsx)(n.BT,{children:"Update your account details"})]}),(0,s.jsx)(n.Wu,{className:"space-y-4",children:E?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(v.E,{className:"h-4 w-20"}),(0,s.jsx)(v.E,{className:"h-10 w-full"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(v.E,{className:"h-4 w-20"}),(0,s.jsx)(v.E,{className:"h-10 w-full"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(v.E,{className:"h-4 w-20"}),(0,s.jsx)(v.E,{className:"h-10 w-full"})]})]}):k?(0,s.jsx)("div",{className:"text-red-500 p-4 border border-red-200 rounded-md bg-red-50 dark:bg-red-900/20 dark:border-red-800",children:k}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,s.jsx)(p.J,{htmlFor:"name",children:"Nombre de Usuario"})]}),(0,s.jsx)(h.p,{id:"name",placeholder:"Tu nombre",value:(null==a||null==(e=a.email)?void 0:e.split("@")[0])||"",disabled:!0,className:"bg-gray-50 dark:bg-gray-800"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"El nombre de usuario se deriva de tu email."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,s.jsx)(p.J,{htmlFor:"email",children:"Email"})]}),(0,s.jsx)(h.p,{id:"email",type:"email",placeholder:"Tu email",value:(null==a?void 0:a.email)||"",disabled:!0,className:"bg-gray-50 dark:bg-gray-800"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Tu email de inicio de sesi\xf3n."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,s.jsx)(p.J,{htmlFor:"company",children:"Nombre de la Cuenta"})]}),(0,s.jsx)(h.p,{id:"company",placeholder:"Tu empresa",value:(null==A?void 0:A.name)||"",disabled:!0,className:"bg-gray-50 dark:bg-gray-800"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"El nombre de tu cuenta o empresa."})]})]})}),(0,s.jsx)(n.wL,{children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(i.Button,{asChild:!0,variant:"outline",children:(0,s.jsxs)("a",{href:"https://docs.rayuela.ai/account/change-password",target:"_blank",rel:"noopener noreferrer",className:"flex items-center",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Cambiar Contrase\xf1a"]})}),(0,s.jsx)(i.Button,{variant:"outline",onClick:()=>{try{throw{status:429,error_code:"RESOURCE_LIMIT_EXCEEDED",message:"Has alcanzado el l\xedmite de recursos de tu plan"}}catch(e){(0,b.h)(e,"Error al guardar los cambios")}},className:"text-warning border-warning/30 hover:bg-warning-light hover:text-warning",children:"Simular Error"})]})})]})}),(0,s.jsx)(f.av,{value:"notifications",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Notification Preferences"}),(0,s.jsx)(n.BT,{children:"Configure how you receive notifications"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 px-4 text-center",children:[(0,s.jsx)(m.A,{className:"h-12 w-12 text-gray-300 dark:text-gray-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-2",children:"Configuraci\xf3n de notificaciones disponible pr\xf3ximamente"}),(0,s.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-500",children:"Podr\xe1s configurar notificaciones por email, SMS y webhooks para eventos importantes como entrenamientos completados, l\xedmites de uso cercanos, etc."}),(0,s.jsxs)("div",{className:"mt-4 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",children:[(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Disponible en Q4 2023"]})]})})]})}),(0,s.jsx)(f.av,{value:"api",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"API Settings"}),(0,s.jsx)(n.BT,{children:"Configure API behavior and defaults"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 px-4 text-center",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-300 dark:text-gray-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-2",children:"Configuraci\xf3n de API disponible pr\xf3ximamente"}),(0,s.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-500",children:"Podr\xe1s configurar comportamientos por defecto de la API, webhooks, l\xedmites personalizados, regiones preferidas, etc."}),(0,s.jsxs)("div",{className:"mt-4 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",children:[(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Disponible en Q1 2024"]})]})})]})})]})]})}},5057:(e,a,r)=>{"use strict";r.d(a,{J:()=>i});var s=r(5155);r(2115);var t=r(968),n=r(9434);function i(e){let{className:a,...r}=e;return(0,s.jsx)(t.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...r})}},6695:(e,a,r)=>{"use strict";r.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var s=r(5155);r(2115);var t=r(9434);function n(e){var a;let{className:r,elevation:n="soft",...i}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border",null!=(a=({none:"shadow-none",sm:"shadow-sm",soft:"shadow-soft",medium:"shadow-medium",glow:"shadow-glow"})[n])?a:"shadow-soft","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",r),...i})}function i(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function l(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("text-subheading rayuela-accent",a),...r})}function o(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-caption",a),...r})}function d(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",a),...r})}function c(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,t.cn)("flex items-center px-6 [.border-t]:pt-6",a),...r})}},7313:(e,a,r)=>{"use strict";r.d(a,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>l});var s=r(5155),t=r(2115),n=r(6176),i=r(9434);let l=n.bL,o=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,s.jsx)(n.B8,{ref:a,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",r),...t})});o.displayName=n.B8.displayName;let d=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,s.jsx)(n.l9,{ref:a,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...t})});d.displayName=n.l9.displayName;let c=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,s.jsx)(n.UC,{ref:a,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...t})});c.displayName=n.UC.displayName},7588:(e,a,r)=>{"use strict";r.d(a,{h:()=>c});var s=r(6671),t=r(3464);class n extends Error{static isApiError(e){return e instanceof n}static fromResponse(e){return new n(e.message,e.status_code,e.error_code,e.details)}constructor(e,a,r,s){super(e),this.status=a,this.errorCode=r,this.details=s,this.name="ApiError"}}let i=t.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{{var a,r,s;let t=localStorage.getItem("rayuela-token"),n=localStorage.getItem("rayuela-apiKey");e.headers=null!=(s=e.headers)?s:{},t&&(e.headers.Authorization="Bearer ".concat(t)),!n||(null==(a=e.url)?void 0:a.includes("/auth/token"))||(null==(r=e.url)?void 0:r.includes("/auth/register"))||(e.headers["X-API-Key"]=n)}return e}),i.interceptors.response.use(e=>e,e=>{if(e.response){let a=e.response.data;throw n.fromResponse(a)}if(e.request)throw new n("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new n(e.message,0,"REQUEST_ERROR",null)});var l=r(6874),o=r.n(l),d=r(2115);function c(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Ha ocurrido un error";return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof n)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void s.o.error(d.createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",d.createElement(o(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void s.o.error(d.createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",d.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void s.o.error(d.createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",d.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void s.o.error(d.createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",d.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void s.o.error(d.createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",d.createElement(o(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void s.o.error(d.createElement("div",{},"Error de validacion: "+e.message+". ",d.createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void s.o.error(d.createElement("div",{},"Datos insuficientes para generar recomendaciones. ",d.createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void s.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(s.o.error(e.message||a),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void s.o.error(e.message||a):void(s.o.error(a),console.groupEnd())}},8856:(e,a,r)=>{"use strict";r.d(a,{E:()=>n});var s=r(5155),t=r(9434);function n(e){let{className:a,...r}=e;return(0,s.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-lg bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-shimmer",a),...r})}},9300:(e,a,r)=>{Promise.resolve().then(r.bind(r,1792))}},e=>{var a=a=>e(e.s=a);e.O(0,[9352,6874,1445,5674,3753,8034,6755,2092,3999,8441,1684,7358],()=>a(9300)),_N_E=e.O()}]);