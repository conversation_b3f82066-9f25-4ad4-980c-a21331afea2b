(()=>{var e={};e.id=9747,e.ids=[9747],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51011:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d={children:["",{children:["(dashboard)",{children:["pipeline",{children:["ingestion-jobs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53697)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/pipeline/ingestion-jobs/page",pathname:"/pipeline/ingestion-jobs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},53697:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\pipeline\\\\ingestion-jobs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61611:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64680:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>L});var r=t(60687),a=t(43210),i=t(44493),n=t(29523),o=t(85726),l=t(6211),d=t(91821),c=t(16023),x=t(61611),u=t(93613),m=t(80462),h=t(99270),p=t(13861),j=t(31158),g=t(13943),f=t(85650),v=t(41585),N=t(89667),b=t(80013),_=t(15079),y=t(63503),w=t(89571),A=t(62796),S=t(5336);let C=(0,t(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var E=t(41862);function P({onIngestionStart:e,trigger:s}){let[t,i]=(0,a.useState)(!1),[o,l]=(0,a.useState)("batch"),[x,m]=(0,a.useState)(null),[h,p]=(0,a.useState)(!1),[j,g]=(0,a.useState)(null),[f,v]=(0,a.useState)(!1),w=async()=>{if(!x)return void g("Por favor selecciona un archivo");p(!0),g(null);try{let s,t=await A(x);try{s=x.name.toLowerCase().endsWith(".json")?JSON.parse(t):x.name.toLowerCase().endsWith(".csv")?await P(t,o):JSON.parse(t)}catch{throw Error("Error al parsear el archivo. Aseg\xfarate de que el formato sea v\xe1lido.")}let r={data_type:o,file_name:x.name,file_size:x.size,...s},a=await e(r);console.log("Ingestion started:",a),v(!0),setTimeout(()=>{i(!1),v(!1),m(null),l("batch")},2e3)}catch(e){g(e instanceof Error?e.message:"Error iniciando ingesta de datos")}finally{p(!1)}},A=e=>new Promise((s,t)=>{let r=new FileReader;r.onload=e=>s(e.target?.result),r.onerror=()=>t(Error("Error reading file")),r.readAsText(e)}),P=async(e,s)=>{let t=e.trim().split("\n"),r=t[0].split(",").map(e=>e.trim()),a=[];for(let e=1;e<t.length;e++){let s=t[e].split(",").map(e=>e.trim()),i={};r.forEach((e,t)=>{i[e]=s[t]}),a.push(i)}switch(s){case"users":return{users:a};case"products":return{products:a};case"interactions":return{interactions:a};case"batch":return{users:a.filter(e=>e.external_id&&!e.product_id),products:a.filter(e=>e.external_id&&e.name&&!e.end_user_external_id),interactions:a.filter(e=>e.end_user_external_id&&e.product_external_id)};default:return{data:a}}};return(0,r.jsxs)(y.lG,{open:t,onOpenChange:i,children:[(0,r.jsx)(y.zM,{asChild:!0,children:s||(0,r.jsxs)(n.Button,{children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})}),(0,r.jsxs)(y.Cf,{className:"sm:max-w-md",children:[(0,r.jsxs)(y.c7,{children:[(0,r.jsx)(y.L3,{children:"Nueva Ingesta de Datos"}),(0,r.jsx)(y.rr,{children:"Sube un archivo CSV o JSON con tus datos de usuarios, productos o interacciones"})]}),f?(0,r.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,r.jsx)(S.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Ingesta iniciada!"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu archivo est\xe1 siendo procesado"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{htmlFor:"dataType",children:"Tipo de datos"}),(0,r.jsxs)(_.l6,{value:o,onValueChange:e=>l(e),children:[(0,r.jsx)(_.bq,{children:(0,r.jsx)(_.yv,{placeholder:"Selecciona el tipo de datos"})}),(0,r.jsxs)(_.gC,{children:[(0,r.jsx)(_.eb,{value:"batch",children:"Lote completo (usuarios, productos, interacciones)"}),(0,r.jsx)(_.eb,{value:"users",children:"Solo usuarios"}),(0,r.jsx)(_.eb,{value:"products",children:"Solo productos"}),(0,r.jsx)(_.eb,{value:"interactions",children:"Solo interacciones"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{htmlFor:"file",children:"Archivo"}),(0,r.jsx)(N.p,{id:"file",type:"file",accept:".csv,.json,.txt",onChange:e=>{let s=e.target.files?.[0];if(s){let e=s.name.toLowerCase().substr(s.name.lastIndexOf("."));if(!["text/csv","application/json","text/plain"].includes(s.type)&&![".csv",".json",".txt"].includes(e))return void g("Por favor selecciona un archivo CSV o JSON v\xe1lido");if(s.size>0xa00000)return void g("El archivo es demasiado grande. M\xe1ximo 10MB permitido");m(s),g(null)}},disabled:h}),x&&(0,r.jsxs)("div",{className:"mt-2 flex items-center text-sm text-muted-foreground",children:[(0,r.jsx)(C,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:[x.name," (",(x.size/1024).toFixed(1)," KB)"]})]})]}),j&&(0,r.jsxs)(d.Fc,{variant:"destructive",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)(d.TN,{children:j})]}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)("p",{children:"Formatos soportados: CSV, JSON"}),(0,r.jsx)("p",{children:"Tama\xf1o m\xe1ximo: 10MB"})]})]}),(0,r.jsxs)(y.Es,{children:[(0,r.jsx)(n.Button,{variant:"outline",onClick:()=>{h||(i(!1),m(null),g(null),v(!1),l("batch"))},disabled:h,children:"Cancelar"}),!f&&(0,r.jsx)(n.Button,{onClick:w,disabled:!x||h,children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(E.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Subiendo..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Iniciar Ingesta"]})})]})]})]})}var k=t(13668),I=t(58133);function L(){let{jobs:e,isLoading:s,error:t,startBatchIngestion:S}=(0,w.o)(),[C,E]=(0,a.useState)(null),{filteredJobs:L,searchQuery:q,setSearchQuery:z,statusFilter:D,setStatusFilter:F,clearFilters:T}=(0,A.x)(e,(e,s)=>e.job_id.toString().includes(s)||(e.file_path?.toLowerCase().includes(s.toLowerCase())??!1)),J=e=>(0,k.z3)(e),M=e=>"FAILED"===e.status,B=e=>{console.log("Retrying job:",e)},O=e=>{console.log("Downloading file:",e)};return s?(0,r.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,r.jsx)(o.E,{className:"h-8 w-64 mb-2"}),(0,r.jsx)(o.E,{className:"h-4 w-96"})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(o.E,{className:"h-6 w-48"}),(0,r.jsx)(o.E,{className:"h-4 w-32"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)(o.E,{className:"h-64 w-full"})})]})]}):(0,r.jsxs)(I.hI,{title:"Historial de Ingesta de Datos",description:"Seguimiento completo de todos tus procesos de carga de datos",actions:(0,r.jsx)(P,{onIngestionStart:S,trigger:(0,r.jsxs)(n.Button,{children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})}),children:[(0,r.jsx)(I.os,{title:"Resumen",icon:(0,r.jsx)(x.A,{className:"h-6 w-6 text-green-500"}),children:(0,r.jsxs)("div",{className:"flex gap-4 text-sm text-muted-foreground p-6",children:[(0,r.jsxs)("span",{children:["Total: ",e.length]}),(0,r.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,r.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,r.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})}),t&&(0,r.jsxs)(d.Fc,{variant:"destructive",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)(d.XL,{children:"Error"}),(0,r.jsx)(d.TN,{children:t})]}),(0,r.jsx)(I.os,{title:"Filtros",icon:(0,r.jsx)(m.A,{className:"h-5 w-5"}),children:(0,r.jsx)(i.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col gap-6 sm:flex-row sm:items-center sm:gap-6",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(N.p,{placeholder:"Buscar por ID o archivo...",value:q,onChange:e=>z(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,r.jsx)(b.J,{htmlFor:"statusFilter",className:"text-sm whitespace-nowrap",children:"Estado:"}),(0,r.jsxs)(_.l6,{value:D,onValueChange:e=>F(e),children:[(0,r.jsx)(_.bq,{className:"w-32",children:(0,r.jsx)(_.yv,{})}),(0,r.jsxs)(_.gC,{children:[(0,r.jsx)(_.eb,{value:"all",children:"Todos"}),(0,r.jsx)(_.eb,{value:"pending",children:"Pendiente"}),(0,r.jsx)(_.eb,{value:"processing",children:"Procesando"}),(0,r.jsx)(_.eb,{value:"completed",children:"Completado"}),(0,r.jsx)(_.eb,{value:"failed",children:"Fallido"})]})]})]}),(0,r.jsx)(n.Button,{variant:"outline",size:"sm",onClick:T,children:"Limpiar"})]})})}),(0,r.jsx)(I.os,{title:"Trabajos de Ingesta",description:"Lista completa de procesos de carga de datos con detalles y estad\xedsticas",children:(0,r.jsx)(i.Wu,{className:"p-0",children:(0,r.jsx)("div",{className:"overflow-hidden",children:(0,r.jsxs)(l.XI,{children:[(0,r.jsx)(l.A0,{className:"bg-muted/10",children:(0,r.jsxs)(l.Hj,{className:"border-b border-border/30",children:[(0,r.jsx)(l.nd,{className:"font-semibold",children:"Job ID"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Estado"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Registros Procesados"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Archivo"}),(0,r.jsx)(l.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,r.jsx)(l.BF,{children:L.length>0?L.map((e,s)=>(0,r.jsxs)(I.AP,{index:s,children:[(0,r.jsxs)(l.nA,{className:"font-medium py-4",children:["#",e.job_id]}),(0,r.jsx)(l.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,k.cR)(e.status),(0,k.KC)(e.status)]})}),(0,r.jsx)(l.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{children:(0,f.GP)(new Date(e.created_at),"dd/MM/yyyy",{locale:v.es})}),(0,r.jsx)("div",{className:"text-muted-foreground",children:(0,f.GP)(new Date(e.created_at),"HH:mm",{locale:v.es})})]})}),(0,r.jsx)(l.nA,{className:"py-4",children:e.duration?(0,r.jsx)("span",{className:"text-sm font-medium",children:(0,k.a3)(e.duration)}):"PROCESSING"===e.status?(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"En curso"}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(l.nA,{className:"py-4",children:e.records_processed?(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{className:"font-medium",children:["Total: ",e.records_processed.total?.toLocaleString()||"—"]}),(0,r.jsxs)("div",{className:"text-muted-foreground text-xs",children:[e.records_processed.users&&`${e.records_processed.users.toLocaleString()} usuarios`,e.records_processed.products&&`, ${e.records_processed.products.toLocaleString()} productos`,e.records_processed.interactions&&`, ${e.records_processed.interactions.toLocaleString()} interacciones`]})]}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(l.nA,{className:"py-4",children:e.file_path?(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium truncate max-w-32",title:e.file_path,children:e.file_path.split("/").pop()}),e.file_size&&(0,r.jsx)("div",{className:"text-muted-foreground text-xs",children:J(e.file_size)})]}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(l.nA,{className:"text-right py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,r.jsxs)(y.lG,{children:[(0,r.jsx)(y.zM,{asChild:!0,children:(0,r.jsx)(n.Button,{variant:"ghost",size:"sm",onClick:()=>E(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(y.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(y.c7,{children:[(0,r.jsxs)(y.L3,{children:["Detalles del Job #",e.job_id]}),(0,r.jsx)(y.rr,{children:"Informaci\xf3n completa del trabajo de ingesta de datos"})]}),C&&(0,r.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Estado"}),(0,r.jsx)("div",{className:"mt-1",children:(0,k.KC)(C.status)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Duraci\xf3n"}),(0,r.jsx)("p",{className:"text-sm",children:C.duration?(0,k.a3)(C.duration):"En curso"})]})]}),C.file_path&&(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Archivo"}),(0,r.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,r.jsx)("code",{className:"text-xs bg-muted p-2 rounded flex-1",children:C.file_path}),(0,r.jsx)(n.Button,{size:"sm",variant:"outline",onClick:()=>O(C.file_path),disabled:!0,children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})]}),C.file_size&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Tama\xf1o: ",J(C.file_size)]})]}),C.records_processed&&(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Registros Procesados"}),(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-2 mt-2",children:[C.records_processed.users&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Usuarios"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:C.records_processed.users.toLocaleString()})]}),C.records_processed.products&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Productos"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:C.records_processed.products.toLocaleString()})]}),C.records_processed.interactions&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Interacciones"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:C.records_processed.interactions.toLocaleString()})]}),C.records_processed.total&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Total"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:C.records_processed.total.toLocaleString()})]})]})]}),C.error_message&&(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,r.jsxs)(d.Fc,{variant:"destructive",className:"mt-1",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)(d.TN,{className:"text-sm",children:C.error_message})]})]}),C.task_id&&(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Task ID"}),(0,r.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:C.task_id})]})]})]})]}),M(e)&&(0,r.jsx)(n.Button,{variant:"ghost",size:"sm",onClick:()=>B(e.job_id),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})})]},e.job_id)):(0,r.jsx)(I.AP,{index:0,children:(0,r.jsx)(l.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"h-8 w-8"}),(0,r.jsx)("p",{children:"No hay trabajos de ingesta a\xfan"}),(0,r.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando subas datos"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"h-8 w-8"}),(0,r.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,r.jsx)(n.Button,{variant:"outline",size:"sm",onClick:T,children:"Limpiar filtros"})]})})})})})]})})})}),(0,r.jsxs)(d.Fc,{children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)(d.XL,{children:"Informaci\xf3n sobre ingesta de datos"}),(0,r.jsx)(d.TN,{children:(0,r.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,r.jsx)("p",{children:"Los trabajos de ingesta procesan archivos de datos (CSV, JSON) para actualizar usuarios, productos e interacciones en el sistema."}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Formatos soportados:"})," CSV, JSON"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Tipos de datos:"})," Usuarios, Productos, Interacciones"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Validaci\xf3n:"})," Se valida formato y campos obligatorios"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Procesamiento:"})," Los datos se procesan de forma as\xedncrona"]})]})]})})]})]})}},67428:(e,s,t)=>{Promise.resolve().then(t.bind(t,64680))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85988:(e,s,t)=>{Promise.resolve().then(t.bind(t,53697))},89571:(e,s,t)=>{"use strict";t.d(s,{o:()=>i});var r=t(43210),a=t(81184);function i(){let[e,s]=(0,r.useState)([]),[t,i]=(0,r.useState)(!0),[n,o]=(0,r.useState)(null),l=async()=>{try{i(!0),o(null);try{let e=[].map(e=>{let s={...e,status:e.status.toUpperCase(),records_processed:e.processed_count?{users:e.processed_count.users,products:e.processed_count.products,interactions:e.processed_count.interactions,total:e.processed_count.total}:void 0};if(s.started_at&&s.completed_at){let e=new Date(s.started_at).getTime(),t=new Date(s.completed_at).getTime();s.duration=Math.round((t-e)/1e3)}return s});s(e);return}catch(e){o("Error fetching ingestion jobs"),console.error("Error fetching ingestion jobs:",e)}}catch(e){o(e instanceof Error?e.message:"Error loading ingestion jobs"),console.error("Error loading ingestion jobs:",e)}finally{i(!1)}},d=async e=>{try{let s=await (0,a._C)().batchDataIngestionApiV1IngestionBatchPost(e);return await l(),s.data}catch(e){throw console.error("Error starting batch ingestion:",e),e}};return{jobs:e,isLoading:t,error:n,fetchJobs:l,getJobStatus:async e=>{try{return(await (0,a._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data}catch(e){throw console.error("Error fetching job status:",e),e}},startBatchIngestion:d}}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,2713,5814,5423,1576,7400,6920,2807,5320,3302],()=>t(51011));module.exports=r})();