"use strict";exports.id=3525,exports.ids=[3525],exports.modules={2735:(e,t,r)=>{r.d(t,{G:()=>i});var a=r(43210),n=r(81184);function i(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[s,o]=(0,a.useState)(null);return{models:e,isLoading:r,error:s,fetchModels:async()=>{try{i(!0),o(null);let e=(await (0,n._C)().listModelsApiV1PipelineModelsGet()).data.map(e=>({id:e.id,artifact_name:e.artifact_name,artifact_version:e.artifact_version,description:e.description||void 0,training_date:e.training_date,performance_metrics:e.performance_metrics||void 0,parameters:e.parameters||void 0}));t(e)}catch(e){o(e instanceof Error?e.message:"Error loading models"),console.error("Error loading models:",e),t([])}finally{i(!1)}},getModelMetrics:async e=>{try{return(await (0,n._C)().getModelMetricsApiV1PipelineModelsModelIdMetricsGet(e)).data}catch(e){throw console.error("Error fetching model metrics:",e),e}}}}},25475:(e,t,r)=>{r.d(t,{G:()=>i});var a=r(43210),n=r(81184);function i(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[s,o]=(0,a.useState)(null),l=async()=>{try{i(!0),o(null);try{let e=[].map(e=>{let t={...e,model_name:e.model?.artifact_name??"Recommendation Model",model_version:e.model?.artifact_version??"v1.0",status:e.status.toUpperCase(),parameters:e.parameters?Object.fromEntries(Object.entries(e.parameters).filter(([,e])=>"number"==typeof e||"string"==typeof e)):void 0,metrics:e.metrics?Object.fromEntries(Object.entries(e.metrics).filter(([,e])=>"number"==typeof e)):void 0};if(t.started_at&&t.completed_at){let e=new Date(t.started_at).getTime(),r=new Date(t.completed_at).getTime();t.duration=Math.round((r-e)/1e3)}return t});t(e);return}catch(e){o("Error fetching training jobs"),console.error("Error fetching training jobs:",e)}}catch(e){o(e instanceof Error?e.message:"Error loading training jobs"),console.error("Error loading training jobs:",e)}finally{i(!1)}},c=async e=>{try{let e=await (0,n._C)().trainModelsApiV1PipelineTrainPost();return await l(),e.data}catch(e){throw console.error("Error starting training:",e),e}};return{jobs:e,isLoading:r,error:s,fetchJobs:l,getJobStatus:async e=>{try{return(await (0,n._C)().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(e)).data}catch(e){throw console.error("Error fetching training job status:",e),e}},startTraining:c}}},55695:(e,t,r)=>{r.d(t,{K:()=>o,S:()=>s});var a=r(81184),n=r(62185);let i=(0,a._C)();async function s(e,t){try{let r={};return e&&(r.model_id=e),t&&(r.metric_type=t),await i.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(r)}catch(a){let e=a instanceof Error?a.message:"Error al obtener m\xe9tricas de rendimiento de recomendaciones",t=a.status||500,r=a.body;throw new n.hD(e,t,r)}}async function o(){try{return await i.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet()}catch(a){let e=a instanceof Error?a.message:"Error al obtener m\xe9tricas de confianza",t=a.status||500,r=a.body;throw new n.hD(e,t,r)}}},56184:(e,t,r)=>{r.d(t,{Q:()=>s});var a=r(5077),n=r(62185),i=r(43210);function s(e={}){let[t,r]=(0,i.useState)(!1),[o,l]=(0,i.useState)(!1),[c,u]=(0,i.useState)(!1),[d,g]=(0,i.useState)(!1),[p,f]=(0,i.useState)(null),{data:m,error:h,isLoading:y,isValidating:b,mutate:v}=(0,a.Ay)("api-keys",async()=>await (0,n.PX)(),{revalidateOnFocus:e.revalidateOnFocus??!0,refreshInterval:e.refreshInterval,dedupingInterval:e.dedupingInterval??6e4,errorRetryCount:e.errorRetryCount??3,onError:e=>{console.error("Error fetching API keys:",e)}}),w=m?.api_keys&&m.api_keys.length>0?m.api_keys.find(e=>e.is_active)||m.api_keys[0]:null,A=async e=>{r(!0),f(null);try{let t={name:e.name||"",permissions:[]},r=await (0,n.Iq)(t);return await v(),r}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al crear API Key",500);throw f(e),e}finally{r(!1)}},_=async(e,t)=>{l(!0),f(null);try{let r={name:t.name||void 0,permissions:[]},a=await (0,n.XW)(e.toString(),r);return await v(),a}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al actualizar API Key",500);throw f(e),e}finally{l(!1)}},C=async e=>{u(!0),f(null);try{return await (0,n.mA)(e),await v(),!0}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al revocar API Key",500);throw f(e),e}finally{u(!1)}},S=async()=>{g(!0),f(null);try{let e=await (0,n.Iq)({name:`API Key ${new Date().toLocaleDateString("es-ES")}`});return await v(),e}catch(e){return f(e instanceof n.hD?e:new n.hD("Error al regenerar API Key",500)),null}finally{g(!1)}};return{data:m??null,primaryKey:w??null,error:h??null,isLoading:y,isValidating:b,mutate:v,dataUpdatedAt:0,createApiKey:A,updateApiKey:_,revokeApiKey:C,regenerateApiKey:S,isCreating:t,isUpdating:o,isRevoking:c,isRegenerating:d,operationError:p,getFormattedApiKey:e=>{let t=e||w;return t?.prefix&&t?.last_chars?`${t.prefix}••••••••${t.last_chars}`:null}}}},62244:(e,t,r)=>{r.d(t,{T4:()=>o,Qf:()=>l.Q,As:()=>i.A,A$:()=>g,Xn:()=>d,TB:()=>c}),r(25475),r(89571),r(2735);var a=r(5077),n=r(62185),i=r(44957),s=r(81184);function o(e={}){let{token:t,apiKey:r}=(0,i.A)(),{data:l,error:c,isLoading:u,isValidating:d,mutate:g}=(0,a.Ay)(t&&r?["account-info",t,r]:null,async()=>await (0,n.Dm)(),{revalidateOnFocus:e.revalidateOnFocus??!1,refreshInterval:e.refreshInterval,dedupingInterval:e.dedupingInterval??6e4,errorRetryCount:e.errorRetryCount??3}),p=async e=>{if(!t||!r)throw Error("No token or API key available");try{let t=(await (0,s._C)().patchCurrentAccountApiV1AccountsCurrentPatch({onboardingChecklistStatus:e})).data;return await g(t,{revalidate:!1}),t}catch(r){console.error("Error updating checklist status:",r);let t=l?{...l,onboardingChecklistStatus:e}:void 0;throw await g(t,{revalidate:!1}),r}};return{accountData:l,error:c,isLoading:u,isValidating:d,refresh:g,lastUpdated:null,getCreationDate:()=>l&&l.createdAt?new Date(l.createdAt):null,isActive:()=>!!l&&l.isActive,getSubscriptionPlan:()=>l&&l.subscription?l.subscription.plan:null,isSubscriptionActive:()=>!!l&&!!l.subscription&&l.subscription.isActive,getSubscriptionExpiryDate:()=>l&&l.subscription&&l.subscription.expiresAt?new Date(l.subscription.expiresAt):null,getChecklistStatus:()=>l&&l.onboardingChecklistStatus?l.onboardingChecklistStatus:{},updateChecklistStatus:p}}var l=r(56184);function c(){let{token:e,apiKey:t}=(0,i.A)(),{data:r,error:s,isLoading:o,mutate:l}=(0,a.Ay)(e&&t?["usage-summary",e,t]:null,async()=>await (0,n.S3)(),{refreshInterval:3e4,revalidateOnFocus:!0}),c=()=>r&&r.apiCalls?.percentage||0,u=()=>r&&r.storage?.percentage||0;return{usageData:r,error:s,isLoading:o,mutate:l,getApiCallsUsed:()=>r?.apiCalls?.used||0,getApiCallsLimit:()=>r?.apiCalls?.limit||0,getApiCallsRemaining:()=>{let e=r?.apiCalls?.used||0;return Math.max(0,(r?.apiCalls?.limit||0)-e)},getStorageUsed:()=>r?.storage?.usedBytes||0,getStorageLimit:()=>r?.storage?.limitBytes||0,getStorageRemaining:()=>{let e=r?.storage?.usedBytes||0;return Math.max(0,(r?.storage?.limitBytes||0)-e)},hasUsageActivity:()=>(r?.apiCalls?.used||0)>0,getApiCallsPercentage:c,getStoragePercentage:u,canTrainNow:()=>!!r&&(r.training?.canTrainNow||!1),getNextTrainingDate:()=>r&&r.training?.nextAvailable?new Date(r.training.nextAvailable):null,getLastStorageMeasurement:()=>r&&r.storage?.lastMeasured||null,getNextApiCallsReset:()=>r&&r.apiCalls?.resetDate?new Date(r.apiCalls.resetDate):null,isApiCallsLimitReached:()=>!!r&&c()>=100,isStorageLimitReached:()=>!!r&&u()>=100,getStorageUsedFormatted:()=>{if(!r)return"0 B";let e=r.storage?.usedBytes||0;if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB","TB"][t]},getStorageLimitFormatted:()=>{if(!r)return"0 B";let e=r.storage?.limitBytes||0,t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB","TB"][t]},getApiCallsUsedFormatted:()=>r?(r.apiCalls?.used||0).toLocaleString():"0",getApiCallsLimitFormatted:()=>r?(r.apiCalls?.limit||0).toLocaleString():"0",getAvailableModels:()=>r&&r.planLimits?.availableModels||[],getMaxRequestsPerMinute:()=>r&&r.planLimits?.maxRequestsPerMinute||0}}function u(e){return!!e&&"object"==typeof e&&"string"==typeof e.date&&"number"==typeof e.api_calls&&"number"==typeof e.storage}function d(e,t){let{token:r,apiKey:s}=(0,i.A)(),o=e?.toISOString().split("T")[0],l=t?.toISOString().split("T")[0],{data:c,error:d,isLoading:g,mutate:p}=(0,a.Ay)(r&&s&&o&&l?["usage-history",r,s,o,l]:null,async()=>await (0,n.M2)(),{refreshInterval:6e4,revalidateOnFocus:!0,onError:e=>{e instanceof n.hD?console.error("Error fetching usage history:",e.message,e.body):console.error("Unexpected error:",e)}});return{data:c,error:d,isLoading:g,mutate:p,getTotalApiCalls:()=>c?c.reduce((e,t)=>u(t)?e+t.api_calls:e,0):0,getPeakUsageDay:()=>{if(!c||0===c.length)return null;let e=c.filter(u);if(0===e.length)return null;let t=e.reduce((e,t)=>t.api_calls>e.api_calls?t:e,e[0]);return{date:new Date(t.date),apiCalls:t.api_calls}},getLatestStorageUsage:()=>{if(!c||0===c.length)return 0;let e=c.filter(u);return 0===e.length?0:e.sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())[0].storage},getChartData:()=>c?c.filter(u).sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()).map(e=>({date:new Date(e.date),apiCalls:e.api_calls,storage:e.storage})):[],getGrowthRate:e=>{if(!c||c.length<2)return 0;let t=c.filter(u);if(t.length<2)return 0;let r=t.sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()),a="apiCalls"===e?r[0].api_calls:r[0].storage,n="apiCalls"===e?r[r.length-1].api_calls:r[r.length-1].storage;return 0===a?100*(n>0):(n-a)/a*100}}}r(55695),r(43210);function g(){let{token:e,apiKey:t}=(0,i.A)(),{data:r,error:s,isLoading:o,mutate:l}=(0,a.Ay)(e&&t?["plans",e,t]:null,async()=>await (0,n.T9)(),{refreshInterval:3e5,revalidateOnFocus:!1});return{plans:r||{},error:s,isLoading:o,refresh:l,getPlanLimits:e=>{if(!r)return null;let t=r[e];return t?.limits||null},getPlanById:e=>r&&r[e]||null,getAllPlans:()=>r?Object.values(r):[],getPlanName:e=>{if(!r)return e;let t=r[e];return t?.name||e}}}r(62796)},62796:(e,t,r)=>{r.d(t,{x:()=>n});var a=r(43210);function n(e,t){let[r,n]=(0,a.useState)(""),[i,s]=(0,a.useState)("all");return{filteredJobs:(0,a.useMemo)(()=>e.filter(e=>{let a="all"===i||e.status.toLowerCase()===i,n=""===r||t(e,r);return a&&n}),[e,i,r,t]),searchQuery:r,setSearchQuery:n,statusFilter:i,setStatusFilter:s,clearFilters:()=>{n(""),s("all")}}}},89571:(e,t,r)=>{r.d(t,{o:()=>i});var a=r(43210),n=r(81184);function i(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[s,o]=(0,a.useState)(null),l=async()=>{try{i(!0),o(null);try{let e=[].map(e=>{let t={...e,status:e.status.toUpperCase(),records_processed:e.processed_count?{users:e.processed_count.users,products:e.processed_count.products,interactions:e.processed_count.interactions,total:e.processed_count.total}:void 0};if(t.started_at&&t.completed_at){let e=new Date(t.started_at).getTime(),r=new Date(t.completed_at).getTime();t.duration=Math.round((r-e)/1e3)}return t});t(e);return}catch(e){o("Error fetching ingestion jobs"),console.error("Error fetching ingestion jobs:",e)}}catch(e){o(e instanceof Error?e.message:"Error loading ingestion jobs"),console.error("Error loading ingestion jobs:",e)}finally{i(!1)}},c=async e=>{try{let t=await (0,n._C)().batchDataIngestionApiV1IngestionBatchPost(e);return await l(),t.data}catch(e){throw console.error("Error starting batch ingestion:",e),e}};return{jobs:e,isLoading:r,error:s,fetchJobs:l,getJobStatus:async e=>{try{return(await (0,n._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data}catch(e){throw console.error("Error fetching job status:",e),e}},startBatchIngestion:c}}},96834:(e,t,r)=>{r.d(t,{E:()=>o});var a=r(60687);r(43210);var n=r(24224),i=r(4780);let s=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,a.jsx)("div",{className:(0,i.cn)(s({variant:t}),e),...r})}}};