(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5598],{1214:(e,a,s)=>{Promise.resolve().then(s.bind(s,9407))},4944:(e,a,s)=>{"use strict";s.d(a,{k:()=>l});var t=s(5155),r=s(2115),n=s(4472),i=s(9434);let l=r.forwardRef((e,a)=>{let{className:s,value:r,...l}=e;return(0,t.jsx)(n.bL,{ref:a,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...l,children:(0,t.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});l.displayName=n.bL.displayName},6102:(e,a,s)=>{"use strict";s.d(a,{Bc:()=>i,ZI:()=>c,k$:()=>o,m_:()=>l});var t=s(5155);s(2115);var r=s(3815),n=s(9434);function i(e){let{delayDuration:a=0,...s}=e;return(0,t.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:a,...s})}function l(e){let{...a}=e;return(0,t.jsx)(i,{children:(0,t.jsx)(r.bL,{"data-slot":"tooltip",...a})})}function o(e){let{...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"tooltip-trigger",...a})}function c(e){let{className:a,sideOffset:s=0,children:i,...l}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:s,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",a),...l,children:[i,(0,t.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},7018:(e,a,s)=>{"use strict";s.d(a,{q:()=>m});var t=s(5155),r=s(2115),n=s(285),i=s(3999),l=s(5731),o=s(6671),c=s(4631),d=s(3786);function m(e){let{children:a,className:s,variant:m="outline",...u}=e,{token:x,apiKey:h}=(0,i.A)(),[f,p]=(0,r.useState)(!1),g=async()=>{if(!x||!h)return void o.o.error("Debes iniciar sesi\xf3n para realizar esta acci\xf3n");p(!0);try{let e=await (0,l.oE)();if(e.url)window.location.href=e.url;else throw Error("No se recibi\xf3 una URL de redirecci\xf3n")}catch(e){console.error("Error al crear sesi\xf3n del Portal de Facturaci\xf3n:",e),o.o.error(e.message||"Error al acceder al portal de facturaci\xf3n"),p(!1)}};return(0,t.jsx)(n.Button,{onClick:g,disabled:f,className:s,variant:m,...u,children:f?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Redirigiendo..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),a||"Gestionar Facturaci\xf3n"]})})}},7313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>l});var t=s(5155),r=s(2115),n=s(1414),i=s(9434);let l=n.bL,o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.B8,{ref:a,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",s),...r})});o.displayName=n.B8.displayName;let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.l9,{ref:a,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});c.displayName=n.l9.displayName;let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.UC,{ref:a,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});d.displayName=n.UC.displayName},7588:(e,a,s)=>{"use strict";s.d(a,{h:()=>d});var t=s(6671),r=s(3464);class n extends Error{static isApiError(e){return e instanceof n}static fromResponse(e){return new n(e.message,e.status_code,e.error_code,e.details)}constructor(e,a,s,t){super(e),this.status=a,this.errorCode=s,this.details=t,this.name="ApiError"}}let i=r.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{{var a,s,t;let r=localStorage.getItem("rayuela-token"),n=localStorage.getItem("rayuela-apiKey");e.headers=null!=(t=e.headers)?t:{},r&&(e.headers.Authorization="Bearer ".concat(r)),!n||(null==(a=e.url)?void 0:a.includes("/auth/token"))||(null==(s=e.url)?void 0:s.includes("/auth/register"))||(e.headers["X-API-Key"]=n)}return e}),i.interceptors.response.use(e=>e,e=>{if(e.response){let a=e.response.data;throw n.fromResponse(a)}if(e.request)throw new n("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new n(e.message,0,"REQUEST_ERROR",null)});var l=s(6874),o=s.n(l),c=s(2115);function d(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Ha ocurrido un error";return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof n)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(c.createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",c.createElement(o(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(c.createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",c.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void t.o.error(c.createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",c.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void t.o.error(c.createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",c.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void t.o.error(c.createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",c.createElement(o(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void t.o.error(c.createElement("div",{},"Error de validacion: "+e.message+". ",c.createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void t.o.error(c.createElement("div",{},"Datos insuficientes para generar recomendaciones. ",c.createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void t.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(t.o.error(e.message||a),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void t.o.error(e.message||a):void(t.o.error(a),console.groupEnd())}},7916:(e,a,s)=>{"use strict";s.d(a,{s:()=>m});var t=s(5155),r=s(2115),n=s(285),i=s(3999),l=s(5731),o=s(6671),c=s(4631),d=s(1586);function m(e){let{priceId:a,planName:s,actionType:m="subscribe",children:u,className:x,variant:h="default",...f}=e,{token:p,apiKey:g}=(0,i.A)(),[j,v]=(0,r.useState)(!1),b=async()=>{if(!p||!g)return void o.o.error("Debes iniciar sesi\xf3n para realizar esta acci\xf3n");v(!0);try{if("contact"===m){window.location.href="/contact-sales";return}let e=await (0,l.fw)(a);if(e.url)window.location.href=e.url;else throw Error("No se recibi\xf3 una URL de redirecci\xf3n")}catch(e){console.error("Error al crear sesi\xf3n de checkout:",e),o.o.error(e.message||"Error al procesar la suscripci\xf3n al plan ".concat(s)),v(!1)}};return(0,t.jsx)(n.Button,{onClick:b,disabled:j,className:x,variant:h,...f,children:j?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Procesando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),u||("upgrade"===m?"Actualizar a ".concat(s):"downgrade"===m?"Cambiar a ".concat(s):"contact"===m?"Contactar con Ventas":"Suscribirse a ".concat(s))]})})}},8338:(e,a,s)=>{"use strict";s.d(a,{KC:()=>h,Yq:()=>f,ZV:()=>u,a3:()=>p,cR:()=>x,z3:()=>m});var t=s(5155);s(2115);var r=s(6126),n=s(646),i=s(3904),l=s(4186),o=s(4861),c=s(5690),d=s(1243);function m(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(a<0?0:a))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][s]}function u(e){return new Intl.NumberFormat().format(e)}function x(e){let a={className:"h-4 w-4"};switch(null==e?void 0:e.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(n.A,{...a,className:"h-4 w-4 text-green-500"});case"running":case"processing":case"in_progress":return(0,t.jsx)(i.A,{...a,className:"h-4 w-4 text-blue-500 animate-spin"});case"pending":case"queued":case"waiting":return(0,t.jsx)(l.A,{...a,className:"h-4 w-4 text-yellow-500"});case"failed":case"error":case"cancelled":return(0,t.jsx)(o.A,{...a,className:"h-4 w-4 text-red-500"});case"starting":case"initializing":return(0,t.jsx)(c.A,{...a,className:"h-4 w-4 text-blue-400"});case"warning":return(0,t.jsx)(d.A,{...a,className:"h-4 w-4 text-amber-500"});default:return(0,t.jsx)(l.A,{...a,className:"h-4 w-4 text-gray-400"})}}function h(e){switch(null==e?void 0:e.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(r.E,{variant:"success",className:"text-xs",children:"Completado"});case"running":case"processing":case"in_progress":return(0,t.jsx)(r.E,{variant:"info",className:"text-xs",children:"En progreso"});case"pending":case"queued":case"waiting":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Pendiente"});case"failed":case"error":case"cancelled":return(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"Fallido"});case"starting":case"initializing":return(0,t.jsx)(r.E,{variant:"secondary",className:"text-xs",children:"Iniciando"});case"warning":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Advertencia"});default:return(0,t.jsx)(r.E,{variant:"outline",className:"text-xs",children:"Desconocido"})}}function f(e){let a=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!e)return"No disponible";try{let s=new Date(e),t={year:"numeric",month:"long",day:"numeric",...a&&{hour:"2-digit",minute:"2-digit"}};return new Intl.DateTimeFormat("es-ES",t).format(s)}catch(e){return console.error("Error al formatear fecha:",e),"Formato de fecha inv\xe1lido"}}function p(e){let a=Math.floor(e/1e3),s=Math.floor(a/60),t=Math.floor(s/60),r=Math.floor(t/24);return r>0?"".concat(r,"d ").concat(t%24,"h ").concat(s%60,"m"):t>0?"".concat(t,"h ").concat(s%60,"m ").concat(a%60,"s"):s>0?"".concat(s,"m ").concat(a%60,"s"):"".concat(a,"s")}},8534:(e,a,s)=>{"use strict";s.d(a,{mm:()=>l,vK:()=>c});var t=s(5155);s(2115);var r=s(9434);let n={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",xl:"h-8 w-8","2xl":"h-12 w-12"},i={success:"text-success",warning:"text-warning",error:"text-destructive",info:"text-info",primary:"text-primary",secondary:"text-secondary-foreground",muted:"text-muted-foreground",interactive:"text-primary hover:text-primary/80",neutral:"text-foreground",subtle:"text-muted-foreground",metric:"text-primary",action:"text-primary",navigation:"text-muted-foreground hover:text-foreground"};function l(e){let{icon:a,size:s="md",context:l="neutral",className:o,"aria-label":c,"aria-hidden":d=!c,...m}=e;return(0,t.jsx)(a,{className:(0,r.cn)(n[s],i[l],"shrink-0",o),"aria-label":c,"aria-hidden":d,...m})}let o={tight:"gap-1",normal:"gap-2",loose:"gap-3"};function c(e){let{icon:a,children:s,size:n="sm",context:i="neutral",iconPosition:c="left",spacing:d="normal",className:m}=e,u=(0,t.jsx)(l,{icon:a,size:n,context:i,"aria-hidden":!0});return(0,t.jsxs)("span",{className:(0,r.cn)("inline-flex items-center",o[d],m),children:["left"===c&&u,s,"right"===c&&u]})}},9407:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>J});var t=s(5155),r=s(2115),n=s(9107),i=s(3008),l=s(8794),o=s(5379),c=s(6695),d=s(8856),m=s(285),u=s(4944),x=s(6126),h=s(5365),f=s(6671),p=s(7588),g=s(3904),j=s(5339),v=s(4186),b=s(2713),N=s(4213),y=s(4788),w=s(1284),E=s(3109),A=s(8534),z=s(7916),C=s(7018),_=s(2502),I=s(1516),M=s(7313);let B={blue:{primary:"rgb(59, 130, 246)",background:"rgba(59, 130, 246, 0.5)"},green:{primary:"rgb(16, 185, 129)",background:"rgba(16, 185, 129, 0.5)"},red:{primary:"rgb(239, 68, 68)",background:"rgba(239, 68, 68, 0.5)"}};function k(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return"".concat(e.toFixed(a),"%")}var R=s(8338),P=s(1243);function L(e){let{data:a=[],isLoading:s=!1,error:n=null,title:i="Uso de API",apiCallsLimit:l,storageLimit:o}=e,[m,u]=(0,r.useState)([]);(0,r.useEffect)(()=>{u(a&&a.length>0?a:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e&&0!==e.length?e.map(e=>({date:e.date,apiCalls:e.apiCalls,storage:e.storage})):function(){let e=[],a=new Date;for(let s=29;s>=0;s--){let t=new Date(a);t.setDate(t.getDate()-s),e.push({date:t.toISOString().split("T")[0],apiCalls:Math.floor(500*Math.random())+100,storage:Math.floor(1e7*Math.random())+1e6})}return e}()}([]))},[a]);let h={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{mode:"index",intersect:!1,callbacks:{title:e=>"Fecha: ".concat(e[0].label),label:e=>{let a=e.dataset.label||"";return a&&(a+=": "),null!==e.parsed.y&&(a.includes("Almacenamiento")?a+=(0,R.z3)(1024*e.parsed.y*1024):a+=(0,R.ZV)(e.parsed.y)),a},footer:e=>{let a=e[0].dataset.label||"";if(a.includes("Llamadas")&&l){let a=Math.min(e[0].parsed.y/l*100,100);return"".concat(k(a)," del l\xedmite (").concat((0,R.ZV)(l),")")}if(a.includes("Almacenamiento")&&o){let a=o/1048576,s=Math.min(e[0].parsed.y/a*100,100);return"".concat(k(s)," del l\xedmite (").concat((0,R.z3)(o),")")}return""}}}},scales:{y:{beginAtZero:!0,ticks:{font:{size:11},callback:function(e){return(0,R.ZV)(e)}}}}};return(m.map(e=>e.date.slice(5)),m.map(e=>e.apiCalls),B.blue.primary,B.blue.background,m.map(e=>e.date.slice(5)),m.map(e=>e.storage/1048576),B.green.primary,B.green.background,{...h,plugins:{...h.plugins,title:{display:!0,text:"Llamadas a la API por d\xeda",font:{size:13,weight:"normal"},padding:{top:10,bottom:10}},annotation:l?{annotations:{limitLine:{type:"line",yMin:l,yMax:l,borderColor:B.red.primary,borderWidth:2,borderDash:[6,6],label:{display:!0,content:"L\xedmite: ".concat((0,R.ZV)(l)),position:"end",backgroundColor:"rgba(239, 68, 68, 0.7)",font:{size:11}}}}}:void 0}},{...h,plugins:{...h.plugins,title:{display:!0,text:"Almacenamiento utilizado (MB)",font:{size:13,weight:"normal"},padding:{top:10,bottom:10}},annotation:o?{annotations:{limitLine:{type:"line",yMin:o/1048576,yMax:o/1048576,borderColor:B.red.primary,borderWidth:2,borderDash:[6,6],label:{display:!0,content:"L\xedmite: ".concat((0,R.z3)(o)),position:"end",backgroundColor:"rgba(239, 68, 68, 0.7)",font:{size:11}}}}}:void 0}},s)?(0,t.jsxs)(c.Zp,{className:"transition-all duration-300 hover:shadow-md",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:(0,t.jsx)(d.E,{className:"h-6 w-1/3"})}),(0,t.jsx)(c.BT,{children:(0,t.jsx)(d.E,{className:"h-4 w-1/2"})})]}),(0,t.jsx)(c.Wu,{className:"h-80",children:(0,t.jsx)(d.E,{className:"h-full w-full rounded-md"})})]}):n?(0,t.jsxs)(c.Zp,{className:"transition-all duration-300 hover:shadow-md border-red-200 dark:border-red-800",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"text-destructive flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-5 w-5"}),"Error al cargar datos"]}),(0,t.jsx)(c.BT,{className:"text-destructive/70",children:"No se pudieron cargar los datos de uso"})]}),(0,t.jsxs)(c.Wu,{className:"flex items-center justify-center py-8",children:[(0,t.jsx)("p",{className:"text-destructive mb-2",children:"Ocurri\xf3 un error al cargar los datos de uso."}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Intenta refrescar la p\xe1gina o contacta al soporte t\xe9cnico."})]})]}):(0,t.jsxs)(c.Zp,{className:"w-full",children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),i]}),(0,t.jsx)(c.BT,{children:"Monitoreo de uso en el rango seleccionado"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.E,{variant:"info",children:"Per\xedodo: \xdaltimos 30 d\xedas"}),m.some(e=>e.apiCalls>0)&&(0,t.jsx)(x.E,{variant:"success",children:"Datos disponibles"})]})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)(M.tU,{defaultValue:"chart",className:"w-full",children:(0,t.jsxs)(M.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(M.Xi,{value:"chart",className:"transition-all duration-200 data-[state=active]:bg-info/10 data-[state=active]:text-info",children:"Gr\xe1fico"}),(0,t.jsx)(M.Xi,{value:"table",className:"transition-all duration-200 data-[state=active]:bg-success/10 data-[state=active]:text-success",children:"Tabla"})]})})})]})}_.t1.register(_.PP,_.kc,_.FN,_.No,_.E8,_.hE,_.m_,_.s$,I.A);var D=s(2355),T=s(3052),Z=s(1228),U=s(9434);function S(e){let{className:a,classNames:s,showOutsideDays:r=!0,...n}=e;return(0,t.jsx)(Z.h,{showOutsideDays:r,className:(0,U.cn)("p-3",a),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,U.cn)((0,m.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,U.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===n.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,U.cn)((0,m.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},components:{Chevron:e=>{let{orientation:a,...s}=e,r="left"===a?D.A:T.A;return(0,t.jsx)(r,{className:"size-4",...s})}},...n})}var F=s(4410);function O(e){let{...a}=e;return(0,t.jsx)(F.bL,{"data-slot":"popover",...a})}function H(e){let{...a}=e;return(0,t.jsx)(F.l9,{"data-slot":"popover-trigger",...a})}function V(e){let{className:a,align:s="center",sideOffset:r=4,...n}=e;return(0,t.jsx)(F.ZL,{children:(0,t.jsx)(F.UC,{"data-slot":"popover-content",align:s,sideOffset:r,className:(0,U.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",a),...n})})}var W=s(9074),q=s(7716),G=s(3439);function K(e){let{onChange:a,className:s}=e,l=new Date,[o,c]=(0,r.useState)(30),[d,u]=(0,r.useState)(!1),[x,h]=(0,r.useState)({from:(0,n.e)(l,30),to:l}),f=e=>{let s={from:(0,n.e)(l,e),to:l};h(s),c(e),a(s)};return(0,t.jsxs)("div",{className:(0,U.cn)("flex flex-wrap gap-2",s),children:[[{label:"\xdaltimos 7 d\xedas",days:7},{label:"\xdaltimos 14 d\xedas",days:14},{label:"\xdaltimos 30 d\xedas",days:30}].map(e=>(0,t.jsx)(m.Button,{variant:o===e.days?"default":"outline",size:"sm",onClick:()=>f(e.days),className:o===e.days?"bg-blue-600 hover:bg-blue-700":"",children:e.label},e.days)),(0,t.jsxs)(O,{open:d,onOpenChange:u,children:[(0,t.jsx)(H,{asChild:!0,children:(0,t.jsxs)(m.Button,{variant:0===o?"default":"outline",size:"sm",className:(0,U.cn)("flex items-center gap-1",0===o?"bg-blue-600 hover:bg-blue-700":""),children:[(0,t.jsx)(W.A,{className:"h-4 w-4"}),0===o?"".concat((0,i.GP)(x.from,"dd/MM/yy")," - ").concat((0,i.GP)(x.to,"dd/MM/yy")):"Personalizado"]})}),(0,t.jsx)(V,{className:"w-auto p-0",align:"end",children:(0,t.jsx)(S,{initialFocus:!0,mode:"range",defaultMonth:x.from,selected:x,onSelect:e=>{if((null==e?void 0:e.from)&&(null==e?void 0:e.to)){let s={from:e.from,to:e.to};h(s),c(0),a(s),u(!1)}},numberOfMonths:2,locale:G.es,disabled:e=>(0,q.d)(e,l)})})]})]})}var X=s(6102);function Y(){var e,a,s,_,I,M,B,k,P,D;(0,o.As)();let[T,Z]=(0,r.useState)(!1),[U,S]=(0,r.useState)([]),[F,O]=(0,r.useState)({from:(0,n.e)(new Date,30),to:new Date}),{accountData:H,error:V,isLoading:W}=(0,o.T4)(),{usageData:q,error:G,isLoading:Y,mutate:$}=(0,o.TB)(),{data:Q,error:J,isLoading:ee,mutate:ea}=(0,o.Xn)(F.from,F.to);(0,r.useEffect)(()=>{Q&&S(Q.map(e=>({date:e.date,apiCalls:e.api_calls,storage:e.storage})))},[Q]);let es=async()=>{Z(!0);try{await Promise.all([$(),ea()]),f.o.success("Datos de uso actualizados")}catch(e){(0,p.h)(e,"Error al actualizar los datos de uso")}finally{Z(!1)}};if((()=>{var e;if(U.length)return U.reduce((e,a)=>e+a.apiCalls,0),null==(e=U[U.length-1])||e.storage})(),Y||W||ee)return(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-display",children:"Uso de API"}),(0,t.jsx)(d.E,{className:"h-9 w-24"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[1,2,3].map(e=>(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{className:"pb-2",children:[(0,t.jsx)(d.E,{className:"h-5 w-24"}),(0,t.jsx)(d.E,{className:"h-4 w-16"})]}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsx)(d.E,{className:"h-8 w-16 mx-auto mb-2"}),(0,t.jsx)(d.E,{className:"h-2 w-full"})]})]},e))}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(d.E,{className:"h-6 w-32"}),(0,t.jsx)(d.E,{className:"h-4 w-48"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)(d.E,{className:"h-64 w-full"})})]})]});let et=()=>{var e,a;return!!(q&&((null==(e=q.apiCalls)?void 0:e.used)||(null==(a=q.storage)?void 0:a.usedBytes)))};return G||V||J?(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-display",children:"Uso de API"}),(0,t.jsxs)(m.Button,{onClick:es,disabled:T,variant:"outline",size:"sm",className:"h-9",children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2 ".concat(T?"animate-spin":"")}),"Actualizar"]})]}),(0,t.jsxs)(h.Fc,{variant:"destructive",className:"mb-6",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsx)(h.XL,{children:"Error al cargar datos"}),(0,t.jsx)(h.TN,{children:("string"==typeof G?G:null==G?void 0:G.message)||("string"==typeof V?V:null==V?void 0:V.message)||("string"==typeof J?J:null==J?void 0:J.message)||"Ocurri\xf3 un error al cargar los datos de uso."})]})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-display mb-2",children:"Uso de API"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Monitorea el uso de tu API y almacenamiento en tiempo real"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(m.Button,{onClick:es,disabled:T,variant:"outline",size:"sm",className:"h-9",children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2 ".concat(T?"animate-spin":"")}),"Actualizar"]})})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)(K,{onChange:e=>{O(e)},className:"w-auto"})}),(0,t.jsxs)("div",{className:"mb-6 flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(A.mm,{icon:v.A,size:"sm",context:"muted",className:"mr-1"}),(0,t.jsxs)("span",{children:["\xdaltima actualizaci\xf3n: ","Hace unos momentos"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsxs)(c.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-blue-100 dark:hover:border-blue-900",children:[(0,t.jsxs)(c.aR,{className:"pb-2",children:[(0,t.jsxs)(c.ZB,{className:"flex items-center text-lg",children:[(0,t.jsx)(A.mm,{icon:b.A,size:"md",context:"primary",className:"mr-2"}),"Llamadas a la API"]}),(0,t.jsxs)(c.BT,{children:["Total en el periodo: ",F.from?(0,i.GP)(F.from,"dd/MM/yy"):"inicio"," - ",F.to?(0,i.GP)(F.to,"dd/MM/yy"):"fin"]})]}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsx)("div",{className:"text-metric text-center py-2 text-primary",children:(0,R.ZV)((null==q||null==(e=q.apiCalls)?void 0:e.used)||0)}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,t.jsx)("span",{children:"Uso actual"}),(0,t.jsx)("span",{className:"font-medium",children:(0,R.ZV)((null==q||null==(a=q.apiCalls)?void 0:a.used)||0)})]}),(0,t.jsx)(u.k,{value:Math.min(((null==q||null==(s=q.apiCalls)?void 0:s.used)||0)/((null==q||null==(_=q.apiCalls)?void 0:_.limit)||1e4)*100,100),className:"h-1.5 rayuela-progress-glow"}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(A.mm,{icon:v.A,size:"xs",context:"muted",className:"mr-1"}),(0,t.jsx)("span",{children:"Pr\xf3ximo reset:"})]}),(0,t.jsx)("span",{className:"font-medium",children:(null==q||null==(I=q.apiCalls)?void 0:I.resetDate)?(0,i.GP)((0,l.H)(q.apiCalls.resetDate),"dd/MM/yy HH:mm"):"No disponible"})]})]})]})]}),(0,t.jsxs)(c.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-green-100 dark:hover:border-green-900",children:[(0,t.jsxs)(c.aR,{className:"pb-2",children:[(0,t.jsxs)(c.ZB,{className:"flex items-center text-lg",children:[(0,t.jsx)(A.mm,{icon:N.A,size:"md",context:"primary",className:"mr-2"}),"Almacenamiento",(0,t.jsx)(X.Bc,{children:(0,t.jsxs)(X.m_,{children:[(0,t.jsx)(X.k$,{asChild:!0,children:(0,t.jsx)(A.mm,{icon:y.A,size:"sm",context:"muted",className:"ml-1 cursor-help"})}),(0,t.jsx)(X.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:"Datos almacenados en tu cuenta"})})]})})]}),(0,t.jsxs)(c.BT,{children:["Espacio utilizado al ",F.to?(0,i.GP)(F.to,"dd/MM/yy"):"final del periodo"]})]}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsx)("div",{className:"text-metric text-center py-2 text-success",children:(0,R.z3)((null==q||null==(M=q.storage)?void 0:M.usedBytes)||0)}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,t.jsx)("span",{children:"Uso actual"}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"font-medium",children:(0,R.z3)((null==q||null==(B=q.storage)?void 0:B.usedBytes)||0)}),(0,t.jsxs)("span",{className:"ml-1",children:["/ ",(null==q||null==(k=q.storage)?void 0:k.limitBytes)?(0,R.z3)(q.storage.limitBytes):"∞"]})]})]}),(0,t.jsx)(u.k,{value:(null==q||null==(P=q.storage)?void 0:P.percentage)||0,className:"h-1.5 rayuela-progress-glow"}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(A.mm,{icon:v.A,size:"xs",context:"muted",className:"mr-1"}),(0,t.jsx)("span",{children:"\xdaltima medici\xf3n:"})]}),(0,t.jsx)("span",{className:"font-medium",children:(null==q||null==(D=q.storage)?void 0:D.lastMeasured)?(0,i.GP)((0,l.H)(q.storage.lastMeasured),"dd/MM/yy HH:mm"):"No disponible"})]})]})]})]}),(0,t.jsxs)(c.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-purple-100 dark:hover:border-purple-900",children:[(0,t.jsxs)(c.aR,{className:"pb-2",children:[(0,t.jsxs)(c.ZB,{className:"flex items-center text-lg",children:[(0,t.jsx)(A.mm,{icon:w.A,size:"md",context:"primary",className:"mr-2"}),"Estado de la Cuenta"]}),(0,t.jsx)(c.BT,{children:"Informaci\xf3n general de tu cuenta"})]}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-3 py-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(A.mm,{icon:w.A,size:"sm",context:"primary",className:"mr-1.5"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"Plan actual:"})]}),(0,t.jsx)(x.E,{variant:"default",className:"text-xs",children:(null==H?void 0:H.plan)||"B\xe1sico"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(A.mm,{icon:N.A,size:"sm",context:"primary",className:"mr-1.5"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"Datos disponibles:"})]}),(0,t.jsx)("span",{className:"font-medium text-sm",children:et()?"S\xed":"No"})]})]}),!et()&&(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-4 text-center",children:[(0,t.jsx)(A.mm,{icon:w.A,size:"xl",context:"muted",className:"mb-2"}),(0,t.jsx)("p",{className:"text-muted-foreground font-medium text-sm",children:"No hay datos disponibles"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground text-center max-w-md",children:"Los datos de uso se mostrar\xe1n aqu\xed una vez que comiences a usar la API."})]})]})]})]}),(0,t.jsxs)(c.Zp,{className:"mb-8",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(A.mm,{icon:E.A,size:"md",context:"primary",className:"mr-2"}),"Historial de Uso"]}),(0,t.jsx)(c.BT,{children:"Tendencias de uso de la API durante el periodo seleccionado"})]}),(0,t.jsxs)(c.Wu,{children:[(0,t.jsx)("div",{className:"mb-4 p-3 bg-info-light rounded-lg border border-info/20",children:(0,t.jsxs)("p",{className:"text-sm flex items-start",children:[(0,t.jsx)(A.mm,{icon:w.A,size:"md",context:"info",className:"mr-2 shrink-0 mt-0.5"}),(0,t.jsx)("span",{children:"El gr\xe1fico muestra las tendencias de uso de tu API durante el periodo seleccionado. Los datos se actualizan autom\xe1ticamente y reflejan las llamadas realizadas y el almacenamiento utilizado."})]})}),(0,t.jsx)(L,{data:U,isLoading:ee,error:J})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(z.s,{priceId:"default_price_id",planName:"Pro"}),(0,t.jsx)(C.q,{})]})]})}var $=s(8126),Q=s.n($);function J(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(Q(),{children:[(0,t.jsx)("title",{children:"Panel de Uso | Rayuela API"}),(0,t.jsx)("meta",{name:"description",content:"Monitorea el uso de tu API, visualiza estad\xedsticas y administra tus l\xedmites de consumo."})]}),(0,t.jsx)(Y,{})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[5647,9352,6874,1445,5674,3753,4214,3843,8034,9566,9521,448,679,2092,3999,7571,8441,1684,7358],()=>a(1214)),_N_E=e.O()}]);