"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2092],{285:(e,t,a)=>{a.d(t,{Button:()=>p,r:()=>r});var i=a(5155),s=a(2115),o=a(9708),n=a(2085),c=a(9434);let r=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-body-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border focus-visible:border-ring",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 shadow-xs hover:shadow-soft rayuela-button-hover",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/95 shadow-xs hover:shadow-soft rayuela-button-hover",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80 shadow-xs hover:shadow-soft rayuela-button-hover",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90 shadow-xs hover:shadow-soft rayuela-button-hover",ghost:"hover:bg-accent hover:text-accent-foreground active:bg-accent/80 rayuela-button-hover",link:"text-primary underline-offset-4 hover:underline active:text-primary/80 rayuela-button-hover"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-lg px-3 text-caption-lg",lg:"h-11 rounded-lg px-8 text-body-lg",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=s.forwardRef((e,t)=>{let{className:a,variant:s,size:n,asChild:p=!1,...l}=e,d=p?o.DX:"button";return(0,i.jsx)(d,{className:(0,c.cn)(r({variant:s,size:n,className:a})),ref:t,...l})});p.displayName="Button"},2656:(e,t,a)=>{a.d(t,{_C:()=>s});var i=a(3464);let s=()=>({healthCheckHealthGet:e=>i.A.get("/health",e),sendVerificationEmailApiV1AuthSendVerificationEmailPost:e=>i.A.post("/api/v1/auth/send-verification-email",void 0,e),verifyEmailApiV1AuthVerifyEmailGet:(e,t)=>i.A.get("/api/v1/auth/verify-email",{...t,params:{...e,...null==t?void 0:t.params}}),registerApiV1AuthRegisterPost:(e,t)=>i.A.post("/api/v1/auth/register",e,t),loginApiV1AuthTokenPost:(e,t)=>i.A.post("/api/v1/auth/token",e,t),logoutApiV1AuthLogoutPost:e=>i.A.post("/api/v1/auth/logout",void 0,e),listAccountsApiV1AccountsGet:e=>i.A.get("/api/v1/accounts/",e),createAccountApiV1AccountsAccountsPost:(e,t)=>i.A.post("/api/v1/accounts/accounts",e,t),getAccountApiV1AccountsAccountIdGet:(e,t)=>i.A.get("/api/v1/accounts/".concat(e),t),deactivateAccountApiV1AccountsAccountIdDeactivatePatch:(e,t)=>i.A.patch("/api/v1/accounts/".concat(e,"/deactivate"),void 0,t),activateAccountApiV1AccountsAccountIdActivatePatch:(e,t)=>i.A.patch("/api/v1/accounts/".concat(e,"/activate"),void 0,t),getAccountInfoApiV1AccountsCurrentGet:e=>i.A.get("/api/v1/accounts/current",e),updateCurrentAccountApiV1AccountsCurrentPut:(e,t)=>i.A.put("/api/v1/accounts/current",e,t),patchCurrentAccountApiV1AccountsCurrentPatch:(e,t)=>i.A.patch("/api/v1/accounts/current",e,t),getAuditLogsApiV1AccountsAccountIdAuditLogsGet:(e,t,a)=>i.A.get("/api/v1/accounts/".concat(e,"/audit-logs"),{...a,params:{...t,...null==a?void 0:a.params}}),getApiUsageApiV1AccountsUsageGet:e=>i.A.get("/api/v1/accounts/usage",e),getAvailablePlansApiV1PlansGet:e=>i.A.get("/api/v1/plans/",e),getCurrentUserInfoApiV1SystemUsersMeGet:e=>i.A.get("/api/v1/system-users/me",e),updateUserMeApiV1SystemUsersMePut:(e,t)=>i.A.put("/api/v1/system-users/me",e,t),deleteUserMeApiV1SystemUsersMeDelete:e=>i.A.delete("/api/v1/system-users/me",e),createSystemUserApiV1SystemUsersPost:(e,t)=>i.A.post("/api/v1/system-users/",e,t),getSystemUserApiV1SystemUsersUserIdGet:(e,t)=>i.A.get("/api/v1/system-users/".concat(e),t),createRoleApiV1SystemUsersRolesPost:(e,t)=>i.A.post("/api/v1/system-users/roles/",e,t),assignRoleApiV1SystemUsersUserIdRolesRoleIdPost:(e,t,a)=>i.A.post("/api/v1/system-users/".concat(e,"/roles/").concat(t),void 0,a),removeRoleApiV1SystemUsersUserIdRolesRoleIdDelete:(e,t,a)=>i.A.delete("/api/v1/system-users/".concat(e,"/roles/").concat(t),a),getUserRolesApiV1SystemUsersUserIdRolesGet:(e,t)=>i.A.get("/api/v1/system-users/".concat(e,"/roles"),t),getUserPermissionsApiV1SystemUsersUserIdPermissionsGet:(e,t)=>i.A.get("/api/v1/system-users/".concat(e,"/permissions"),t),createEndUserApiV1EndUsersPost:(e,t)=>i.A.post("/api/v1/end-users/",e,t),readEndUsersApiV1EndUsersGet:(e,t)=>i.A.get("/api/v1/end-users/",{...t,params:{...e,...null==t?void 0:t.params}}),readEndUserApiV1EndUsersUserIdGet:(e,t)=>i.A.get("/api/v1/end-users/".concat(e),t),createProductApiV1ProductsPost:(e,t)=>i.A.post("/api/v1/products/",e,t),readProductsApiV1ProductsGet:(e,t)=>i.A.get("/api/v1/products/",{...t,params:{...e,...null==t?void 0:t.params}}),getProductApiV1ProductsProductIdGet:(e,t)=>i.A.get("/api/v1/products/".concat(e),t),updateProductApiV1ProductsProductIdPut:(e,t,a)=>i.A.put("/api/v1/products/".concat(e),t,a),updateInventoryApiV1ProductsProductIdInventoryPatch:(e,t,a)=>i.A.patch("/api/v1/products/".concat(e,"/inventory"),t,a),getMostSearchedApiV1RecommendationsMostSearchedGet:(e,t)=>i.A.get("/api/v1/recommendations/most-searched/",{...t,params:{...e,...null==t?void 0:t.params}}),getTrendingSearchesApiV1RecommendationsTrendingSearchesGet:(e,t)=>i.A.get("/api/v1/recommendations/trending-searches/",{...t,params:{...e,...null==t?void 0:t.params}}),getPopularTrendsApiV1RecommendationsPopularTrendsGet:(e,t)=>i.A.get("/api/v1/recommendations/popular-trends/",{...t,params:{...e,...null==t?void 0:t.params}}),getRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGet:(e,t,a)=>i.A.get("/api/v1/recommendations/related-searches/".concat(e),{...a,params:{...t,...null==a?void 0:a.params}}),getMostSoldApiV1RecommendationsMostSoldGet:(e,t)=>i.A.get("/api/v1/recommendations/most-sold/",{...t,params:{...e,...null==t?void 0:t.params}}),getTopRatedApiV1RecommendationsTopRatedGet:(e,t)=>i.A.get("/api/v1/recommendations/top-rated/",{...t,params:{...e,...null==t?void 0:t.params}}),getCategoryProductsApiV1RecommendationsCategoryCategoryGet:(e,t,a)=>i.A.get("/api/v1/recommendations/category/".concat(e),{...a,params:{...t,...null==a?void 0:a.params}}),getRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGet:(e,t,a)=>i.A.get("/api/v1/recommendations/related-categories/".concat(e),{...a,params:{...t,...null==a?void 0:a.params}}),getAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGet:(e,t,a)=>i.A.get("/api/v1/recommendations/also-bought/".concat(e),{...a,params:{...t,...null==a?void 0:a.params}}),getSimilarProductsApiV1RecommendationsProductsProductIdSimilarGet:(e,t,a)=>i.A.get("/api/v1/recommendations/products/".concat(e,"/similar"),{...a,params:{...t,...null==a?void 0:a.params}}),invalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPost:(e,t)=>i.A.post("/api/v1/recommendations/invalidate-cache/".concat(e),void 0,t),invalidateAccountCacheApiV1RecommendationsInvalidateCachePost:e=>i.A.post("/api/v1/recommendations/invalidate-cache",void 0,e),queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPost:(e,t)=>i.A.post("/api/v1/recommendations/personalized/query",e,t),getRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGet:(e,t,a)=>i.A.get("/api/v1/recommendations/explain/".concat(e,"/").concat(t),a),getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet:e=>i.A.get("/api/v1/recommendations/confidence-metrics",e),rollbackModelApiV1RecommendationsRollbackArtifactVersionPost:(e,t)=>i.A.post("/api/v1/recommendations/rollback/".concat(e),void 0,t),createInteractionApiV1InteractionsPost:(e,t)=>i.A.post("/api/v1/interactions/",e,t),readInteractionsApiV1InteractionsGet:(e,t)=>i.A.get("/api/v1/interactions/",{...t,params:{...e,...null==t?void 0:t.params}}),trainModelsApiV1PipelineTrainPost:e=>i.A.post("/api/v1/pipeline/train",void 0,e),getTrainingStatusApiV1PipelineStatusGet:e=>i.A.get("/api/v1/pipeline/status",e),getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet:(e,t)=>i.A.get("/api/v1/pipeline/jobs/".concat(e,"/status"),t),listModelsApiV1PipelineModelsGet:(e,t)=>i.A.get("/api/v1/pipeline/models",{...t,params:{...e,...null==t?void 0:t.params}}),getModelMetricsApiV1PipelineModelsModelIdMetricsGet:(e,t)=>i.A.get("/api/v1/pipeline/models/".concat(e,"/metrics"),t),invalidateCacheApiV1PipelineInvalidateCachePost:(e,t)=>i.A.post("/api/v1/pipeline/invalidate-cache",void 0,{...t,params:{...e,...null==t?void 0:t.params}}),trainArtifactForAccountApiV1PipelineTrainAccountIdPost:(e,t)=>i.A.post("/api/v1/pipeline/train/".concat(e),void 0,t),processTrainingJobApiV1PipelineProcessPost:(e,t)=>i.A.post("/api/v1/pipeline/process",e,t),trainingCallbackApiV1PipelineCallbackJobIdPost:(e,t,a)=>i.A.post("/api/v1/pipeline/callback/".concat(e),t,a),testCacheApiV1CacheTestCacheGet:e=>i.A.get("/api/v1/cache/test-cache",e),checkRedisApiV1CacheRedisHealthGet:e=>i.A.get("/api/v1/cache/redis-health",e),getAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet:(e,t)=>i.A.get("/api/v1/analytics/analytics/account",{...t,params:{...e,...null==t?void 0:t.params}}),getEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet:(e,t)=>i.A.get("/api/v1/analytics/analytics/endpoints",{...t,params:{...e,...null==t?void 0:t.params}}),getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet:(e,t)=>i.A.get("/api/v1/analytics/analytics/recommendation_performance",{...t,params:{...e,...null==t?void 0:t.params}}),compareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGet:(e,t)=>i.A.get("/api/v1/analytics/analytics/models/compare",{...t,params:{...e,...null==t?void 0:t.params}}),getMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGet:(e,t)=>i.A.get("/api/v1/analytics/analytics/metrics/history",{...t,params:{...e,...null==t?void 0:t.params}}),createCheckoutSessionApiV1BillingCreateCheckoutSessionPost:(e,t)=>i.A.post("/api/v1/billing/create-checkout-session",e,t),createPortalSessionApiV1BillingCreatePortalSessionPost:(e,t)=>i.A.post("/api/v1/billing/create-portal-session",e,t),mercadopagoWebhookApiV1BillingWebhookMercadopagoPost:e=>i.A.post("/api/v1/billing/webhook/mercadopago",void 0,e),listRolesApiV1RolesGet:e=>i.A.get("/api/v1/roles/",e),createRoleApiV1RolesPost:(e,t)=>i.A.post("/api/v1/roles/",e,t),getRoleApiV1RolesRoleIdGet:(e,t)=>i.A.get("/api/v1/roles/".concat(e),t),updateRoleApiV1RolesRoleIdPut:(e,t,a)=>i.A.put("/api/v1/roles/".concat(e),t,a),deleteRoleApiV1RolesRoleIdDelete:(e,t)=>i.A.delete("/api/v1/roles/".concat(e),t),getRolePermissionsApiV1RolesRoleIdPermissionsGet:(e,t)=>i.A.get("/api/v1/roles/".concat(e,"/permissions"),t),assignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPost:(e,t,a)=>i.A.post("/api/v1/roles/".concat(e,"/permissions/").concat(t),void 0,a),removePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDelete:(e,t,a)=>i.A.delete("/api/v1/roles/".concat(e,"/permissions/").concat(t),a),listPermissionsApiV1PermissionsGet:e=>i.A.get("/api/v1/permissions/",e),createPermissionApiV1PermissionsPost:(e,t)=>i.A.post("/api/v1/permissions/",e,t),getPermissionApiV1PermissionsPermissionIdGet:(e,t)=>i.A.get("/api/v1/permissions/".concat(e),t),getRolesWithPermissionApiV1PermissionsPermissionIdRolesGet:(e,t)=>i.A.get("/api/v1/permissions/".concat(e,"/roles"),t),cleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost:(e,t)=>i.A.post("/api/v1/maintenance/maintenance/cleanup-audit-logs",void 0,{...t,params:{...e,...null==t?void 0:t.params}}),cleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost:(e,t)=>i.A.post("/api/v1/maintenance/maintenance/cleanup-interactions",void 0,{...t,params:{...e,...null==t?void 0:t.params}}),getTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet:(e,t)=>i.A.get("/api/v1/maintenance/maintenance/task/".concat(e),t),cleanupDataSecureEndpointApiV1MaintenanceMaintenanceCleanupDataSecurePost:(e,t)=>i.A.post("/api/v1/maintenance/maintenance/cleanup-data-secure",void 0,{...t,params:{...e,...null==t?void 0:t.params}}),archiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost:(e,t)=>i.A.post("/api/v1/maintenance/maintenance/archive-and-cleanup-audit-logs",void 0,{...t,params:{...e,...null==t?void 0:t.params}}),archiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost:(e,t)=>i.A.post("/api/v1/maintenance/maintenance/archive-and-cleanup-interactions",void 0,{...t,params:{...e,...null==t?void 0:t.params}}),listArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet:(e,t,a)=>i.A.get("/api/v1/maintenance/maintenance/archived-files/".concat(e),{...a,params:{...t,...null==a?void 0:a.params}}),cleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost:(e,t)=>i.A.post("/api/v1/maintenance/maintenance/cleanup-soft-deleted-records",void 0,{...t,params:{...e,...null==t?void 0:t.params}}),getSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet:(e,t)=>i.A.get("/api/v1/maintenance/maintenance/soft-delete-statistics",{...t,params:{...e,...null==t?void 0:t.params}}),monitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost:(e,t)=>i.A.post("/api/v1/maintenance/maintenance/monitor-tables",void 0,{...t,params:{...e,...null==t?void 0:t.params}}),getSubscriptionUsageApiV1SubscriptionUsageGet:e=>i.A.get("/api/v1/subscription/usage",e),getStorageUsageApiV1StorageUsageGet:e=>i.A.get("/api/v1/storage/usage",e),refreshStorageUsageApiV1StorageRefreshPost:e=>i.A.post("/api/v1/storage/refresh",void 0,e),batchDataIngestionApiV1IngestionBatchPost:(e,t)=>i.A.post("/api/v1/ingestion/batch",e,t),getBatchJobStatusApiV1IngestionBatchJobIdGet:(e,t)=>i.A.get("/api/v1/ingestion/batch/".concat(e),t),getUsageSummaryApiV1UsageSummaryGet:e=>i.A.get("/api/v1/usage/summary",e),getUsageHistoryApiV1UsageHistoryGet:(e,t)=>i.A.get("/api/v1/usage/history",{...t,params:{...e,...null==t?void 0:t.params}}),listApiKeysApiV1ApiKeysGet:e=>i.A.get("/api/v1/api-keys/",e),createApiKeyApiV1ApiKeysPost:(e,t)=>i.A.post("/api/v1/api-keys/",e,t),revokeApiKeyApiV1ApiKeysDelete:e=>i.A.delete("/api/v1/api-keys/",e),getCurrentApiKeyApiV1ApiKeysCurrentGet:e=>i.A.get("/api/v1/api-keys/current",e),updateApiKeyApiV1ApiKeysApiKeyIdPut:(e,t,a)=>i.A.put("/api/v1/api-keys/".concat(e),t,a),revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete:(e,t)=>i.A.delete("/api/v1/api-keys/".concat(e),t)})},9434:(e,t,a)=>{a.d(t,{cn:()=>o});var i=a(2596),s=a(9688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,i.$)(t))}}}]);