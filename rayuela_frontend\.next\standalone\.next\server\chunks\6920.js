"use strict";exports.id=6920,exports.ids=[6920],exports.modules={3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},25957:(e,t,n)=>{n.d(t,{UC:()=>eJ,YJ:()=>e$,In:()=>eY,q7:()=>eQ,VF:()=>e1,p4:()=>e0,JU:()=>eG,ZL:()=>eZ,bL:()=>eU,wn:()=>e3,PP:()=>e6,wv:()=>e5,l9:()=>ez,WT:()=>eq,LM:()=>eX});var r,o=n(43210),l=n(51215);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(70569),s=n(9510),u=n(98599),d=n(11273),c=n(43),f=n(8730),p=n(60687),v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,f.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,p.jsx)(o?n:t,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),m=n(13495),h=n(16309),w="dismissableLayer.update",y=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),g=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:l,onPointerDownOutside:i,onFocusOutside:s,onInteractOutside:d,onDismiss:c,...f}=e,g=o.useContext(y),[E,S]=o.useState(null),C=E?.ownerDocument??globalThis?.document,[,T]=o.useState({}),P=(0,u.s)(t,e=>S(e)),L=Array.from(g.layers),[N]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),j=L.indexOf(N),R=E?L.indexOf(E):-1,D=g.layersWithOutsidePointerEventsDisabled.size>0,k=R>=j,I=function(e,t=globalThis?.document){let n=(0,m.c)(e),r=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){b("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",l.current),l.current=r,t.addEventListener("click",l.current,{once:!0})):r()}else t.removeEventListener("click",l.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",l.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...g.branches].some(e=>e.contains(t));k&&!n&&(i?.(e),d?.(e),e.defaultPrevented||c?.())},C),M=function(e,t=globalThis?.document){let n=(0,m.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&b("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...g.branches].some(e=>e.contains(t))&&(s?.(e),d?.(e),e.defaultPrevented||c?.())},C);return(0,h.U)(e=>{R===g.layers.size-1&&(l?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},C),o.useEffect(()=>{if(E)return n&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(E)),g.layers.add(E),x(),()=>{n&&1===g.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[E,C,n,g]),o.useEffect(()=>()=>{E&&(g.layers.delete(E),g.layersWithOutsidePointerEventsDisabled.delete(E),x())},[E,g]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(w,e),()=>document.removeEventListener(w,e)},[]),(0,p.jsx)(v.div,{...f,ref:P,style:{pointerEvents:D?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,I.onPointerDownCapture)})});function x(){let e=new CustomEvent(w);document.dispatchEvent(e)}function b(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&o.addEventListener(e,t,{once:!0}),r)o&&l.flushSync(()=>o.dispatchEvent(i));else o.dispatchEvent(i)}g.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(y),r=o.useRef(null),l=(0,u.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(v.div,{...e,ref:l})}).displayName="DismissableLayerBranch";var E=n(1359),S="focusScope.autoFocusOnMount",C="focusScope.autoFocusOnUnmount",T={bubbles:!1,cancelable:!0},P=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:l,onUnmountAutoFocus:i,...a}=e,[s,d]=o.useState(null),c=(0,m.c)(l),f=(0,m.c)(i),h=o.useRef(null),w=(0,u.s)(t,e=>d(e)),y=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(y.paused||!s)return;let t=e.target;s.contains(t)?h.current=t:j(h.current,{select:!0})},t=function(e){if(y.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||j(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&j(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,s,y.paused]),o.useEffect(()=>{if(s){R.add(y);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(S,T);s.addEventListener(S,c),s.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(j(r,{select:t}),document.activeElement!==n)return}(L(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&j(s))}return()=>{s.removeEventListener(S,c),setTimeout(()=>{let t=new CustomEvent(C,T);s.addEventListener(C,f),s.dispatchEvent(t),t.defaultPrevented||j(e??document.body,{select:!0}),s.removeEventListener(C,f),R.remove(y)},0)}}},[s,c,f,y]);let g=o.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,l]=function(e){let t=L(e);return[N(t,e),N(t.reverse(),e)]}(t);r&&l?e.shiftKey||o!==l?e.shiftKey&&o===r&&(e.preventDefault(),n&&j(l,{select:!0})):(e.preventDefault(),n&&j(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,p.jsx)(v.div,{tabIndex:-1,...a,ref:w,onKeyDown:g})});function L(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function N(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function j(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}P.displayName="FocusScope";var R=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=D(e,t)).unshift(t)},remove(t){e=D(e,t),e[0]?.resume()}}}();function D(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var k=n(96963),I=n(73393),M=n(66156),A=o.forwardRef((e,t)=>{let{container:n,...r}=e,[i,a]=o.useState(!1);(0,M.N)(()=>a(!0),[]);let s=n||i&&globalThis?.document?.body;return s?l.createPortal((0,p.jsx)(v.div,{...r,ref:t}),s):null});A.displayName="Portal";var F=n(65551),O=n(83721),B=n(97364),H=n(63376),_=n(42247),K=[" ","Enter","ArrowUp","ArrowDown"],W=[" ","Enter"],V="Select",[U,z,q]=(0,s.N)(V),[Y,Z]=(0,d.A)(V,[q,I.Bk]),J=(0,I.Bk)(),[X,$]=Y(V),[G,Q]=Y(V),ee=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:v,disabled:m,required:h,form:w}=e,y=J(t),[g,x]=o.useState(null),[b,E]=o.useState(null),[S,C]=o.useState(!1),T=(0,c.jH)(d),[P,L]=(0,F.i)({prop:r,defaultProp:l??!1,onChange:i,caller:V}),[N,j]=(0,F.i)({prop:a,defaultProp:s,onChange:u,caller:V}),R=o.useRef(null),D=!g||w||!!g.closest("form"),[M,A]=o.useState(new Set),O=Array.from(M).map(e=>e.props.value).join(";");return(0,p.jsx)(I.bL,{...y,children:(0,p.jsxs)(X,{required:h,scope:t,trigger:g,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,k.B)(),value:N,onValueChange:j,open:P,onOpenChange:L,dir:T,triggerPointerDownPosRef:R,disabled:m,children:[(0,p.jsx)(U.Provider,{scope:t,children:(0,p.jsx)(G,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{A(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{A(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),D?(0,p.jsxs)(e_,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:v,value:N,onChange:e=>j(e.target.value),disabled:m,form:w,children:[void 0===N?(0,p.jsx)("option",{value:""}):null,Array.from(M)]},O):null]})})};ee.displayName=V;var et="SelectTrigger",en=o.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...l}=e,i=J(n),s=$(et,n),d=s.disabled||r,c=(0,u.s)(t,s.onTriggerChange),f=z(n),m=o.useRef("touch"),[h,w,y]=eW(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=eV(t,e,n);void 0!==r&&s.onValueChange(r.value)}),g=e=>{d||(s.onOpenChange(!0),y()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,p.jsx)(I.Mz,{asChild:!0,...i,children:(0,p.jsx)(v.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eK(s.value)?"":void 0,...l,ref:c,onClick:(0,a.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==m.current&&g(e)}),onPointerDown:(0,a.m)(l.onPointerDown,e=>{m.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,a.m)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||w(e.key),(!t||" "!==e.key)&&K.includes(e.key)&&(g(),e.preventDefault())})})})});en.displayName=et;var er="SelectValue",eo=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,s=$(er,n),{onValueNodeHasChildrenChange:d}=s,c=void 0!==l,f=(0,u.s)(t,s.onValueNodeChange);return(0,M.N)(()=>{d(c)},[d,c]),(0,p.jsx)(v.span,{...a,ref:f,style:{pointerEvents:"none"},children:eK(s.value)?(0,p.jsx)(p.Fragment,{children:i}):l})});eo.displayName=er;var el=o.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,p.jsx)(v.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});el.displayName="SelectIcon";var ei=e=>(0,p.jsx)(A,{asChild:!0,...e});ei.displayName="SelectPortal";var ea="SelectContent",es=o.forwardRef((e,t)=>{let n=$(ea,e.__scopeSelect),[r,i]=o.useState();return((0,M.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,p.jsx)(ef,{...e,ref:t}):r?l.createPortal((0,p.jsx)(eu,{scope:e.__scopeSelect,children:(0,p.jsx)(U.Slot,{scope:e.__scopeSelect,children:(0,p.jsx)("div",{children:e.children})})}),r):null});es.displayName=ea;var[eu,ed]=Y(ea),ec=(0,f.TL)("SelectContent.RemoveScroll"),ef=o.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:s,side:d,sideOffset:c,align:f,alignOffset:v,arrowPadding:m,collisionBoundary:h,collisionPadding:w,sticky:y,hideWhenDetached:x,avoidCollisions:b,...S}=e,C=$(ea,n),[T,L]=o.useState(null),[N,j]=o.useState(null),R=(0,u.s)(t,e=>L(e)),[D,k]=o.useState(null),[I,M]=o.useState(null),A=z(n),[F,O]=o.useState(!1),B=o.useRef(!1);o.useEffect(()=>{if(T)return(0,H.Eq)(T)},[T]),(0,E.Oh)();let K=o.useCallback(e=>{let[t,...n]=A().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),n?.focus(),document.activeElement!==o))return},[A,N]),W=o.useCallback(()=>K([D,T]),[K,D,T]);o.useEffect(()=>{F&&W()},[F,W]);let{onOpenChange:V,triggerPointerDownPosRef:U}=C;o.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(U.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,V,U]),o.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[q,Y]=eW(e=>{let t=A().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eV(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Z=o.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==C.value&&C.value===t||r)&&(k(e),r&&(B.current=!0))},[C.value]),J=o.useCallback(()=>T?.focus(),[T]),X=o.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==C.value&&C.value===t||r)&&M(e)},[C.value]),G="popper"===r?ev:ep,Q=G===ev?{side:d,sideOffset:c,align:f,alignOffset:v,arrowPadding:m,collisionBoundary:h,collisionPadding:w,sticky:y,hideWhenDetached:x,avoidCollisions:b}:{};return(0,p.jsx)(eu,{scope:n,content:T,viewport:N,onViewportChange:j,itemRefCallback:Z,selectedItem:D,onItemLeave:J,itemTextRefCallback:X,focusSelectedItem:W,selectedItemText:I,position:r,isPositioned:F,searchRef:q,children:(0,p.jsx)(_.A,{as:ec,allowPinchZoom:!0,children:(0,p.jsx)(P,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(l,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,p.jsx)(g,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,p.jsx)(G,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...S,...Q,onPlaced:()=>O(!0),ref:R,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,a.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});ef.displayName="SelectContentImpl";var ep=o.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...l}=e,a=$(ea,n),s=ed(ea,n),[d,c]=o.useState(null),[f,m]=o.useState(null),h=(0,u.s)(t,e=>m(e)),w=z(n),y=o.useRef(!1),g=o.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:S}=s,C=o.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&f&&x&&b&&E){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==a.dir){let r=o.left-t.left,l=n.left-r,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),c=i(l,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let r=t.right-o.right,l=window.innerWidth-n.right-r,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),c=i(l,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let l=w(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(f),p=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),m=parseInt(c.borderBottomWidth,10),h=p+v+u+parseInt(c.paddingBottom,10)+m,g=Math.min(5*b.offsetHeight,h),S=window.getComputedStyle(x),C=parseInt(S.paddingTop,10),T=parseInt(S.paddingBottom,10),P=e.top+e.height/2-10,L=b.offsetHeight/2,N=p+v+(b.offsetTop+L);if(N<=P){let e=l.length>0&&b===l[l.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-P,L+(e?T:0)+(f.clientHeight-x.offsetTop-x.offsetHeight)+m);d.style.height=N+t+"px"}else{let e=l.length>0&&b===l[0].ref.current;d.style.top="0px";let t=Math.max(P,p+x.offsetTop+(e?C:0)+L);d.style.height=t+(h-N)+"px",x.scrollTop=N-P+x.offsetTop}d.style.margin="10px 0",d.style.minHeight=g+"px",d.style.maxHeight=s+"px",r?.(),requestAnimationFrame(()=>y.current=!0)}},[w,a.trigger,a.valueNode,d,f,x,b,E,a.dir,r]);(0,M.N)(()=>C(),[C]);let[T,P]=o.useState();(0,M.N)(()=>{f&&P(window.getComputedStyle(f).zIndex)},[f]);let L=o.useCallback(e=>{e&&!0===g.current&&(C(),S?.(),g.current=!1)},[C,S]);return(0,p.jsx)(em,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:y,onScrollButtonChange:L,children:(0,p.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,p.jsx)(v.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});ep.displayName="SelectItemAlignedPosition";var ev=o.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=J(n);return(0,p.jsx)(I.UC,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ev.displayName="SelectPopperPosition";var[em,eh]=Y(ea,{}),ew="SelectViewport",ey=o.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...l}=e,i=ed(ew,n),s=eh(ew,n),d=(0,u.s)(t,i.onViewportChange),c=o.useRef(0);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,p.jsx)(U.Slot,{scope:n,children:(0,p.jsx)(v.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,a.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ey.displayName=ew;var eg="SelectGroup",[ex,eb]=Y(eg),eE=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,k.B)();return(0,p.jsx)(ex,{scope:n,id:o,children:(0,p.jsx)(v.div,{role:"group","aria-labelledby":o,...r,ref:t})})});eE.displayName=eg;var eS="SelectLabel",eC=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=eb(eS,n);return(0,p.jsx)(v.div,{id:o.id,...r,ref:t})});eC.displayName=eS;var eT="SelectItem",[eP,eL]=Y(eT),eN=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:l=!1,textValue:i,...s}=e,d=$(eT,n),c=ed(eT,n),f=d.value===r,[m,h]=o.useState(i??""),[w,y]=o.useState(!1),g=(0,u.s)(t,e=>c.itemRefCallback?.(e,r,l)),x=(0,k.B)(),b=o.useRef("touch"),E=()=>{l||(d.onValueChange(r),d.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,p.jsx)(eP,{scope:n,value:r,disabled:l,textId:x,isSelected:f,onItemTextChange:o.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,p.jsx)(U.ItemSlot,{scope:n,value:r,disabled:l,textValue:m,children:(0,p.jsx)(v.div,{role:"option","aria-labelledby":x,"data-highlighted":w?"":void 0,"aria-selected":f&&w,"data-state":f?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...s,ref:g,onFocus:(0,a.m)(s.onFocus,()=>y(!0)),onBlur:(0,a.m)(s.onBlur,()=>y(!1)),onClick:(0,a.m)(s.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,a.m)(s.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,a.m)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(s.onPointerMove,e=>{b.current=e.pointerType,l?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.m)(s.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(W.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eN.displayName=eT;var ej="SelectItemText",eR=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:i,...a}=e,s=$(ej,n),d=ed(ej,n),c=eL(ej,n),f=Q(ej,n),[m,h]=o.useState(null),w=(0,u.s)(t,e=>h(e),c.onItemTextChange,e=>d.itemTextRefCallback?.(e,c.value,c.disabled)),y=m?.textContent,g=o.useMemo(()=>(0,p.jsx)("option",{value:c.value,disabled:c.disabled,children:y},c.value),[c.disabled,c.value,y]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=f;return(0,M.N)(()=>(x(g),()=>b(g)),[x,b,g]),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(v.span,{id:c.textId,...a,ref:w}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(a.children,s.valueNode):null]})});eR.displayName=ej;var eD="SelectItemIndicator",ek=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return eL(eD,n).isSelected?(0,p.jsx)(v.span,{"aria-hidden":!0,...r,ref:t}):null});ek.displayName=eD;var eI="SelectScrollUpButton",eM=o.forwardRef((e,t)=>{let n=ed(eI,e.__scopeSelect),r=eh(eI,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,u.s)(t,r.onScrollButtonChange);return(0,M.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,p.jsx)(eO,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eM.displayName=eI;var eA="SelectScrollDownButton",eF=o.forwardRef((e,t)=>{let n=ed(eA,e.__scopeSelect),r=eh(eA,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,u.s)(t,r.onScrollButtonChange);return(0,M.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,p.jsx)(eO,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eF.displayName=eA;var eO=o.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...l}=e,i=ed("SelectScrollButton",n),s=o.useRef(null),u=z(n),d=o.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return o.useEffect(()=>()=>d(),[d]),(0,M.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,p.jsx)(v.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,a.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:(0,a.m)(l.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:(0,a.m)(l.onPointerLeave,()=>{d()})})}),eB=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,p.jsx)(v.div,{"aria-hidden":!0,...r,ref:t})});eB.displayName="SelectSeparator";var eH="SelectArrow";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=J(n),l=$(eH,n),i=ed(eH,n);return l.open&&"popper"===i.position?(0,p.jsx)(I.i3,{...o,...r,ref:t}):null}).displayName=eH;var e_=o.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{let l=o.useRef(null),i=(0,u.s)(r,l),a=(0,O.Z)(t);return o.useEffect(()=>{let e=l.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,p.jsx)(v.select,{...n,style:{...B.Qg,...n.style},ref:i,defaultValue:t})});function eK(e){return""===e||void 0===e}function eW(e){let t=(0,m.c)(e),n=o.useRef(""),r=o.useRef(0),l=o.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=o.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,l,i]}function eV(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==n?s:void 0}e_.displayName="SelectBubbleInput";var eU=ee,ez=en,eq=eo,eY=el,eZ=ei,eJ=es,eX=ey,e$=eE,eG=eC,eQ=eN,e0=eR,e1=ek,e6=eM,e3=eF,e5=eB},78272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}};