"""Add system_user_id column to audit_logs and FK to system_users

Revision ID: 20250704_120000
Revises: 20250701_020000
Create Date: 2025-07-04 12:00:00
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250704_120000"
down_revision: Union[str, None] = "20250701_020000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _column_exists(table_name: str, column_name: str) -> bool:
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT column_name FROM information_schema.columns
        WHERE table_name = :table_name
          AND column_name = :column_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "column_name": column_name},
    )
    return result.fetchone() is not None


def _constraint_exists(constraint_name: str, table_name: str) -> bool:
    """Helper function to check if a constraint exists"""
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT constraint_name FROM information_schema.table_constraints
        WHERE table_name = :table_name
          AND constraint_name = :constraint_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "constraint_name": constraint_name},
    )
    return result.fetchone() is not None


def upgrade() -> None:
    """Add system_user_id column and foreign key."""

    # First, fix system_users table structure to match SQLAlchemy model
    print("Fixing system_users table structure...")

    # Step 1: Fix system_user_roles table structure first
    print("  Fixing system_user_roles table structure...")

    # Drop existing constraints in system_user_roles
    existing_fks = ["fk_system_user_role_system_user", "system_user_roles_user_id_fkey", "system_user_roles_role_id_fkey"]
    for fk_name in existing_fks:
        if _constraint_exists(fk_name, "system_user_roles"):
            op.drop_constraint(fk_name, "system_user_roles", type_="foreignkey")

    if _constraint_exists("system_user_roles_pkey", "system_user_roles"):
        op.drop_constraint("system_user_roles_pkey", "system_user_roles", type_="primary")

    # Add account_id column if it doesn't exist
    if not _column_exists("system_user_roles", "account_id"):
        op.add_column("system_user_roles", sa.Column("account_id", sa.Integer(), nullable=False, server_default="1"))
        op.alter_column("system_user_roles", "account_id", server_default=None)

    # Rename user_id to system_user_id if needed
    if _column_exists("system_user_roles", "user_id") and not _column_exists("system_user_roles", "system_user_id"):
        op.alter_column("system_user_roles", "user_id", new_column_name="system_user_id")

    # Create composite primary key for system_user_roles
    op.create_primary_key("system_user_roles_pkey", "system_user_roles", ["account_id", "system_user_id", "role_id"])

    # Step 2: Drop existing foreign key constraints that reference system_users
    print("  Dropping remaining foreign key constraints...")

    # Step 3: Drop existing primary key constraint from system_users
    print("  Dropping existing primary key constraint from system_users...")
    if _constraint_exists("system_users_pkey", "system_users"):
        op.drop_constraint("system_users_pkey", "system_users", type_="primary")

    # Step 4: Rename user_id to id if needed
    if _column_exists("system_users", "user_id") and not _column_exists("system_users", "id"):
        print("  Renaming system_users.user_id to system_users.id...")
        op.alter_column("system_users", "user_id", new_column_name="id")

    # Step 5: Create composite primary key (account_id, id)
    print("  Creating composite primary key (account_id, id)...")
    op.create_primary_key("system_users_pkey", "system_users", ["account_id", "id"])

    # Step 6: Recreate foreign key constraints with composite keys
    print("  Recreating foreign key constraints...")
    if not _constraint_exists("fk_system_user_role_system_user", "system_user_roles"):
        op.create_foreign_key(
            "fk_system_user_role_system_user",
            "system_user_roles",
            "system_users",
            ["account_id", "system_user_id"],
            ["account_id", "id"],
            ondelete="CASCADE",
        )

    # Create foreign key to roles table for system_user_roles
    if not _constraint_exists("fk_system_user_role_role", "system_user_roles"):
        op.create_foreign_key(
            "fk_system_user_role_role",
            "system_user_roles",
            "roles",
            ["account_id", "role_id"],
            ["account_id", "id"],
            ondelete="CASCADE",
        )

    # Step 7: Add system_user_id column to audit_logs
    if not _column_exists("audit_logs", "system_user_id"):
        print("Adding system_user_id column to audit_logs...")
        op.add_column("audit_logs", sa.Column("system_user_id", sa.Integer(), nullable=True))

    # Step 8: Create composite foreign key (account_id, system_user_id) to system_users (account_id, id)
    print("Creating foreign key constraint for audit_logs...")
    if not _constraint_exists("fk_audit_log_system_user", "audit_logs"):
        op.create_foreign_key(
            "fk_audit_log_system_user",
            "audit_logs",
            "system_users",
            ["account_id", "system_user_id"],
            ["account_id", "id"],
            ondelete="SET NULL",
        )


def downgrade() -> None:
    """Revert system_user_id addition and system_users structure changes"""

    # Step 1: Drop foreign key constraint from audit_logs
    print("Dropping foreign key constraint from audit_logs...")
    if _constraint_exists("fk_audit_log_system_user", "audit_logs"):
        op.drop_constraint("fk_audit_log_system_user", "audit_logs", type_="foreignkey")

    # Step 2: Drop system_user_id column from audit_logs
    print("Dropping system_user_id column from audit_logs...")
    if _column_exists("audit_logs", "system_user_id"):
        op.drop_column("audit_logs", "system_user_id")

    # Step 3: Revert system_users table structure
    print("Reverting system_users table structure...")

    # Drop foreign key constraints that reference system_users
    existing_fks = ["fk_system_user_role_system_user", "fk_system_user_role_role"]
    for fk_name in existing_fks:
        if _constraint_exists(fk_name, "system_user_roles"):
            op.drop_constraint(fk_name, "system_user_roles", type_="foreignkey")

    # Drop composite primary key from system_users
    if _constraint_exists("system_users_pkey", "system_users"):
        op.drop_constraint("system_users_pkey", "system_users", type_="primary")

    # Revert column name change if needed
    if _column_exists("system_users", "id") and not _column_exists("system_users", "user_id"):
        print("  Reverting system_users.id back to system_users.user_id...")
        op.alter_column("system_users", "id", new_column_name="user_id")

    # Create original simple primary key
    print("  Creating original primary key (user_id)...")
    op.create_primary_key("system_users_pkey", "system_users", ["user_id"])

    # Step 4: Revert system_user_roles table structure
    print("Reverting system_user_roles table structure...")

    # Drop composite primary key from system_user_roles
    if _constraint_exists("system_user_roles_pkey", "system_user_roles"):
        op.drop_constraint("system_user_roles_pkey", "system_user_roles", type_="primary")

    # Rename system_user_id back to user_id if needed
    if _column_exists("system_user_roles", "system_user_id") and not _column_exists("system_user_roles", "user_id"):
        op.alter_column("system_user_roles", "system_user_id", new_column_name="user_id")

    # Drop account_id column if it exists
    if _column_exists("system_user_roles", "account_id"):
        op.drop_column("system_user_roles", "account_id")

    # Create original simple primary key for system_user_roles
    op.create_primary_key("system_user_roles_pkey", "system_user_roles", ["user_id", "role_id"])

    # Recreate original foreign key constraints
    print("  Recreating original foreign key constraints...")
    if not _constraint_exists("system_user_roles_user_id_fkey", "system_user_roles"):
        op.create_foreign_key(
            "system_user_roles_user_id_fkey",
            "system_user_roles",
            "system_users",
            ["user_id"],
            ["user_id"],
            ondelete="CASCADE",
        )

    if not _constraint_exists("system_user_roles_role_id_fkey", "system_user_roles"):
        op.create_foreign_key(
            "system_user_roles_role_id_fkey",
            "system_user_roles",
            "roles",
            ["role_id"],
            ["role_id"],
            ondelete="CASCADE",
        )