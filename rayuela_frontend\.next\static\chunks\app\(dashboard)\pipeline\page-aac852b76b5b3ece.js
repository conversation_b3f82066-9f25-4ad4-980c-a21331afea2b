(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3001],{285:(e,s,a)=>{"use strict";a.d(s,{Button:()=>l,r:()=>o});var t=a(5155),r=a(2115),n=a(9708),i=a(2085),c=a(9434);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-body-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border focus-visible:border-ring",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 shadow-xs hover:shadow-soft rayuela-button-hover",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/95 shadow-xs hover:shadow-soft rayuela-button-hover",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80 shadow-xs hover:shadow-soft rayuela-button-hover",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90 shadow-xs hover:shadow-soft rayuela-button-hover",ghost:"hover:bg-accent hover:text-accent-foreground active:bg-accent/80 rayuela-button-hover",link:"text-primary underline-offset-4 hover:underline active:text-primary/80 rayuela-button-hover"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-lg px-3 text-caption-lg",lg:"h-11 rounded-lg px-8 text-body-lg",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=r.forwardRef((e,s)=>{let{className:a,variant:r,size:i,asChild:l=!1,...d}=e,u=l?n.DX:"button";return(0,t.jsx)(u,{className:(0,c.cn)(o({variant:r,size:i,className:a})),ref:s,...d})});l.displayName="Button"},4944:(e,s,a)=>{"use strict";a.d(s,{k:()=>c});var t=a(5155),r=a(2115),n=a(4472),i=a(9434);let c=r.forwardRef((e,s)=>{let{className:a,value:r,...c}=e;return(0,t.jsx)(n.bL,{ref:s,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...c,children:(0,t.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});c.displayName=n.bL.displayName},5365:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>o,TN:()=>d,XL:()=>l});var t=a(5155),r=a(2115),n=a(2085),i=a(9434);let c=(0,n.F)("relative w-full rounded-lg border p-4 transition-all hover:shadow-sm [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground border-border",destructive:"border-destructive/50 text-destructive bg-destructive/5 hover:bg-destructive/10 [&>svg]:text-destructive",success:"border-success/50 text-success bg-success-light hover:bg-success/10 [&>svg]:text-success",warning:"border-warning/50 text-warning bg-warning-light hover:bg-warning/10 [&>svg]:text-warning",info:"border-info/50 text-info bg-info-light hover:bg-info/10 [&>svg]:text-info"}},defaultVariants:{variant:"default"}}),o=r.forwardRef((e,s)=>{let{className:a,variant:r,...n}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(c({variant:r}),a),...n})});o.displayName="Alert";let l=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",a),...r})});l.displayName="AlertTitle";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",a),...r})});d.displayName="AlertDescription"},5447:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(5155),r=a(2115),n=a(6695),i=a(6126),c=a(285),o=a(8856),l=a(5365),d=a(4944),u=a(3314),m=a(3904),x=a(5339),h=a(9376),g=a(9676),f=a(4213),v=a(5487),p=a(381),j=a(1539),b=a(5690),N=a(9869),w=a(3008),y=a(3439),E=a(6874),_=a.n(E),A=a(8338);let M=async()=>(await new Promise(e=>setTimeout(e,1e3)),{id:123,status:"PROCESSING",created_at:new Date().toISOString(),started_at:new Date().toISOString(),progress:75,model_name:"Recommendation Model v2.1"}),B=async()=>(await new Promise(e=>setTimeout(e,800)),{id:456,status:"COMPLETED",created_at:new Date(Date.now()-36e5).toISOString(),records_processed:{users:1250,products:5430,interactions:15670}}),k=async()=>(await new Promise(e=>setTimeout(e,600)),[{id:1,name:"Recommendation Model",version:"v2.0",training_date:new Date(Date.now()-6048e5).toISOString(),status:"ACTIVE",performance_metrics:{accuracy:.85,precision:.82}},{id:2,name:"Trending Model",version:"v1.5",training_date:new Date(Date.now()-12096e5).toISOString(),status:"ACTIVE"}]);function S(){let[e,s]=(0,r.useState)(null),[a,E]=(0,r.useState)(null),[S,I]=(0,r.useState)([]),[D,P]=(0,r.useState)(!0),[R,T]=(0,r.useState)(null);return((0,r.useEffect)(()=>{(async()=>{try{P(!0),T(null);let[e,a,t]=await Promise.all([M(),B(),k()]);s(e),E(a),I(t)}catch(e){T("Error loading pipeline data"),console.error("Error loading pipeline data:",e)}finally{P(!1)}})()},[]),D)?(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,t.jsx)(o.E,{className:"h-8 w-64 mb-2"}),(0,t.jsx)(o.E,{className:"h-4 w-96"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(o.E,{className:"h-6 w-48"}),(0,t.jsx)(o.E,{className:"h-4 w-32"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(o.E,{className:"h-20 w-full"})})]},s))})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsx)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-3",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 text-primary"}),"Pipeline & Modelos"]}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2",children:"Monitorea tus pipelines de datos y modelos de recomendaci\xf3n"})]}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsxs)(c.Button,{variant:"outline",size:"sm",onClick:()=>window.location.reload(),children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Actualizar"]})})]})}),R&&(0,t.jsxs)(l.Fc,{variant:"destructive",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(l.XL,{children:"Error"}),(0,t.jsx)(l.TN,{children:R})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6",children:[(0,t.jsxs)(n.Zp,{className:"col-span-1 xl:col-span-1",children:[(0,t.jsxs)(n.aR,{className:"pb-3",children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-purple-500"}),"\xdaltimo Entrenamiento"]}),(0,t.jsx)(n.BT,{children:"Estado del entrenamiento m\xe1s reciente"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Job #",e.id]}),(0,A.KC)(e.status)]}),e.model_name&&(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:e.model_name}),"PROCESSING"===e.status&&e.progress&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{children:"Progreso"}),(0,t.jsxs)("span",{children:[e.progress,"%"]})]}),(0,t.jsx)(d.k,{value:e.progress,className:"h-2"})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Inicio: ",(0,w.GP)(new Date(e.created_at),"d 'de' MMM, HH:mm",{locale:y.es})]}),"FAILED"===e.status&&e.error_message&&(0,t.jsxs)(l.Fc,{variant:"destructive",className:"mt-3",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(l.TN,{className:"text-xs",children:e.error_message})]})]}):(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-4",children:[(0,t.jsx)(h.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,t.jsx)("p",{className:"text-sm",children:"No hay entrenamientos recientes"})]}),(0,t.jsx)(c.Button,{variant:"outline",size:"sm",className:"w-full mt-4",asChild:!0,children:(0,t.jsxs)(_(),{href:"/pipeline/training-jobs",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Ver Historial"]})})]})]}),(0,t.jsxs)(n.Zp,{className:"col-span-1 xl:col-span-1",children:[(0,t.jsxs)(n.aR,{className:"pb-3",children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-green-500"}),"Ingesta de Datos Reciente"]}),(0,t.jsx)(n.BT,{children:"\xdaltimo proceso de carga de datos"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Job #",a.id]}),(0,A.KC)(a.status)]}),a.records_processed&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-xs font-medium text-muted-foreground",children:"Registros procesados:"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-1 text-sm",children:[a.records_processed.users&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Usuarios:"}),(0,t.jsx)("span",{className:"font-medium",children:a.records_processed.users.toLocaleString()})]}),a.records_processed.products&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Productos:"}),(0,t.jsx)("span",{className:"font-medium",children:a.records_processed.products.toLocaleString()})]}),a.records_processed.interactions&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Interacciones:"}),(0,t.jsx)("span",{className:"font-medium",children:a.records_processed.interactions.toLocaleString()})]})]})]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:(0,w.GP)(new Date(a.created_at),"d 'de' MMM, HH:mm",{locale:y.es})})]}):(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-4",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,t.jsx)("p",{className:"text-sm",children:"No hay ingestas recientes"})]}),(0,t.jsx)(c.Button,{variant:"outline",size:"sm",className:"w-full mt-4",asChild:!0,children:(0,t.jsxs)(_(),{href:"/pipeline/ingestion-jobs",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Ver Historial"]})})]})]}),(0,t.jsxs)(n.Zp,{className:"col-span-1 xl:col-span-1",children:[(0,t.jsxs)(n.aR,{className:"pb-3",children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-blue-500"}),"Modelos Activos"]}),(0,t.jsx)(n.BT,{children:"Modelos actualmente en producci\xf3n"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[S.length>0?(0,t.jsxs)("div",{className:"space-y-3",children:[S.slice(0,2).map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-3 space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.name}),(0,t.jsxs)(i.E,{variant:"success",className:"text-xs",children:["v",e.version]})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Entrenado: ",(0,w.GP)(new Date(e.training_date),"d MMM yyyy",{locale:y.es})]}),e.performance_metrics&&(0,t.jsx)("div",{className:"flex gap-2 text-xs",children:Object.entries(e.performance_metrics).map(e=>{let[s,a]=e;return(0,t.jsxs)("span",{className:"bg-muted px-2 py-1 rounded",children:[s,": ",(100*a).toFixed(1),"%"]},s)})})]},e.id)),S.length>2&&(0,t.jsxs)("div",{className:"text-xs text-muted-foreground text-center",children:["+",S.length-2," modelos m\xe1s"]})]}):(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-4",children:[(0,t.jsx)(v.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,t.jsx)("p",{className:"text-sm",children:"No hay modelos activos"})]}),(0,t.jsx)(c.Button,{variant:"outline",size:"sm",className:"w-full mt-4",asChild:!0,children:(0,t.jsxs)(_(),{href:"/models",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Gestionar Modelos"]})})]})]}),(0,t.jsxs)(n.Zp,{className:"col-span-1 xl:col-span-1",children:[(0,t.jsxs)(n.aR,{className:"pb-3",children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-yellow-500"}),"Acciones R\xe1pidas"]}),(0,t.jsx)(n.BT,{children:"Operaciones comunes del pipeline"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-3",children:[(0,t.jsxs)(c.Button,{size:"sm",className:"w-full justify-start",disabled:!0,children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Iniciar Nuevo Entrenamiento"]}),(0,t.jsxs)(c.Button,{variant:"outline",size:"sm",className:"w-full justify-start",disabled:!0,children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Subir Datos"]}),(0,t.jsxs)(c.Button,{variant:"outline",size:"sm",className:"w-full justify-start",disabled:!0,children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Invalidar Cach\xe9"]}),(0,t.jsxs)(l.Fc,{className:"mt-4",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(l.TN,{className:"text-xs",children:"Las acciones del pipeline estar\xe1n disponibles pr\xf3ximamente. Por ahora puedes monitorear el estado actual."})]})]})]})]}),(0,t.jsxs)(l.Fc,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(l.XL,{children:"Informaci\xf3n sobre el Pipeline"}),(0,t.jsx)(l.TN,{children:(0,t.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,t.jsx)("p",{children:"Esta secci\xf3n te permite monitorear el estado de tus procesos de machine learning:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Entrenamientos:"})," Seguimiento de jobs de entrenamiento de modelos"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Ingesta:"})," Monitoreo de procesos de carga de datos"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Modelos:"})," Estado de modelos activos en producci\xf3n"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"M\xe9tricas:"})," Rendimiento y estado de salud del pipeline"]})]}),(0,t.jsxs)("p",{className:"mt-2 text-muted-foreground",children:[(0,t.jsx)("strong",{children:"Pr\xf3ximamente:"})," Interfaces para iniciar entrenamientos, gestionar datos y configurar par\xe1metros de modelo."]})]})})]})]})}},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>c});var t=a(5155);a(2115);var r=a(2085),n=a(9434);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,n.cn)(i({variant:a}),s),...r})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>o,Wu:()=>l,ZB:()=>c,Zp:()=>n,aR:()=>i,wL:()=>d});var t=a(5155);a(2115);var r=a(9434);function n(e){var s;let{className:a,elevation:n="soft",...i}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border",null!=(s=({none:"shadow-none",sm:"shadow-sm",soft:"shadow-soft",medium:"shadow-medium",glow:"shadow-glow"})[n])?s:"shadow-soft","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",a),...i})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("text-subheading rayuela-accent",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-caption",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...a})}},7867:(e,s,a)=>{Promise.resolve().then(a.bind(a,5447))},8338:(e,s,a)=>{"use strict";a.d(s,{KC:()=>h,Yq:()=>g,ZV:()=>m,a3:()=>f,cR:()=>x,z3:()=>u});var t=a(5155);a(2115);var r=a(6126),n=a(646),i=a(3904),c=a(4186),o=a(4861),l=a(5690),d=a(1243);function u(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(s<0?0:s))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][a]}function m(e){return new Intl.NumberFormat().format(e)}function x(e){let s={className:"h-4 w-4"};switch(null==e?void 0:e.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(n.A,{...s,className:"h-4 w-4 text-green-500"});case"running":case"processing":case"in_progress":return(0,t.jsx)(i.A,{...s,className:"h-4 w-4 text-blue-500 animate-spin"});case"pending":case"queued":case"waiting":return(0,t.jsx)(c.A,{...s,className:"h-4 w-4 text-yellow-500"});case"failed":case"error":case"cancelled":return(0,t.jsx)(o.A,{...s,className:"h-4 w-4 text-red-500"});case"starting":case"initializing":return(0,t.jsx)(l.A,{...s,className:"h-4 w-4 text-blue-400"});case"warning":return(0,t.jsx)(d.A,{...s,className:"h-4 w-4 text-amber-500"});default:return(0,t.jsx)(c.A,{...s,className:"h-4 w-4 text-gray-400"})}}function h(e){switch(null==e?void 0:e.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(r.E,{variant:"success",className:"text-xs",children:"Completado"});case"running":case"processing":case"in_progress":return(0,t.jsx)(r.E,{variant:"info",className:"text-xs",children:"En progreso"});case"pending":case"queued":case"waiting":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Pendiente"});case"failed":case"error":case"cancelled":return(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"Fallido"});case"starting":case"initializing":return(0,t.jsx)(r.E,{variant:"secondary",className:"text-xs",children:"Iniciando"});case"warning":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Advertencia"});default:return(0,t.jsx)(r.E,{variant:"outline",className:"text-xs",children:"Desconocido"})}}function g(e){let s=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!e)return"No disponible";try{let a=new Date(e),t={year:"numeric",month:"long",day:"numeric",...s&&{hour:"2-digit",minute:"2-digit"}};return new Intl.DateTimeFormat("es-ES",t).format(a)}catch(e){return console.error("Error al formatear fecha:",e),"Formato de fecha inv\xe1lido"}}function f(e){let s=Math.floor(e/1e3),a=Math.floor(s/60),t=Math.floor(a/60),r=Math.floor(t/24);return r>0?"".concat(r,"d ").concat(t%24,"h ").concat(a%60,"m"):t>0?"".concat(t,"h ").concat(a%60,"m ").concat(s%60,"s"):a>0?"".concat(a,"m ").concat(s%60,"s"):"".concat(s,"s")}},8856:(e,s,a)=>{"use strict";a.d(s,{E:()=>n});var t=a(5155),r=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-lg bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-shimmer",s),...a})}},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>n});var t=a(2596),r=a(9688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[9352,6874,4214,4345,8441,1684,7358],()=>s(7867)),_N_E=e.O()}]);