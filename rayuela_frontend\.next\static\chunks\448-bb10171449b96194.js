"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[448],{3052:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3109:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("trending-up",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},4788:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6176:(e,t,r)=>{r.d(t,{UC:()=>X,B8:()=>Q,bL:()=>J,l9:()=>W});var n=r(2115),o=r(5185),a=r(6081),i=r(7328),l=r(6101),u=r(1285);r(7650);var s=r(9708),c=r(5155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(o?r:t,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),f=r(9033),p=r(5845),v=r(4315),m="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,y,g]=(0,i.N)(h),[x,A]=(0,a.A)(h,[g]),[R,j]=x(h),F=n.forwardRef((e,t)=>(0,c.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(I,{...e,ref:t})})}));F.displayName=h;var I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:u,currentTabStopId:s,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:g,onEntryFocus:x,preventScrollOnEntryFocus:A=!1,...j}=e,F=n.useRef(null),I=(0,l.s)(t,F),k=(0,v.jH)(u),[C,D]=(0,p.i)({prop:s,defaultProp:null!=w?w:null,onChange:g,caller:h}),[E,N]=n.useState(!1),K=(0,f.c)(x),L=y(r),P=n.useRef(!1),[S,M]=n.useState(0);return n.useEffect(()=>{let e=F.current;if(e)return e.addEventListener(m,K),()=>e.removeEventListener(m,K)},[K]),(0,c.jsx)(R,{scope:r,orientation:a,dir:k,loop:i,currentTabStopId:C,onItemFocus:n.useCallback(e=>D(e),[D]),onItemShiftTab:n.useCallback(()=>N(!0),[]),onFocusableItemAdd:n.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>M(e=>e-1),[]),children:(0,c.jsx)(d.div,{tabIndex:E||0===S?-1:0,"data-orientation":a,...j,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{P.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!P.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(m,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),A)}}P.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),k="RovingFocusGroupItem",C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,children:s,...f}=e,p=(0,u.B)(),v=l||p,m=j(k,r),b=m.currentTabStopId===v,h=y(r),{onFocusableItemAdd:g,onFocusableItemRemove:x}=m;return n.useEffect(()=>{if(a)return g(),()=>x()},[a,g,x]),(0,c.jsx)(w.ItemSlot,{scope:r,id:v,focusable:a,active:i,children:(0,c.jsx)(d.span,{tabIndex:b?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return D[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>T(r))}}),children:"function"==typeof s?s({isCurrentTabStop:b}):s})})});C.displayName=k;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var E=r(8905),N=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(o?r:t,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),K="Tabs",[L,P]=(0,a.A)(K,[A]),S=A(),[M,_]=L(K),B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:s="automatic",...d}=e,f=(0,v.jH)(l),[m,b]=(0,p.i)({prop:n,onChange:o,defaultProp:null!=a?a:"",caller:K});return(0,c.jsx)(M,{scope:r,baseId:(0,u.B)(),value:m,onValueChange:b,orientation:i,dir:f,activationMode:s,children:(0,c.jsx)(N.div,{dir:f,"data-orientation":i,...d,ref:t})})});B.displayName=K;var G="TabsList",U=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=_(G,r),i=S(r);return(0,c.jsx)(F,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:n,children:(0,c.jsx)(N.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});U.displayName=G;var V="TabsTrigger",$=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,l=_(V,r),u=S(r),s=z(l.baseId,n),d=O(l.baseId,n),f=n===l.value;return(0,c.jsx)(C,{asChild:!0,...u,focusable:!a,active:f,children:(0,c.jsx)(N.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:s,...i,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;f||a||!e||l.onValueChange(n)})})})});$.displayName=V;var H="TabsContent",q=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:i,...l}=e,u=_(H,r),s=z(u.baseId,o),d=O(u.baseId,o),f=o===u.value,p=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,c.jsx)(E.C,{present:a||f,children:r=>{let{present:n}=r;return(0,c.jsx)(N.div,{"data-state":f?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":s,hidden:!n,id:d,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&i})}})});function z(e,t){return"".concat(e,"-trigger-").concat(t)}function O(e,t){return"".concat(e,"-content-").concat(t)}q.displayName=H;var J=B,Q=U,W=$,X=q}}]);