exports.id=5320,exports.ids=[5320],exports.modules={6007:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>J});var t=r(60687),s=r(43210),n=r(16189),i=r(44957),o=r(29523),l=r(85814),c=r.n(l),d=r(4780);function m({variant:e="default",className:a,linkClassName:r,href:s="/"}){let n=(0,t.jsx)("div",{className:(0,d.cn)("font-bold transition-all duration-300 ease-in-out rayuela-accent","marketing"===e?"text-display tracking-extra-tight":"text-heading tracking-tight","hover:scale-105",a),children:"Rayuela.ai"});return s?(0,t.jsx)(c(),{href:s,className:(0,d.cn)("rayuela-interactive hover:opacity-90 rayuela-focus-ring rounded-lg p-1 -m-1",r),children:n}):n}var u=r(37276);function x(){let{logout:e,user:a}=(0,i.A)();return(0,t.jsx)("header",{className:"bg-card shadow-sm border-b border-border h-16 shrink-0",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-full px-4",children:[(0,t.jsxs)(u.YJ,{children:[(0,t.jsx)(m,{className:"text-foreground hidden md:block"}),(0,t.jsx)("span",{className:"text-heading text-foreground",children:"Dashboard"})]}),(0,t.jsx)(u.YJ,{spacing:"normal",children:a&&(0,t.jsx)(o.Button,{onClick:e,variant:"outline",size:"sm",children:"Logout"})})]})})}var h=r(32192),f=r(19959),g=r(10510),p=r(58559),v=r(25541),b=r(85778),j=r(78200),y=r(84027),N=r(82080),w=r(25334),k=r(58887),A=r(56748),z=r(53520);let C=[{href:"/dashboard",label:"Dashboard",icon:(0,t.jsx)(z.mm,{icon:h.A,size:"sm",context:"navigation",className:"rayuela-icon-accent"})},{href:"/api-keys",label:"API Keys",icon:(0,t.jsx)(z.mm,{icon:f.A,size:"sm",context:"navigation",className:"rayuela-icon-accent"})},{href:"/pipeline",label:"Pipeline",icon:(0,t.jsx)(z.mm,{icon:g.A,size:"sm",context:"navigation",className:"rayuela-icon-primary"})},{href:"/usage",label:"Usage",icon:(0,t.jsx)(z.mm,{icon:p.A,size:"sm",context:"navigation",className:"rayuela-icon-progress"})},{href:"/recommendation-metrics",label:"Metrics",icon:(0,t.jsx)(z.mm,{icon:v.A,size:"sm",context:"navigation",className:"rayuela-icon-progress"})},{href:"/billing",label:"Billing",icon:(0,t.jsx)(z.mm,{icon:b.A,size:"sm",context:"navigation",className:"rayuela-icon-accent"})},{href:"/models",label:"Models",icon:(0,t.jsx)(z.mm,{icon:j.A,size:"sm",context:"navigation",className:"rayuela-icon-exploration"})},{href:"/settings",label:"Settings",icon:(0,t.jsx)(z.mm,{icon:y.A,size:"sm",context:"navigation",className:"rayuela-icon-accent"})},{href:"https://docs.rayuela.ai",label:"Docs",external:!0,icon:(0,t.jsx)(z.mm,{icon:N.A,size:"sm",context:"navigation",className:"rayuela-icon-exploration"})}];function _(){return(0,t.jsxs)("aside",{className:"w-64 bg-sidebar text-sidebar-foreground border-r border-sidebar-border p-4 flex flex-col h-full shrink-0 rayuela-subtle-gradient",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)(m,{href:"/dashboard",className:"text-sidebar-foreground"})}),(0,t.jsx)("nav",{children:(0,t.jsx)(u.BJ,{spacing:"sm",className:"rayuela-stagger-2",children:C.map(e=>(0,t.jsx)(o.Button,{variant:"ghost",className:"justify-start text-left text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground px-3 rayuela-interactive group",asChild:!0,children:e.external?(0,t.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"w-full",children:(0,t.jsx)(u.hf,{icon:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsx)(z.mm,{icon:w.A,size:"xs",context:"navigation",className:"opacity-60"})]}),className:"w-full justify-between",children:e.label})}):(0,t.jsx)(c(),{href:e.href,className:"w-full",children:(0,t.jsx)(u.hf,{icon:e.icon,children:e.label})})},e.href))})}),(0,t.jsx)("div",{className:"flex-grow"}),(0,t.jsxs)("div",{className:"mt-6 pt-4 border-t border-sidebar-border/50 rayuela-exploration-glow",children:[(0,t.jsx)(o.Button,{variant:"ghost",size:"sm",className:"w-full justify-start text-sidebar-foreground/80 hover:text-sidebar-foreground hover:bg-sidebar-accent/50 rayuela-interactive group",asChild:!0,children:(0,t.jsx)("a",{href:"mailto:<EMAIL>?subject=Feedback%20sobre%20Rayuela.ai",target:"_blank",rel:"noopener noreferrer",children:(0,t.jsx)(u.hf,{icon:(0,t.jsx)(z.mm,{icon:k.A,size:"sm",context:"navigation",className:"rayuela-icon-exploration"}),children:"Feedback"})})}),(0,t.jsx)("div",{className:"mt-3 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs text-sidebar-foreground/60 group",children:[(0,t.jsx)(z.mm,{icon:A.A,size:"xs",context:"navigation",className:"rayuela-icon-exploration animate-pulse"}),(0,t.jsx)("span",{children:"Explora Rayuela.ai"})]})})]})]})}var B=r(91821),E=r(52581),R=r(11860),P=r(41550);function D({onClose:e}){let{requestNewVerificationEmail:a}=(0,i.A)(),[r,n]=(0,s.useState)(!1),[l,c]=(0,s.useState)(!1);if(l)return null;let d=async()=>{n(!0);try{await a()&&E.o.success("Email de verificaci\xf3n enviado. Por favor, revisa tu bandeja de entrada.")}catch(a){console.error("Error al reenviar email de verificaci\xf3n:",a);let e=a instanceof Error?a.message:"Error al reenviar email de verificaci\xf3n.";E.o.error(e)}finally{n(!1)}};return(0,t.jsxs)(B.Fc,{variant:"warning",className:"mb-6 relative",children:[(0,t.jsxs)(B.XL,{className:"flex items-center",children:["Verificaci\xf3n de email pendiente",(0,t.jsxs)(o.Button,{variant:"ghost",size:"sm",className:"p-0 h-auto absolute top-2 right-2 text-amber-500 hover:text-amber-700 hover:bg-transparent",onClick:()=>{c(!0),e&&e()},children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Cerrar"})]})]}),(0,t.jsxs)(B.TN,{children:[(0,t.jsx)("p",{children:"Tu email a\xfan no ha sido verificado. Por favor, verifica tu email para acceder a todas las funcionalidades."}),(0,t.jsxs)(o.Button,{variant:"outline",size:"sm",className:"mt-2 border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800",onClick:d,disabled:r,children:[(0,t.jsx)(P.A,{className:"mr-2 h-4 w-4"}),r?"Enviando...":"Reenviar email de verificaci\xf3n"]})]})]})}function J({children:e}){let{user:a,isLoading:r}=(0,i.A)();(0,n.useRouter)();let[o,l]=(0,s.useState)(!1);return r?(0,t.jsx)("div",{children:"Cargando..."}):a?(0,t.jsxs)("div",{className:"flex h-screen bg-background",children:[(0,t.jsx)(_,{}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,t.jsx)(x,{}),(0,t.jsxs)("main",{className:"flex-1 overflow-x-hidden overflow-y-auto bg-muted/30 p-6",children:[o&&(0,t.jsx)(D,{onClose:()=>l(!1)}),e]})]})]}):null}},37276:(e,a,r)=>{"use strict";r.d(a,{BJ:()=>o,YJ:()=>l,hf:()=>i});var t=r(60687);r(43210);var s=r(4780);let n={iconText:{sm:"gap-1.5",md:"gap-2",lg:"gap-3"},elementGroup:{tight:"gap-1",normal:"gap-2",loose:"gap-3"},stack:{xs:"space-y-1",sm:"space-y-1.5",md:"space-y-2",lg:"space-y-3",xl:"space-y-4"}};function i({icon:e,children:a,size:r="md",align:i="center",className:o,...l}){return(0,t.jsxs)("div",{className:(0,s.cn)("flex",n.iconText[r],{start:"items-start",center:"items-center",end:"items-end"}[i],o),...l,children:[(0,t.jsx)("span",{className:"shrink-0 flex items-center",children:e}),(0,t.jsx)("span",{className:"min-w-0",children:a})]})}function o({spacing:e="md",className:a,children:r,...i}){return(0,t.jsx)("div",{className:(0,s.cn)(n.stack[e],a),...i,children:r})}function l({spacing:e="normal",align:a="center",wrap:r=!1,className:i,children:o,...l}){return(0,t.jsx)("div",{className:(0,s.cn)("flex",n.elementGroup[e],{start:"items-start",center:"items-center",end:"items-end",stretch:"items-stretch"}[a],r&&"flex-wrap",i),...l,children:o})}},44493:(e,a,r)=>{"use strict";r.d(a,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>d});var t=r(60687);r(43210);var s=r(4780);function n({className:e,elevation:a="soft",...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border",{none:"shadow-none",sm:"shadow-sm",soft:"shadow-soft",medium:"shadow-medium",glow:"shadow-glow"}[a]??"shadow-soft","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",e),...r})}function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function o({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("text-subheading rayuela-accent",e),...a})}function l({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-caption",e),...a})}function c({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...a})}function d({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...a})}},53520:(e,a,r)=>{"use strict";r.d(a,{mm:()=>o,vK:()=>c});var t=r(60687);r(43210);var s=r(4780);let n={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",xl:"h-8 w-8","2xl":"h-12 w-12"},i={success:"text-success",warning:"text-warning",error:"text-destructive",info:"text-info",primary:"text-primary",secondary:"text-secondary-foreground",muted:"text-muted-foreground",interactive:"text-primary hover:text-primary/80",neutral:"text-foreground",subtle:"text-muted-foreground",metric:"text-primary",action:"text-primary",navigation:"text-muted-foreground hover:text-foreground"};function o({icon:e,size:a="md",context:r="neutral",className:o,"aria-label":l,"aria-hidden":c=!l,...d}){return(0,t.jsx)(e,{className:(0,s.cn)(n[a],i[r],"shrink-0",o),"aria-label":l,"aria-hidden":c,...d})}let l={tight:"gap-1",normal:"gap-2",loose:"gap-3"};function c({icon:e,children:a,size:r="sm",context:n="neutral",iconPosition:i="left",spacing:c="normal",className:d}){let m=(0,t.jsx)(o,{icon:e,size:r,context:n,"aria-hidden":!0});return(0,t.jsxs)("span",{className:(0,s.cn)("inline-flex items-center",l[c],d),children:["left"===i&&m,a,"right"===i&&m]})}},57675:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx","default")},75426:(e,a,r)=>{Promise.resolve().then(r.bind(r,57675))},85726:(e,a,r)=>{"use strict";r.d(a,{E:()=>n});var t=r(60687),s=r(4780);function n({className:e,...a}){return(0,t.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-lg bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-shimmer",e),...a})}},93578:(e,a,r)=>{Promise.resolve().then(r.bind(r,6007))}};