(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6337],{10:(e,a,s)=>{"use strict";s.d(a,{$v:()=>g,EO:()=>x,Lt:()=>o,Rx:()=>j,Zr:()=>f,ck:()=>u,r7:()=>h,tv:()=>c,wd:()=>p});var t=s(5155),r=s(2115),n=s(2278),i=s(9434),l=s(285);let o=n.bL,c=n.l9,d=n.ZL,m=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.hJ,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r,ref:a})});m.displayName=n.hJ.displayName;let x=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsxs)(d,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(n.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...r})]})});x.displayName=n.UC.displayName;let p=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...s})};p.displayName="AlertDialogHeader";let u=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};u.displayName="AlertDialogFooter";let h=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold",s),...r})});h.displayName=n.hE.displayName;let g=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});g.displayName=n.VY.displayName;let j=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.rc,{ref:a,className:(0,i.cn)((0,l.r)(),s),...r})});j.displayName=n.rc.displayName;let f=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.ZD,{ref:a,className:(0,i.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",s),...r})});f.displayName=n.ZD.displayName},1538:(e,a,s)=>{"use strict";s.d(a,{default:()=>V});var t=s(5155),r=s(2115),n=s(5379),i=s(5731),l=s(285),o=s(5365),c=s(6695),d=s(8856),m=s(9803),x=s(5040),p=s(2713),u=s(4416),h=s(1586),g=s(6874),j=s.n(g),f=s(3453),y=s(5339),b=s(9545),N=s(6102);function v(e){let{className:a=""}=e,[s,n]=(0,r.useState)("loading"),[i,l]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/health");if(e.ok){let a=await e.json();n("healthy"===a.status?"operational":"degraded")}else n("degraded")}catch(e){console.error("Error checking API status:",e),n("outage")}l(new Date)};e();let a=setInterval(e,3e5);return()=>clearInterval(a)},[]);let o=(()=>{switch(s){case"operational":return{label:"API Operativa",icon:(0,t.jsx)(f.A,{className:"h-4 w-4 text-green-500"}),description:"Todos los sistemas funcionando correctamente",className:"text-green-600 dark:text-green-400"};case"degraded":return{label:"Rendimiento Degradado",icon:(0,t.jsx)(y.A,{className:"h-4 w-4 text-amber-500"}),description:"Algunos servicios pueden experimentar lentitud",className:"text-amber-600 dark:text-amber-400"};case"outage":return{label:"Servicio Interrumpido",icon:(0,t.jsx)(b.A,{className:"h-4 w-4 text-red-500"}),description:"Estamos experimentando problemas t\xe9cnicos",className:"text-red-600 dark:text-red-400"};default:return{label:"Verificando estado...",icon:(0,t.jsx)(y.A,{className:"h-4 w-4 text-gray-400 animate-pulse"}),description:"Obteniendo informaci\xf3n del estado de la API",className:"text-gray-600 dark:text-gray-400"}}})(),c=i?i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):"";return(0,t.jsx)(N.Bc,{children:(0,t.jsxs)(N.m_,{children:[(0,t.jsx)(N.k$,{asChild:!0,children:(0,t.jsxs)("div",{className:"flex items-center gap-1.5 ".concat(a),children:[o.icon,(0,t.jsx)("span",{className:"text-xs font-medium ".concat(o.className),children:o.label})]})}),(0,t.jsx)(N.ZI,{side:"bottom",children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("p",{className:"font-medium",children:o.label}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o.description}),i&&(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["\xdaltima verificaci\xf3n: ",c]})]})})]})})}var k=s(7262),w=s(9621),A=s(5196),E=s(4357),_=s(4213),I=s(3311),C=s(3904),P=s(646),S=s(3786),T=s(2138),D=s(6126),K=s(2881),O=s(5524);let R=e=>{let{title:a,language:s,code:n,apiKey:i}=e,[o,c]=(0,r.useState)(!1),d=i?n.replace(/YOUR_API_KEY/g,i):n;return(0,t.jsxs)("div",{className:"mt-3 ml-6 border rounded-md bg-gray-50 dark:bg-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(w.A,{className:"h-3 w-3 mr-1 text-gray-500"}),(0,t.jsx)("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:a}),(0,t.jsx)(D.E,{variant:"outline",className:"ml-2 text-[10px] px-1 py-0 h-4",children:s})]}),(0,t.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>{navigator.clipboard.writeText(d).then(()=>{c(!0),setTimeout(()=>c(!1),2e3)}).catch(e=>{console.error("Error al copiar:",e)})},className:"h-6 px-2 text-xs",children:o?(0,t.jsx)(A.A,{className:"h-3 w-3"}):(0,t.jsx)(E.A,{className:"h-3 w-3"})})]}),(0,t.jsx)("pre",{className:"p-3 text-xs overflow-x-auto",children:(0,t.jsx)("code",{className:"text-gray-800 dark:text-gray-200",children:d})})]})},B=e=>{let{item:a,onToggle:s,onRefresh:r,apiKey:n,codeSnippets:i,colorScheme:l}=e,o={blue:{bg:"bg-info-light/50",checkbox:"bg-info border-info",badge:"bg-info-light",link:"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"},purple:{bg:"bg-purple-50/50 dark:bg-purple-900/10",checkbox:"bg-purple-600 border-purple-600",badge:"bg-purple-50 dark:bg-purple-900/30",link:"text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"},amber:{bg:"bg-warning-light/50",checkbox:"bg-warning border-warning",badge:"bg-warning-light",link:"text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300"}}[l];return(0,t.jsxs)("li",{className:"flex items-start p-2 rounded-md transition-colors ".concat(a.completed?o.bg:"hover:bg-gray-50 dark:hover:bg-gray-800/50"),children:[(0,t.jsx)("div",{className:"flex h-5 items-center mr-3",children:(0,t.jsx)(k.S,{id:a.id,checked:a.completed,onCheckedChange:()=>s(a.id),className:a.completed?o.checkbox:""})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[a.icon,(0,t.jsx)("label",{htmlFor:a.id,className:"ml-2 text-sm font-medium ".concat(a.completed?"text-gray-500":"text-gray-900 dark:text-gray-100"),children:a.label}),a.autoDetect&&(0,t.jsx)(N.Bc,{children:(0,t.jsxs)(N.m_,{children:[(0,t.jsx)(N.k$,{asChild:!0,children:(0,t.jsx)(D.E,{variant:"outline",className:"ml-2 text-[10px] px-1 py-0 h-4 ".concat(o.badge),children:"Auto"})}),(0,t.jsx)(N.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:"Este paso se verifica autom\xe1ticamente"})})]})}),a.completed&&(0,t.jsx)(P.A,{className:"h-3.5 w-3.5 ml-2 text-success"})]}),a.docsLink&&(0,t.jsx)(N.Bc,{children:(0,t.jsxs)(N.m_,{children:[(0,t.jsx)(N.k$,{asChild:!0,children:(0,t.jsxs)("a",{href:a.docsLink,className:"text-xs ".concat(o.link," flex items-center"),target:"_blank",rel:"noopener noreferrer",children:[(0,t.jsx)(x.A,{className:"h-3 w-3 mr-1"}),a.docsTitle||"Documentaci\xf3n"]})}),(0,t.jsx)(N.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:a.tooltipContent||"Ver documentaci\xf3n detallada"})})]})})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-0.5 ml-6",children:a.description}),(0,t.jsxs)("div",{className:"mt-2 ml-6 flex items-center",children:[a.isExternal?(0,t.jsxs)("a",{href:a.link,target:"_blank",rel:"noopener noreferrer",className:"text-xs ".concat(o.link," font-medium flex items-center"),children:[(0,t.jsx)(S.A,{className:"h-3 w-3 mr-1"}),"Ver gu\xeda paso a paso"]}):(0,t.jsxs)(j(),{href:a.link,className:"text-xs ".concat(o.link," font-medium flex items-center"),children:[(0,t.jsx)(T.A,{className:"h-3 w-3 mr-1"}),"Ir a ",a.label]}),a.autoDetect&&!a.completed&&(0,t.jsxs)("button",{onClick:r,className:"ml-3 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 flex items-center",title:"Verificar progreso",children:[(0,t.jsx)(C.A,{className:"h-3 w-3 mr-1"}),"Verificar"]})]}),"send_catalog_data"===a.id&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(R,{title:"Cargar productos (cURL)",language:"bash",code:i.catalog.curl,apiKey:n}),(0,t.jsx)(R,{title:"Cargar productos (Python)",language:"python",code:i.catalog.python,apiKey:n}),(0,t.jsx)(R,{title:"Cargar productos (JavaScript)",language:"javascript",code:i.catalog.javascript,apiKey:n})]}),"send_interaction_data"===a.id&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(R,{title:"Cargar interacciones (cURL)",language:"bash",code:i.interactions.curl,apiKey:n}),(0,t.jsx)(R,{title:"Cargar interacciones (Python)",language:"python",code:i.interactions.python,apiKey:n}),(0,t.jsx)(R,{title:"Cargar interacciones (JavaScript)",language:"javascript",code:i.interactions.javascript,apiKey:n})]}),"first_recommendation"===a.id&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(R,{title:"Obtener recomendaciones (cURL)",language:"bash",code:i.recommendations.curl,apiKey:n}),(0,t.jsx)(R,{title:"Obtener recomendaciones (Python)",language:"python",code:i.recommendations.python,apiKey:n}),(0,t.jsx)(R,{title:"Obtener recomendaciones (JavaScript)",language:"javascript",code:i.recommendations.javascript,apiKey:n})]})]})]})},L=e=>{let{className:a}=e;return(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:a,children:[(0,t.jsx)("line",{x1:"10",y1:"6",x2:"21",y2:"6"}),(0,t.jsx)("line",{x1:"10",y1:"12",x2:"21",y2:"12"}),(0,t.jsx)("line",{x1:"10",y1:"18",x2:"21",y2:"18"}),(0,t.jsx)("polyline",{points:"3 6 4 7 6 5"}),(0,t.jsx)("polyline",{points:"3 12 4 13 6 11"}),(0,t.jsx)("polyline",{points:"3 18 4 19 6 17"})]})},z=()=>{let{apiKey:e}=(0,n.As)(),a="http://localhost:8001",s={catalog:{curl:'curl -X POST "'.concat(a,'/ingestion/batch" \\\n  -H "X-API-Key: YOUR_API_KEY" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "products": [\n      {\n        "external_id": "P001",\n        "name": "Smartphone XYZ",\n        "category": "Electronics",\n        "price": 599.99,\n        "description": "Un smartphone de \xfaltima generaci\xf3n",\n        "image_url": "https://ejemplo.com/images/p001.jpg"\n      }\n    ]\n  }\''),python:'import requests\n\nurl = "'.concat(a,'/ingestion/batch"\nheaders = {\n    "X-API-Key": "YOUR_API_KEY",\n    "Content-Type": "application/json"\n}\ndata = {\n    "products": [\n        {\n            "external_id": "P001",\n            "name": "Smartphone XYZ",\n            "category": "Electronics",\n            "price": 599.99,\n            "description": "Un smartphone de \xfaltima generaci\xf3n",\n            "image_url": "https://ejemplo.com/images/p001.jpg"\n        }\n    ]\n}\n\nresponse = requests.post(url, headers=headers, json=data)\nprint(response.json())'),javascript:"const response = await fetch('".concat(a,"/ingestion/batch', {\n  method: 'POST',\n  headers: {\n    'X-API-Key': 'YOUR_API_KEY',\n    'Content-Type': 'application/json'\n  },\n  body: JSON.stringify({\n    products: [\n      {\n        external_id: 'P001',\n        name: 'Smartphone XYZ',\n        category: 'Electronics',\n        price: 599.99,\n        description: 'Un smartphone de \xfaltima generaci\xf3n',\n        image_url: 'https://ejemplo.com/images/p001.jpg'\n      }\n    ]\n  })\n});\n\nconst data = await response.json();\nconsole.log(data);")},interactions:{curl:'curl -X POST "'.concat(a,'/ingestion/batch" \\\n  -H "X-API-Key: YOUR_API_KEY" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "interactions": [\n      {\n        "user_external_id": "U001",\n        "product_external_id": "P001",\n        "interaction_type": "view",\n        "timestamp": "2024-01-15T10:30:00Z"\n      }\n    ]\n  }\''),python:'import requests\nfrom datetime import datetime\n\nurl = "'.concat(a,'/ingestion/batch"\nheaders = {\n    "X-API-Key": "YOUR_API_KEY",\n    "Content-Type": "application/json"\n}\ndata = {\n    "interactions": [\n        {\n            "user_external_id": "U001",\n            "product_external_id": "P001",\n            "interaction_type": "view",\n            "timestamp": datetime.now().isoformat() + "Z"\n        }\n    ]\n}\n\nresponse = requests.post(url, headers=headers, json=data)\nprint(response.json())'),javascript:"const response = await fetch('".concat(a,"/ingestion/batch', {\n  method: 'POST',\n  headers: {\n    'X-API-Key': 'YOUR_API_KEY',\n    'Content-Type': 'application/json'\n  },\n  body: JSON.stringify({\n    interactions: [\n      {\n        user_external_id: 'U001',\n        product_external_id: 'P001',\n        interaction_type: 'view',\n        timestamp: new Date().toISOString()\n      }\n    ]\n  })\n});\n\nconst data = await response.json();\nconsole.log(data);")},recommendations:{curl:'curl -X POST "'.concat(a,'/recommendations/personalized/query" \\\n  -H "X-API-Key: YOUR_API_KEY" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "user_id": 123,\n    "limit": 5,\n    "strategy": "balanced",\n    "model_type": "standard"\n  }\''),python:'import requests\n\nurl = "'.concat(a,'/recommendations/personalized/query"\nheaders = {\n    "X-API-Key": "YOUR_API_KEY",\n    "Content-Type": "application/json"\n}\ndata = {\n    "user_id": 123,\n    "limit": 5,\n    "strategy": "balanced",\n    "model_type": "standard"\n}\n\nresponse = requests.post(url, headers=headers, json=data)\nrecommendations = response.json()\nprint(recommendations)'),javascript:"const response = await fetch('".concat(a,"/recommendations/personalized/query', {\n  method: 'POST',\n  headers: {\n    'X-API-Key': 'YOUR_API_KEY',\n    'Content-Type': 'application/json'\n  },\n  body: JSON.stringify({\n    user_id: 123,\n    limit: 5,\n    strategy: 'balanced',\n    model_type: 'standard'\n  })\n});\n\nconst recommendations = await response.json();\nconsole.log(recommendations);")}},[i,o]=(0,r.useState)(!0),[d,p]=(0,r.useState)(!1),[h,g]=(0,r.useState)([{id:"generate_key",label:"He Guardado mi API Key",description:"Confirma que has guardado tu API Key de forma segura. \uD83E\uDDEA En el Developer Sandbox puedes resetear todo y generar nuevas claves cuando necesites.",icon:(0,t.jsx)(m.A,{className:"h-4 w-4 text-info"}),link:"/api-keys",docsLink:"https://docs.rayuela.ai/authentication",docsTitle:"Gu\xeda de Autenticaci\xf3n",completed:!1,autoDetect:!1,tooltipContent:"En el Developer Sandbox puedes experimentar libremente sin preocuparte por costos. Guarda tu API Key pero recuerda que puedes resetear todo cuando necesites empezar fresh.",category:"setup"},{id:"read_docs",label:"Consultar Documentaci\xf3n",description:"Lee la gu\xeda de inicio r\xe1pido para aprender a usar la API",icon:(0,t.jsx)(x.A,{className:"h-4 w-4 text-success"}),link:"https://docs.rayuela.ai/quickstart",docsLink:"https://docs.rayuela.ai/api-reference",docsTitle:"Referencia de API",isExternal:!0,completed:!1,tooltipContent:"Familiar\xedzate con los conceptos b\xe1sicos y la estructura de la API",category:"setup"},{id:"send_catalog_data",label:"Ingresar Datos de Productos",description:"Usa el endpoint /ingestion/batch para cargar tus productos. C\xf3digo listo para copiar incluido abajo.",icon:(0,t.jsx)(_.A,{className:"h-4 w-4 text-purple-500"}),link:"https://docs.rayuela.ai/guides/data_ingestion_guide",docsLink:"https://docs.rayuela.ai/guides/data_ingestion_guide",docsTitle:"Gu\xeda de Ingesta de Datos",isExternal:!0,completed:!1,autoDetect:!0,tooltipContent:"Carga tus productos usando el endpoint de ingesta por lotes. Los ejemplos de c\xf3digo est\xe1n listos para usar con tu API Key.",category:"data"},{id:"send_interaction_data",label:"Ingresar Datos de Interacciones",description:"Usa el endpoint /ingestion/batch para cargar interacciones usuario-producto. C\xf3digo listo para copiar incluido abajo.",icon:(0,t.jsx)(_.A,{className:"h-4 w-4 text-indigo-500"}),link:"https://docs.rayuela.ai/guides/data_ingestion_guide#interacciones-interactions",docsLink:"https://docs.rayuela.ai/guides/data_ingestion_guide",docsTitle:"Gu\xeda de Ingesta de Datos",isExternal:!0,completed:!1,autoDetect:!0,tooltipContent:"Las interacciones (vistas, compras, clicks) son fundamentales para generar recomendaciones personalizadas. Los ejemplos de c\xf3digo est\xe1n listos para usar.",category:"data"},{id:"train_model",label:"Entrenar Primer Modelo",description:"Inicia el entrenamiento de tu modelo de recomendaci\xf3n",icon:(0,t.jsx)(I.A,{className:"h-4 w-4 text-orange-500"}),link:"https://docs.rayuela.ai/quickstart#entrenamiento-de-modelo",docsLink:"https://docs.rayuela.ai/models",docsTitle:"Gu\xeda de Entrenamiento",isExternal:!0,completed:!1,autoDetect:!0,tooltipContent:"Una vez cargados tus datos, entrena tu primer modelo para comenzar a generar recomendaciones personalizadas.",category:"model"},{id:"first_recommendation",label:"Obtener Recomendaciones",description:"Usa el endpoint /recommendations/personalized/query para obtener recomendaciones. C\xf3digo listo para copiar incluido abajo.",icon:(0,t.jsx)(I.A,{className:"h-4 w-4 text-warning"}),link:"https://docs.rayuela.ai/quickstart#obtenci\xf3n-de-recomendaciones",docsLink:"https://docs.rayuela.ai/recommendations",docsTitle:"Gu\xeda de Recomendaciones",isExternal:!0,completed:!1,autoDetect:!0,tooltipContent:"Aprende a solicitar recomendaciones personalizadas usando el endpoint de consulta. Los ejemplos de c\xf3digo est\xe1n listos para usar con tu API Key.",category:"model"}]);(0,r.useEffect)(()=>{if(j&&v)try{let e=v();if(e&&Object.keys(e).length>0)g(a=>a.map(a=>({...a,completed:!!e[a.id]}))),e.dismissed&&p(!0);else{let e=localStorage.getItem("gettingStartedChecklist");if(e){let a=JSON.parse(e);g(e=>e.map(e=>({...e,completed:!!a[e.id]})))}let a="true"===localStorage.getItem("dismissedChecklist");p(a)}}catch(a){console.error("Error loading checklist state:",a);let e=localStorage.getItem("gettingStartedChecklist");if(e)try{let a=JSON.parse(e);g(e=>e.map(e=>({...e,completed:!!a[e.id]})))}catch(e){console.error("Error parsing saved checklist from localStorage:",e)}}},[]);let{accountData:j,error:f,isLoading:y,refresh:b,getChecklistStatus:v,updateChecklistStatus:k}=(0,n.T4)({revalidateOnFocus:!0,dedupingInterval:5e3,refreshInterval:15e3});(0,n.Qf)({revalidateOnFocus:!0,dedupingInterval:5e3,refreshInterval:15e3}),(0,r.useEffect)(()=>{j&&v&&(async()=>{try{let e=await v();e&&"object"==typeof e&&g(a=>a.map(a=>({...a,completed:!!e[a.id]})));let a="true"===localStorage.getItem("dismissedChecklist");p(a)}catch(a){console.error("Error loading checklist state:",a);let e=localStorage.getItem("gettingStartedChecklist");if(e)try{let a=JSON.parse(e);g(e=>e.map(e=>({...e,completed:a[e.id]||!1})))}catch(e){console.error("Error parsing saved checklist from localStorage:",e)}}})()},[j,v]);let{usageData:w,error:A,isLoading:E,mutate:S}=(0,n.TB)();(0,r.useEffect)(()=>{(j||w)&&R()},[j,w]);let T=async()=>{try{await Promise.all([b(),S()])}catch(e){console.error("Error al actualizar los datos:",e)}},R=async()=>{if(!j||!w)return;let{updatedItems:e,isNewApiKey:a,hasApiKey:s,hasSentCatalogData:t,hasSentInteractionData:r,hasTrainedModel:n,hasMadeApiCalls:i}=(0,O.Vd)(j,w,h,null),l="true"===localStorage.getItem("seenPostModalHighlight");g(e);let c=(0,O.qC)(e,s,l,t,r,n,i);if(k)try{await k(c)}catch(e){console.error("Error updating checklist status in backend:",e)}localStorage.setItem("gettingStartedChecklist",JSON.stringify(c)),(l||a)&&!localStorage.getItem("dismissedChecklist")&&(localStorage.removeItem("dismissedChecklist"),p(!1)),o(!1)};(0,r.useEffect)(()=>{j&&w?R():y||E||o(!1)},[j,w,y,E]);let z=async e=>{let a=h.map(a=>a.id===e?{...a,completed:!a.completed}:a);g(a);let s={};if(a.forEach(e=>{s[e.id]=e.completed}),k)try{await k(s)}catch(e){console.error("Error updating checklist status in backend:",e)}localStorage.setItem("gettingStartedChecklist",JSON.stringify(s))},U=async()=>{if(p(!0),localStorage.setItem("dismissedChecklist","true"),k)try{let e=(null==v?void 0:v())||{};await k({...e,dismissed:!0})}catch(e){console.error("Error updating dismissed status in backend:",e)}};if(d)return null;let Y=h.filter(e=>e.completed).length,Z=h.length,q=Math.round(Y/Z*100),F="true"===localStorage.getItem("seenPostModalHighlight"),X=(0,O.Io)(F,!1);return X&&localStorage.setItem("checklistHighlighted","true"),(0,t.jsxs)(c.Zp,{className:"mb-6 border-2 ".concat(X?"border-indigo-400 dark:border-indigo-500 shadow-lg animate-pulse":"border-indigo-200 dark:border-indigo-800/40 shadow-md hover:shadow-lg"," transition-all duration-300 ").concat(Y<Z?"ring-2 ring-indigo-100 dark:ring-indigo-900/20":""),children:[(0,t.jsx)(c.aR,{className:"pb-2 ".concat(X?"bg-indigo-50 dark:bg-indigo-900/20":Y<Z?"bg-gradient-to-r from-indigo-50 to-transparent dark:from-indigo-900/10 dark:to-transparent":""),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.ZB,{className:"text-lg flex items-center",children:[(0,t.jsx)("span",{className:"".concat(X?"bg-indigo-200 dark:bg-indigo-800":"bg-indigo-100 dark:bg-indigo-900/50"," p-1.5 rounded-md mr-2 transition-colors"),children:(0,t.jsx)(L,{className:"h-5 w-5 text-indigo-600 dark:text-indigo-400"})}),X?"\xa1Comienza Aqu\xed!":"Primeros Pasos"]}),(0,t.jsxs)(c.BT,{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:X?"Sigue estos pasos para comenzar a usar tu nueva API Key":"Completa estas tareas para comenzar a usar Rayuela"}),(0,t.jsx)(N.Bc,{children:(0,t.jsxs)(N.m_,{children:[(0,t.jsx)(N.k$,{asChild:!0,children:(0,t.jsx)(l.Button,{variant:"ghost",size:"icon",className:"h-6 w-6 text-indigo-500 hover:text-indigo-700 hover:bg-indigo-50 -mr-2",onClick:T,disabled:y||E,children:(0,t.jsx)(C.A,{className:"h-3.5 w-3.5 ".concat(y||E?"animate-spin":"")})})}),(0,t.jsx)(N.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:"Actualizar progreso"})})]})})]})]}),(0,t.jsxs)("div",{className:"flex gap-1",children:[!i&&j&&w&&(0,t.jsx)(N.Bc,{children:(0,t.jsxs)(N.m_,{children:[(0,t.jsx)(N.k$,{asChild:!0,children:(0,t.jsxs)(D.E,{variant:"outline",className:"text-[10px] px-1.5 py-0 h-5 bg-gray-50 dark:bg-gray-800 text-gray-500",children:["Actualizado:"," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})}),(0,t.jsx)(N.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:"\xdaltima verificaci\xf3n de progreso"})})]})}),(0,t.jsx)(l.Button,{variant:"ghost",size:"icon",className:"text-gray-400 hover:text-gray-500",onClick:U,children:(0,t.jsx)(u.A,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Progreso"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[Y,"/",Z," completados"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden",children:(0,t.jsx)("div",{className:"h-2.5 rounded-full transition-all duration-500 ease-in-out ".concat(100===q?"bg-gradient-to-r from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600":q>50?"bg-gradient-to-r from-indigo-500 to-blue-500 dark:from-indigo-600 dark:to-blue-600":"bg-gradient-to-r from-indigo-400 to-indigo-500 dark:from-indigo-500 dark:to-indigo-600"),style:{width:"".concat(q,"%")}})}),100===q&&(0,t.jsx)("div",{className:"flex justify-center mt-2 text-green-600 dark:text-green-400 text-xs font-medium",children:(0,t.jsx)(K.hf,{icon:(0,t.jsx)(P.A,{className:"h-4 w-4"}),size:"sm",children:"\xa1Configuraci\xf3n completada!"})}),(y||E)&&(0,t.jsx)("div",{className:"flex justify-center py-2 mb-4 mt-2",children:(0,t.jsx)(K.hf,{icon:(0,t.jsx)(C.A,{className:"h-4 w-4 animate-spin text-indigo-500"}),children:(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"Verificando progreso..."})})}),(f||A)&&(0,t.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border-l-2 border-red-500 pl-3 py-2 mb-4 mt-2",children:(0,t.jsxs)("p",{className:"text-xs text-red-600 dark:text-red-400",children:["Error al verificar el progreso.",(0,t.jsx)("button",{onClick:T,className:"ml-2 underline hover:text-red-700 dark:hover:text-red-300",children:"Reintentar"})]})}),(0,t.jsxs)("div",{className:"space-y-6 mt-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2",children:(0,t.jsx)(K.hf,{icon:(0,t.jsx)("span",{className:"bg-blue-100 dark:bg-blue-900/50 p-1 rounded-md",children:(0,t.jsx)(m.A,{className:"h-3.5 w-3.5 text-blue-600 dark:text-blue-400"})}),children:"Configuraci\xf3n Inicial"})}),(0,t.jsx)("ul",{className:"space-y-3",children:h.filter(e=>"setup"===e.category).map(a=>(0,t.jsx)(B,{item:a,onToggle:z,onRefresh:T,apiKey:e||void 0,codeSnippets:s,colorScheme:"blue"},a.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[(0,t.jsx)("span",{className:"bg-purple-100 dark:bg-purple-900/50 p-1 rounded-md mr-2",children:(0,t.jsx)(_.A,{className:"h-3.5 w-3.5 text-purple-600 dark:text-purple-400"})}),"Carga de Datos"]}),(0,t.jsx)("ul",{className:"space-y-3",children:h.filter(e=>"data"===e.category).map(a=>(0,t.jsx)(B,{item:a,onToggle:z,onRefresh:T,apiKey:e||void 0,codeSnippets:s,colorScheme:"purple"},a.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[(0,t.jsx)("span",{className:"bg-amber-100 dark:bg-amber-900/50 p-1 rounded-md mr-2",children:(0,t.jsx)(I.A,{className:"h-3.5 w-3.5 text-amber-600 dark:text-amber-400"})}),"Modelo y Recomendaciones"]}),(0,t.jsx)("ul",{className:"space-y-3",children:h.filter(e=>"model"===e.category).map(a=>(0,t.jsx)(B,{item:a,onToggle:z,onRefresh:T,apiKey:e||void 0,codeSnippets:s,colorScheme:"amber"},a.id))})]})]})]})})]})};var U=s(10),Y=s(5021),Z=s(1243),q=s(2525),F=s(6671),X=s(3999);function J(e){let{currentPlan:a}=e,[s,n]=(0,r.useState)(!1),{token:i}=(0,X.A)();if("FREE"!==a)return null;let d=async()=>{n(!0);try{if((await fetch("/api/v1/sandbox/reset",{method:"POST",headers:{Authorization:"Bearer ".concat(i),"Content-Type":"application/json"}})).ok)F.o.success("\uD83E\uDDEA Datos de sandbox limpiados exitosamente"),window.location.reload();else throw Error("Error al limpiar el sandbox")}catch(e){console.error("Error resetting sandbox:",e),F.o.error("Error al limpiar los datos de sandbox")}finally{n(!1)}};return(0,t.jsxs)(c.Zp,{className:"border-orange-200 dark:border-orange-800",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2 text-orange-800 dark:text-orange-200",children:[(0,t.jsx)(Y.A,{className:"h-5 w-5"}),"Developer Sandbox Tools"]}),(0,t.jsx)(c.BT,{children:"Herramientas especiales para desarrollo y experimentaci\xf3n"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"Entorno de Desarrollo"}),(0,t.jsxs)(o.TN,{children:["Est\xe1s en el plan ",(0,t.jsx)("strong",{children:"Developer Sandbox"}),". Puedes experimentar libremente sin preocuparte por costos o afectar datos de producci\xf3n."]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"font-medium",children:"Reset de Datos Experimentales"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Limpia todos tus datos de prueba (productos, usuarios, interacciones, modelos) para empezar fresh con nuevos experimentos."}),(0,t.jsxs)(U.Lt,{children:[(0,t.jsx)(U.tv,{asChild:!0,children:(0,t.jsxs)(l.Button,{variant:"outline",className:"w-full border-orange-300 text-orange-700 hover:bg-orange-50 dark:border-orange-700 dark:text-orange-300 dark:hover:bg-orange-900/20",disabled:s,children:[(0,t.jsx)(q.A,{className:"mr-2 h-4 w-4"}),s?"Limpiando...":"Limpiar Datos de Sandbox"]})}),(0,t.jsxs)(U.EO,{children:[(0,t.jsxs)(U.wd,{children:[(0,t.jsx)(U.r7,{children:"\xbfLimpiar todos los datos de sandbox?"}),(0,t.jsxs)(U.$v,{className:"space-y-2",children:[(0,t.jsx)("p",{children:"Esta acci\xf3n eliminar\xe1 permanentemente:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside text-sm space-y-1 ml-4",children:[(0,t.jsx)("li",{children:"Todos los productos de prueba"}),(0,t.jsx)("li",{children:"Todos los usuarios de prueba"}),(0,t.jsx)("li",{children:"Todas las interacciones de prueba"}),(0,t.jsx)("li",{children:"Todos los modelos entrenados"}),(0,t.jsx)("li",{children:"Historial de jobs de entrenamiento e ingesta"})]}),(0,t.jsx)("p",{className:"font-medium text-orange-600 dark:text-orange-400",children:"⚠️ Esta acci\xf3n no se puede deshacer."}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"\uD83D\uDD12 Tus API Keys y configuraci\xf3n de cuenta se mantendr\xe1n intactas."})]})]}),(0,t.jsxs)(U.ck,{children:[(0,t.jsx)(U.Zr,{children:"Cancelar"}),(0,t.jsx)(U.Rx,{onClick:d,className:"bg-orange-600 hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800",disabled:s,children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(C.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Limpiando..."]}):"S\xed, limpiar sandbox"})]})]})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,t.jsxs)("p",{children:["\uD83D\uDCA1 ",(0,t.jsx)("strong",{children:"Tip:"})," Limpia tus datos regularmente para probar diferentes escenarios"]}),(0,t.jsxs)("p",{children:["\uD83D\uDE80 ",(0,t.jsx)("strong",{children:"Workflow recomendado:"})," Reset → Cargar datos test → Entrenar → Evaluar"]})]})]})]})]})}var M=s(8338);function V(){var e,a,s;let{token:g,apiKey:f}=(0,n.As)(),[y,b]=(0,r.useState)(!1),[N,k]=(0,r.useState)(!0),[w,A]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{if(!g||!f)return k(!1);try{let e=await (0,i._)(),a=new Date(e.createdAt),s=(new Date().getTime()-a.getTime())/36e5;b(s<24)}catch(e){console.error("Error al obtener datos de la cuenta:",e)}finally{k(!1)}})(),"true"===localStorage.getItem("dismissedOnboarding")&&A(!0)},[g,f]);let{accountData:E,error:_,isLoading:I}=(0,n.T4)(),{usageData:C,error:P,isLoading:S}=(0,n.TB)(),T=I||S;return(0,t.jsxs)("div",{className:"rayuela-fade-in",children:[(0,t.jsx)("div",{className:"rayuela-scale-in rayuela-stagger-1",children:(0,t.jsx)(z,{})}),(0,t.jsx)("div",{className:"rayuela-scale-in rayuela-stagger-2",children:(0,t.jsx)(J,{currentPlan:null==E||null==(e=E.subscription)?void 0:e.plan})}),y&&!w&&!N&&(0,t.jsx)("div",{className:"rayuela-slide-up rayuela-stagger-2",children:(0,t.jsx)(o.Fc,{className:"mb-6",variant:"info",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(o.XL,{className:"flex items-center",children:"\xa1Bienvenido a Rayuela!"}),(0,t.jsxs)(o.TN,{className:"mt-2",children:["Para comenzar a utilizar nuestra API, te recomendamos seguir estos pasos:",(0,t.jsxs)("ol",{className:"list-none mt-3 space-y-3",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm",children:"1"}),(0,t.jsx)(j(),{href:"/api-keys",className:"text-primary hover:text-primary/80 font-medium underline underline-offset-2",children:"Gestiona tus API Keys"}),(0,t.jsx)("span",{className:"ml-2",children:"para generar o ver tus claves de acceso"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm",children:"2"}),(0,t.jsx)("a",{href:"https://docs.rayuela.ai/quickstart",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary/80 font-medium underline underline-offset-2",children:"Consulta nuestra gu\xeda de inicio r\xe1pido"}),(0,t.jsx)("span",{className:"ml-2",children:"para aprender a integrar la API"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm",children:"3"}),(0,t.jsx)(j(),{href:"/usage",className:"text-primary hover:text-primary/80 font-medium underline underline-offset-2",children:"Monitorea tu uso"}),(0,t.jsx)("span",{className:"ml-2",children:"para ver estad\xedsticas y l\xedmites de tu cuenta"})]})]})]}),(0,t.jsxs)("div",{className:"mt-5 flex flex-wrap gap-3",children:[(0,t.jsx)(l.Button,{asChild:!0,size:"sm",children:(0,t.jsxs)(j(),{href:"/api-keys",className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Gestionar API Keys"]})}),(0,t.jsx)(l.Button,{asChild:!0,variant:"outline",size:"sm",children:(0,t.jsxs)("a",{href:"https://docs.rayuela.ai/quickstart",target:"_blank",rel:"noopener noreferrer",className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Ver Gu\xeda de Inicio"]})}),(0,t.jsx)(l.Button,{asChild:!0,variant:"outline",size:"sm",children:(0,t.jsxs)(j(),{href:"/usage",className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Ver Uso"]})})]})]}),(0,t.jsx)(l.Button,{variant:"ghost",size:"icon",className:"text-muted-foreground hover:bg-muted hover:text-foreground",onClick:()=>{A(!0),localStorage.setItem("dismissedOnboarding","true")},children:(0,t.jsx)(u.A,{className:"h-4 w-4"})})]})})}),(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-heading-large text-foreground",children:"Dashboard Overview"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2",children:"Welcome to your dashboard. Here you'll find an overview of your API usage, billing information, and manage your API keys."})]}),(0,t.jsx)(v,{className:"hidden md:flex"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-6",children:[(0,t.jsxs)(c.Zp,{className:"col-span-1 md:col-span-2 lg:col-span-2 border-2 border-blue-50 dark:border-blue-900/30 shadow-sm hover:shadow-md transition-all duration-300",children:[(0,t.jsxs)(c.aR,{className:"pb-2",children:[(0,t.jsxs)(c.ZB,{className:"flex items-center text-subheading",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 mr-2 text-info"}),"Resumen de Uso"]}),(0,t.jsx)(c.BT,{children:"Vista r\xe1pida de tu consumo actual"})]}),(0,t.jsx)(c.Wu,{children:T?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.E,{className:"h-6 w-full"}),(0,t.jsx)(d.E,{className:"h-4 w-3/4"})]}):P?(0,t.jsx)("p",{className:"text-red-500 text-sm",children:"Error al cargar datos de uso"}):C&&E?(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"p-3 bg-info-light rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-blue-700 dark:text-blue-300 font-medium",children:"Llamadas API"}),(0,t.jsx)("div",{className:"text-metric mt-1 text-blue-800 dark:text-blue-200",children:(0,M.ZV)((null==(a=C.apiCalls)?void 0:a.used)||0)}),(0,t.jsx)("div",{className:"text-xs text-blue-600/70 dark:text-blue-400/70 mt-1",children:"Uso actual"})]}),(0,t.jsxs)("div",{className:"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-green-700 dark:text-green-300 font-medium",children:"Almacenamiento"}),(0,t.jsx)("div",{className:"text-metric mt-1 text-green-800 dark:text-green-200",children:(0,M.z3)((null==(s=C.storage)?void 0:s.usedBytes)||0)}),(0,t.jsx)("div",{className:"text-xs text-green-600/70 dark:text-green-400/70 mt-1",children:"Uso actual"})]})]}):(0,t.jsx)("p",{className:"text-gray-500",children:"No hay datos de uso disponibles"})}),(0,t.jsx)(c.wL,{children:(0,t.jsx)(l.Button,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,t.jsxs)(j(),{href:"/usage",className:"flex items-center justify-center",children:[(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Ver Panel de Uso Completo"]})})})]}),(0,t.jsxs)(c.Zp,{className:"border-2 border-orange-50 dark:border-orange-900/30 shadow-sm hover:shadow-md transition-all duration-300",children:[(0,t.jsxs)(c.aR,{className:"pb-2",children:[(0,t.jsxs)(c.ZB,{className:"flex items-center text-subheading",children:[(0,t.jsx)(m.A,{className:"h-5 w-5 mr-2 text-warning"}),"API Key Actual"]}),(0,t.jsx)(c.BT,{children:"Informaci\xf3n de tu clave activa"})]}),(0,t.jsx)(c.Wu,{children:T?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.E,{className:"h-4 w-full"}),(0,t.jsx)(d.E,{className:"h-3 w-2/3"})]}):_?(0,t.jsx)("p",{className:"text-red-500 text-sm",children:"Error al cargar datos de la cuenta"}):f?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"text-sm font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded",children:[f.slice(0,4),"...",f.slice(-4)]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"API Key activa"})]}):(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay API Key disponible"})})]}),(0,t.jsxs)(c.Zp,{className:"border-2 border-green-50 dark:border-green-900/30 shadow-sm hover:shadow-md transition-all duration-300",children:[(0,t.jsxs)(c.aR,{className:"pb-2",children:[(0,t.jsxs)(c.ZB,{className:"flex items-center text-subheading",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 mr-2 text-success"}),"Estado de Cuenta"]}),(0,t.jsx)(c.BT,{children:"Estado actual de tu suscripci\xf3n"})]}),(0,t.jsx)(c.Wu,{children:T?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.E,{className:"h-6 w-full"}),(0,t.jsx)(d.E,{className:"h-4 w-3/4"})]}):_?(0,t.jsx)("p",{className:"text-red-500 text-sm",children:"Error al cargar datos de suscripci\xf3n"}):(null==E?void 0:E.subscription)?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full mr-2 ".concat(E.subscription.isActive?"bg-green-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-sm font-medium",children:E.subscription.plan||"Free"})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[E.subscription.isActive?"Activa":"Inactiva",E.subscription.expiresAt&&(0,t.jsxs)("span",{children:[" • Expira ",(0,M.Yq)(E.subscription.expiresAt)]})]})]}):(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Plan Free"})]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Plan b\xe1sico activo"})]})})]})]})]})}},2881:(e,a,s)=>{"use strict";s.d(a,{BJ:()=>l,YJ:()=>o,hf:()=>i});var t=s(5155);s(2115);var r=s(9434);let n={iconText:{sm:"gap-1.5",md:"gap-2",lg:"gap-3"},elementGroup:{tight:"gap-1",normal:"gap-2",loose:"gap-3"},stack:{xs:"space-y-1",sm:"space-y-1.5",md:"space-y-2",lg:"space-y-3",xl:"space-y-4"}};function i(e){let{icon:a,children:s,size:i="md",align:l="center",className:o,...c}=e;return(0,t.jsxs)("div",{className:(0,r.cn)("flex",n.iconText[i],{start:"items-start",center:"items-center",end:"items-end"}[l],o),...c,children:[(0,t.jsx)("span",{className:"shrink-0 flex items-center",children:a}),(0,t.jsx)("span",{className:"min-w-0",children:s})]})}function l(e){let{spacing:a="md",className:s,children:i,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)(n.stack[a],s),...l,children:i})}function o(e){let{spacing:a="normal",align:s="center",wrap:i=!1,className:l,children:o,...c}=e;return(0,t.jsx)("div",{className:(0,r.cn)("flex",n.elementGroup[a],{start:"items-start",center:"items-center",end:"items-end",stretch:"items-stretch"}[s],i&&"flex-wrap",l),...c,children:o})}},5902:(e,a,s)=>{Promise.resolve().then(s.bind(s,1538))},8338:(e,a,s)=>{"use strict";s.d(a,{KC:()=>u,Yq:()=>h,ZV:()=>x,a3:()=>g,cR:()=>p,z3:()=>m});var t=s(5155);s(2115);var r=s(6126),n=s(646),i=s(3904),l=s(4186),o=s(4861),c=s(5690),d=s(1243);function m(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(a<0?0:a))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][s]}function x(e){return new Intl.NumberFormat().format(e)}function p(e){let a={className:"h-4 w-4"};switch(null==e?void 0:e.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(n.A,{...a,className:"h-4 w-4 text-green-500"});case"running":case"processing":case"in_progress":return(0,t.jsx)(i.A,{...a,className:"h-4 w-4 text-blue-500 animate-spin"});case"pending":case"queued":case"waiting":return(0,t.jsx)(l.A,{...a,className:"h-4 w-4 text-yellow-500"});case"failed":case"error":case"cancelled":return(0,t.jsx)(o.A,{...a,className:"h-4 w-4 text-red-500"});case"starting":case"initializing":return(0,t.jsx)(c.A,{...a,className:"h-4 w-4 text-blue-400"});case"warning":return(0,t.jsx)(d.A,{...a,className:"h-4 w-4 text-amber-500"});default:return(0,t.jsx)(l.A,{...a,className:"h-4 w-4 text-gray-400"})}}function u(e){switch(null==e?void 0:e.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(r.E,{variant:"success",className:"text-xs",children:"Completado"});case"running":case"processing":case"in_progress":return(0,t.jsx)(r.E,{variant:"info",className:"text-xs",children:"En progreso"});case"pending":case"queued":case"waiting":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Pendiente"});case"failed":case"error":case"cancelled":return(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"Fallido"});case"starting":case"initializing":return(0,t.jsx)(r.E,{variant:"secondary",className:"text-xs",children:"Iniciando"});case"warning":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Advertencia"});default:return(0,t.jsx)(r.E,{variant:"outline",className:"text-xs",children:"Desconocido"})}}function h(e){let a=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!e)return"No disponible";try{let s=new Date(e),t={year:"numeric",month:"long",day:"numeric",...a&&{hour:"2-digit",minute:"2-digit"}};return new Intl.DateTimeFormat("es-ES",t).format(s)}catch(e){return console.error("Error al formatear fecha:",e),"Formato de fecha inv\xe1lido"}}function g(e){let a=Math.floor(e/1e3),s=Math.floor(a/60),t=Math.floor(s/60),r=Math.floor(t/24);return r>0?"".concat(r,"d ").concat(t%24,"h ").concat(s%60,"m"):t>0?"".concat(t,"h ").concat(s%60,"m ").concat(a%60,"s"):s>0?"".concat(s,"m ").concat(a%60,"s"):"".concat(a,"s")}}},e=>{var a=a=>e(e.s=a);e.O(0,[9352,6874,1445,5674,3753,3843,9566,9521,8638,2092,3999,7571,9843,8441,1684,7358],()=>a(5902)),_N_E=e.O()}]);