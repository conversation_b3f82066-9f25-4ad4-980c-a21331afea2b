<!DOCTYPE html><html lang="es" class="__variable_e8ce0c"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/74726d29afef2ab2.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/554aa4ecb4bf38a8.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-a18916eaa43bd3fa.js"/><script src="/_next/static/chunks/4bd1b696-1c31b9758c3426b8.js" async=""></script><script src="/_next/static/chunks/1684-6d160cce63225728.js" async=""></script><script src="/_next/static/chunks/main-app-733d1219104ce150.js" async=""></script><script src="/_next/static/chunks/9352-31b4412fb2722f24.js" async=""></script><script src="/_next/static/chunks/1445-21443766417aa7af.js" async=""></script><script src="/_next/static/chunks/5674-7748bf837df56152.js" async=""></script><script src="/_next/static/chunks/3753-51055f9a7954ec13.js" async=""></script><script src="/_next/static/chunks/2092-d6fb07d59d636626.js" async=""></script><script src="/_next/static/chunks/3999-62f798ec15041d09.js" async=""></script><script src="/_next/static/chunks/app/layout-71c41bba19c0ef97.js" async=""></script><script src="/_next/static/chunks/6874-da306f8e1ba36f76.js" async=""></script><script src="/_next/static/chunks/app/(public)/docs/page-445ade47f2102b2e.js" async=""></script><title>Documentación API | Rayuela.ai</title><meta name="description" content="Documentación completa de la API de Rayuela. Guías de inicio rápido, referencias de API, ejemplos de código y mejores prácticas."/><meta name="author" content="Rayuela Team"/><meta name="keywords" content="documentación, API, guía, tutorial, referencia, SDK, ejemplos"/><meta name="creator" content="Rayuela"/><meta name="publisher" content="Rayuela"/><meta name="robots" content="index, follow"/><link rel="canonical" href="https://rayuela.ai/docs"/><meta property="og:title" content="Documentación API | Rayuela.ai"/><meta property="og:description" content="Documentación completa de la API de Rayuela. Guías de inicio rápido, referencias de API, ejemplos de código y mejores prácticas."/><meta property="og:url" content="https://rayuela.ai/docs"/><meta property="og:site_name" content="Rayuela.ai"/><meta property="og:locale" content="es_AR"/><meta property="og:image" content="https://rayuela.ai/og-image.png"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="Documentación API"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@rayuela_ai"/><meta name="twitter:title" content="Documentación API | Rayuela.ai"/><meta name="twitter:description" content="Documentación completa de la API de Rayuela. Guías de inicio rápido, referencias de API, ejemplos de código y mejores prácticas."/><meta name="twitter:image" content="https://rayuela.ai/og-image.png"/><link rel="icon" href="/favicon.ico"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div><main><script type="application/ld+json">{"@context":"https://schema.org","@type":"APIReference","name":"Rayuela API Documentation","description":"Complete API reference for Rayuela recommendation system","url":"https://rayuela.ai/docs","programmingLanguage":["Python","JavaScript","PHP"]}</script><div class="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end"><div class="container mx-auto px-4 py-16"><div class="text-center mb-16"><h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">Documentación de Rayuela</h1><p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">Todo lo que necesitas para integrar sistemas de recomendación en tu aplicación. Desde guías de inicio rápido hasta referencias detalladas de API.</p><div class="flex justify-center gap-4"><a class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border focus-visible:border-ring bg-primary hover:bg-primary/90 active:bg-primary/95 shadow-xs hover:shadow-soft rayuela-button-hover h-11 rounded-lg px-8 text-body-lg" href="/docs/quickstart/python"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap w-5 h-5 mr-2"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg>Inicio Rápido</a><a class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border focus-visible:border-ring border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80 shadow-xs hover:shadow-soft rayuela-button-hover h-11 rounded-lg px-8 text-body-lg" href="/docs/api/recommendations"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code w-5 h-5 mr-2"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg>Referencia API</a></div></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap w-6 h-6 text-blue-600 dark:text-blue-400"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg></div><div data-slot="card-title" class="text-subheading rayuela-accent text-xl">Inicio Rápido</div><div data-slot="card-description" class="text-caption">Comienza a usar Rayuela en minutos</div></div><div data-slot="card-content" class="px-6"><ul class="space-y-2"><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/quickstart/python"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Python</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/quickstart/nodejs"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>JavaScript/Node.js</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/quickstart/php"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>PHP</a></li></ul></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code w-6 h-6 text-blue-600 dark:text-blue-400"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div data-slot="card-title" class="text-subheading rayuela-accent text-xl">Referencia de API</div><div data-slot="card-description" class="text-caption">Documentación completa de todos los endpoints</div></div><div data-slot="card-content" class="px-6"><ul class="space-y-2"><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/api/authentication"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Autenticación</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/api/recommendations"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Recomendaciones</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/api/pipeline"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Pipeline de Entrenamiento</a></li></ul></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-6 h-6 text-blue-600 dark:text-blue-400"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><div data-slot="card-title" class="text-subheading rayuela-accent text-xl">Ingesta de Datos</div><div data-slot="card-description" class="text-caption">Cómo enviar datos de productos, usuarios e interacciones</div></div><div data-slot="card-content" class="px-6"><ul class="space-y-2"><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/guides/data-ingestion"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Guía de Ingesta</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/guides/data-formats"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Formatos de Datos</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/guides/best-practices"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Mejores Prácticas</a></li></ul></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-column w-6 h-6 text-blue-600 dark:text-blue-400"><path d="M3 3v16a2 2 0 0 0 2 2h16"></path><path d="M18 17V9"></path><path d="M13 17V5"></path><path d="M8 17v-3"></path></svg></div><div data-slot="card-title" class="text-subheading rayuela-accent text-xl">Analytics y Métricas</div><div data-slot="card-description" class="text-caption">Monitorea el rendimiento de tus recomendaciones</div></div><div data-slot="card-content" class="px-6"><ul class="space-y-2"><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/analytics/metrics"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Métricas Disponibles</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/analytics/dashboards"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Dashboards</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/analytics/reports"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Reportes</a></li></ul></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-6 h-6 text-blue-600 dark:text-blue-400"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path></svg></div><div data-slot="card-title" class="text-subheading rayuela-accent text-xl">Seguridad</div><div data-slot="card-description" class="text-caption">Autenticación, autorización y mejores prácticas</div></div><div data-slot="card-content" class="px-6"><ul class="space-y-2"><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/security/api-keys"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>API Keys</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/security/jwt"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>JWT Tokens</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/security/rate-limiting"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Rate Limiting</a></li></ul></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open w-6 h-6 text-blue-600 dark:text-blue-400"><path d="M12 7v14"></path><path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path></svg></div><div data-slot="card-title" class="text-subheading rayuela-accent text-xl">Guías Avanzadas</div><div data-slot="card-description" class="text-caption">Casos de uso específicos y configuraciones avanzadas</div></div><div data-slot="card-content" class="px-6"><ul class="space-y-2"><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/guides/cold-start"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Cold Start</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/guides/ab-testing"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>A/B Testing</a></li><li><a class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" href="/docs/guides/personalization"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>Personalización</a></li></ul></div></div></div><div class="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg"><h2 class="text-3xl font-bold text-center text-gray-900 dark:text-white mb-8">Guías Populares</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><a class="p-6 border rounded-lg hover:shadow-md transition-shadow" href="/docs/quickstart/python"><h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">🐍 Inicio Rápido con Python</h3><p class="text-gray-600 dark:text-gray-300">Integra Rayuela en tu aplicación Python en menos de 5 minutos.</p></a><a class="p-6 border rounded-lg hover:shadow-md transition-shadow" href="/docs/guides/data-ingestion"><h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">📊 Guía de Ingesta de Datos</h3><p class="text-gray-600 dark:text-gray-300">Aprende a enviar datos de productos, usuarios e interacciones.</p></a><a class="p-6 border rounded-lg hover:shadow-md transition-shadow" href="/docs/api/recommendations"><h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">🎯 API de Recomendaciones</h3><p class="text-gray-600 dark:text-gray-300">Referencia completa para obtener recomendaciones personalizadas.</p></a><a class="p-6 border rounded-lg hover:shadow-md transition-shadow" href="/docs/guides/cold-start"><h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">🚀 Manejo de Cold Start</h3><p class="text-gray-600 dark:text-gray-300">Estrategias para nuevos usuarios y productos sin historial.</p></a></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></main></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section><script src="/_next/static/chunks/webpack-a18916eaa43bd3fa.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[3999,[\"9352\",\"static/chunks/9352-31b4412fb2722f24.js\",\"1445\",\"static/chunks/1445-21443766417aa7af.js\",\"5674\",\"static/chunks/5674-7748bf837df56152.js\",\"3753\",\"static/chunks/3753-51055f9a7954ec13.js\",\"2092\",\"static/chunks/2092-d6fb07d59d636626.js\",\"3999\",\"static/chunks/3999-62f798ec15041d09.js\",\"7177\",\"static/chunks/app/layout-71c41bba19c0ef97.js\"],\"AuthProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[6671,[\"9352\",\"static/chunks/9352-31b4412fb2722f24.js\",\"1445\",\"static/chunks/1445-21443766417aa7af.js\",\"5674\",\"static/chunks/5674-7748bf837df56152.js\",\"3753\",\"static/chunks/3753-51055f9a7954ec13.js\",\"2092\",\"static/chunks/2092-d6fb07d59d636626.js\",\"3999\",\"static/chunks/3999-62f798ec15041d09.js\",\"7177\",\"static/chunks/app/layout-71c41bba19c0ef97.js\"],\"Toaster\"]\n6:I[285,[\"9352\",\"static/chunks/9352-31b4412fb2722f24.js\",\"6874\",\"static/chunks/6874-da306f8e1ba36f76.js\",\"7371\",\"static/chunks/app/(public)/docs/page-445ade47f2102b2e.js\"],\"Button\"]\n7:I[6874,[\"9352\",\"static/chunks/9352-31b4412fb2722f24.js\",\"6874\",\"static/chunks/6874-da306f8e1ba36f76.js\",\"7371\",\"static/chunks/app/(public)/docs/page-445ade47f2102b2e.js\"],\"\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/74726d29afef2ab2.css\",\"style\"]\n:HL[\"/_next/static/css/554aa4ecb4bf38a8.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"pDXSyABSoCVQNQiYYq3id\",\"p\":\"\",\"c\":[\"\",\"docs\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(public)\",{\"children\":[\"docs\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/74726d29afef2ab2.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/554aa4ecb4bf38a8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"es\",\"className\":\"__variable_e8ce0c\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L5\",null,{\"position\":\"top-right\",\"richColors\":true}]]}]}]]}],{\"children\":[\"(public)\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"div\",null,{\"children\":[\"$\",\"main\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:0:props:children:props:notFound:0:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"docs\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"APIReference\\\",\\\"name\\\":\\\"Rayuela API Documentation\\\",\\\"description\\\":\\\"Complete API reference for Rayuela recommendation system\\\",\\\"url\\\":\\\"https://rayuela.ai/docs\\\",\\\"programmingLanguage\\\":[\\\"Python\\\",\\\"JavaScript\\\",\\\"PHP\\\"]}\"}}],[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-16\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-center mb-16\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6\",\"children\":\"Documentación de Rayuela\"}],[\"$\",\"p\",null,{\"className\":\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8\",\"children\":\"Todo lo que necesitas para integrar sistemas de recomendación en tu aplicación. Desde guías de inicio rápido hasta referencias detalladas de API.\"}],[\"$\",\"div\",null,{\"className\":\"flex justify-center gap-4\",\"children\":[[\"$\",\"$L6\",null,{\"asChild\":true,\"size\":\"lg\",\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/quickstart/python\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-zap w-5 h-5 mr-2\",\"children\":[[\"$\",\"path\",\"1xq2db\",{\"d\":\"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\"}],\"$undefined\"]}],\"Inicio Rápido\"]}]}],[\"$\",\"$L6\",null,{\"variant\":\"outline\",\"size\":\"lg\",\"asChild\":true,\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/api/recommendations\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-code w-5 h-5 mr-2\",\"children\":[[\"$\",\"polyline\",\"z7tu5w\",{\"points\":\"16 18 22 12 16 6\"}],[\"$\",\"polyline\",\"1eg1df\",{\"points\":\"8 6 2 12 8 18\"}],\"$undefined\"]}],\"Referencia API\"]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\"children\":[[\"$\",\"div\",\"0\",{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-zap w-6 h-6 text-blue-600 dark:text-blue-400\",\"children\":[[\"$\",\"path\",\"1xq2db\",{\"d\":\"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\"}],\"$undefined\"]}]}],[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent text-xl\",\"children\":\"Inicio Rápido\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Comienza a usar Rayuela en minutos\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",\"0\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/quickstart/python\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Python\"]}]}],[\"$\",\"li\",\"1\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/quickstart/nodejs\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"JavaScript/Node.js\"]}]}],[\"$\",\"li\",\"2\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/quickstart/php\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"PHP\"]}]}]]}]}]]}],[\"$\",\"div\",\"1\",{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-code w-6 h-6 text-blue-600 dark:text-blue-400\",\"children\":[[\"$\",\"polyline\",\"z7tu5w\",{\"points\":\"16 18 22 12 16 6\"}],[\"$\",\"polyline\",\"1eg1df\",{\"points\":\"8 6 2 12 8 18\"}],\"$undefined\"]}]}],[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent text-xl\",\"children\":\"Referencia de API\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Documentación completa de todos los endpoints\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",\"0\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/api/authentication\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Autenticación\"]}]}],[\"$\",\"li\",\"1\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/api/recommendations\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Recomendaciones\"]}]}],[\"$\",\"li\",\"2\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/api/pipeline\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Pipeline de Entrenamiento\"]}]}]]}]}]]}],[\"$\",\"div\",\"2\",{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-database w-6 h-6 text-blue-600 dark:text-blue-400\",\"children\":[[\"$\",\"ellipse\",\"msslwz\",{\"cx\":\"12\",\"cy\":\"5\",\"rx\":\"9\",\"ry\":\"3\"}],[\"$\",\"path\",\"1wlel7\",{\"d\":\"M3 5V19A9 3 0 0 0 21 19V5\"}],[\"$\",\"path\",\"mv7ke4\",{\"d\":\"M3 12A9 3 0 0 0 21 12\"}],\"$undefined\"]}]}],[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent text-xl\",\"children\":\"Ingesta de Datos\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Cómo enviar datos de productos, usuarios e interacciones\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",\"0\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/guides/data-ingestion\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Guía de Ingesta\"]}]}],[\"$\",\"li\",\"1\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/guides/data-formats\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Formatos de Datos\"]}]}],[\"$\",\"li\",\"2\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/guides/best-practices\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Mejores Prácticas\"]}]}]]}]}]]}],[\"$\",\"div\",\"3\",{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-chart-column w-6 h-6 text-blue-600 dark:text-blue-400\",\"children\":[[\"$\",\"path\",\"c24i48\",{\"d\":\"M3 3v16a2 2 0 0 0 2 2h16\"}],[\"$\",\"path\",\"2bz60n\",{\"d\":\"M18 17V9\"}],[\"$\",\"path\",\"1frdt8\",{\"d\":\"M13 17V5\"}],[\"$\",\"path\",\"17ska0\",{\"d\":\"M8 17v-3\"}],\"$undefined\"]}]}],[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent text-xl\",\"children\":\"Analytics y Métricas\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Monitorea el rendimiento de tus recomendaciones\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",\"0\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/analytics/metrics\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Métricas Disponibles\"]}]}],[\"$\",\"li\",\"1\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/analytics/dashboards\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Dashboards\"]}]}],[\"$\",\"li\",\"2\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/analytics/reports\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Reportes\"]}]}]]}]}]]}],[\"$\",\"div\",\"4\",{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-shield w-6 h-6 text-blue-600 dark:text-blue-400\",\"children\":[[\"$\",\"path\",\"oel41y\",{\"d\":\"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z\"}],\"$undefined\"]}]}],[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent text-xl\",\"children\":\"Seguridad\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Autenticación, autorización y mejores prácticas\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",\"0\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/security/api-keys\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"API Keys\"]}]}],[\"$\",\"li\",\"1\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/security/jwt\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"JWT Tokens\"]}]}],[\"$\",\"li\",\"2\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/security/rate-limiting\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Rate Limiting\"]}]}]]}]}]]}],[\"$\",\"div\",\"5\",{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out h-full\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-book-open w-6 h-6 text-blue-600 dark:text-blue-400\",\"children\":[[\"$\",\"path\",\"1akyts\",{\"d\":\"M12 7v14\"}],[\"$\",\"path\",\"ruj8y\",{\"d\":\"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\"}],\"$undefined\"]}]}],[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent text-xl\",\"children\":\"Guías Avanzadas\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Casos de uso específicos y configuraciones avanzadas\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",\"0\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/guides/cold-start\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Cold Start\"]}]}],[\"$\",\"li\",\"1\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/guides/ab-testing\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"A/B Testing\"]}]}],[\"$\",\"li\",\"2\",{\"children\":[\"$\",\"$L7\",null,{\"href\":\"/docs/guides/personalization\",\"className\":\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-arrow-right w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1ays0h\",{\"d\":\"M5 12h14\"}],[\"$\",\"path\",\"xquz4c\",{\"d\":\"m12 5 7 7-7 7\"}],\"$undefined\"]}],\"Personalización\"]}]}]]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-3xl font-bold text-center text-gray-900 dark:text-white mb-8\",\"children\":\"Guías Populares\"}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 md:grid-cols-2 gap-6\",\"children\":[[\"$\",\"$L7\",null,{\"href\":\"/docs/quickstart/python\",\"className\":\"p-6 border rounded-lg hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\"children\":\"🐍 Inicio Rápido con Python\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 dark:text-gray-300\",\"children\":\"Integra Rayuela en tu aplicación Python en menos de 5 minutos.\"}]]}],[\"$\",\"$L7\",null,{\"href\":\"/docs/guides/data-ingestion\",\"className\":\"p-6 border rounded-lg hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\"children\":\"📊 Guía de Ingesta de Datos\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 dark:text-gray-300\",\"children\":\"Aprende a enviar datos de productos, usuarios e interacciones.\"}]]}],[\"$\",\"$L7\",null,{\"href\":\"/docs/api/recommendations\",\"className\":\"p-6 border rounded-lg hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\"children\":\"🎯 API de Recomendaciones\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 dark:text-gray-300\",\"children\":\"Referencia completa para obtener recomendaciones personalizadas.\"}]]}],[\"$\",\"$L7\",null,{\"href\":\"/docs/guides/cold-start\",\"className\":\"p-6 border rounded-lg hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\"children\":\"🚀 Manejo de Cold Start\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 dark:text-gray-300\",\"children\":\"Estrategias para nuevos usuarios y productos sin historial.\"}]]}]]}]]}]]}]}]],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"_sVOOxjzC3YJ343LFnVfs\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Documentación API | Rayuela.ai\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Documentación completa de la API de Rayuela. Guías de inicio rápido, referencias de API, ejemplos de código y mejores prácticas.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Rayuela Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"documentación, API, guía, tutorial, referencia, SDK, ejemplos\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Rayuela\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"Rayuela\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"7\",{\"rel\":\"canonical\",\"href\":\"https://rayuela.ai/docs\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:title\",\"content\":\"Documentación API | Rayuela.ai\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:description\",\"content\":\"Documentación completa de la API de Rayuela. Guías de inicio rápido, referencias de API, ejemplos de código y mejores prácticas.\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:url\",\"content\":\"https://rayuela.ai/docs\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:site_name\",\"content\":\"Rayuela.ai\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:locale\",\"content\":\"es_AR\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:image\",\"content\":\"https://rayuela.ai/og-image.png\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:alt\",\"content\":\"Documentación API\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:creator\",\"content\":\"@rayuela_ai\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:title\",\"content\":\"Documentación API | Rayuela.ai\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:description\",\"content\":\"Documentación completa de la API de Rayuela. Guías de inicio rápido, referencias de API, ejemplos de código y mejores prácticas.\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:image\",\"content\":\"https://rayuela.ai/og-image.png\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"e:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>