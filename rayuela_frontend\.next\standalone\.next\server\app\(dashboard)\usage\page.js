(()=>{var t={};t.id=5598,t.ids=[5598],t.modules={3261:(t,e,i)=>{"use strict";i.d(e,{k:()=>w});var s=i(60687),r=i(43210),a=i(11273);i(51215);var n=i(8730),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let i=(0,n.TL)(`Primitive.${e}`),a=r.forwardRef((t,r)=>{let{asChild:a,...n}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a?i:e,{...n,ref:r})});return a.displayName=`Primitive.${e}`,{...t,[e]:a}},{}),l="Progress",[h,d]=(0,a.A)(l),[c,u]=h(l),f=r.forwardRef((t,e)=>{var i,r;let{__scopeProgress:a,value:n=null,max:l,getValueLabel:h=m,...d}=t;(l||0===l)&&!y(l)&&console.error((i=`${l}`,`Invalid prop \`max\` of value \`${i}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=y(l)?l:100;null===n||v(n,u)||console.error((r=`${n}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let f=v(n,u)?n:null,p=b(f)?h(f,u):void 0;return(0,s.jsx)(c,{scope:a,value:f,max:u,children:(0,s.jsx)(o.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":b(f)?f:void 0,"aria-valuetext":p,role:"progressbar","data-state":x(f,u),"data-value":f??void 0,"data-max":u,...d,ref:e})})});f.displayName=l;var p="ProgressIndicator",g=r.forwardRef((t,e)=>{let{__scopeProgress:i,...r}=t,a=u(p,i);return(0,s.jsx)(o.div,{"data-state":x(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:e})});function m(t,e){return`${Math.round(t/e*100)}%`}function x(t,e){return null==t?"indeterminate":t===e?"complete":"loading"}function b(t){return"number"==typeof t}function y(t){return b(t)&&!isNaN(t)&&t>0}function v(t,e){return b(t)&&!isNaN(t)&&t<=e&&t>=0}g.displayName=p;var _=i(4780);let w=r.forwardRef(({className:t,value:e,...i},r)=>(0,s.jsx)(f,{ref:r,className:(0,_.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...i,children:(0,s.jsx)(g,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(e||0)}%)`}})}));w.displayName=f.displayName},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5045:(t,e,i)=>{"use strict";let s;i.r(e),i.d(e,{default:()=>hA});var r,a,n,o,l,h,d={};i.r(d),i.d(d,{Button:()=>oT,CaptionLabel:()=>oN,Chevron:()=>oA,Day:()=>oj,DayButton:()=>oL,Dropdown:()=>oI,DropdownNav:()=>oR,Footer:()=>oz,Month:()=>oF,MonthCaption:()=>oW,MonthGrid:()=>oB,Months:()=>oV,MonthsDropdown:()=>o$,Nav:()=>oU,NextMonthButton:()=>oZ,Option:()=>oX,PreviousMonthButton:()=>oq,Root:()=>oG,Select:()=>oK,Week:()=>oJ,WeekNumber:()=>o1,WeekNumberHeader:()=>o2,Weekday:()=>oQ,Weekdays:()=>o0,Weeks:()=>o5,YearsDropdown:()=>o3});var c={};i.r(c),i.d(c,{formatCaption:()=>o4,formatDay:()=>o6,formatMonthCaption:()=>o8,formatMonthDropdown:()=>o9,formatWeekNumber:()=>o7,formatWeekNumberHeader:()=>lt,formatWeekdayName:()=>le,formatYearCaption:()=>ls,formatYearDropdown:()=>li});var u={};i.r(u),i.d(u,{labelCaption:()=>la,labelDay:()=>ll,labelDayButton:()=>lo,labelGrid:()=>lr,labelGridcell:()=>ln,labelMonthDropdown:()=>ld,labelNav:()=>lh,labelNext:()=>lc,labelPrevious:()=>lu,labelWeekNumber:()=>lp,labelWeekNumberHeader:()=>lg,labelWeekday:()=>lf,labelYearDropdown:()=>lm});var f=i(60687),p=i(43210),g=i(87981),m=i(23711);function x(t,e,i){let s=(0,m.a)(t,i?.in);return isNaN(e)?(0,g.w)(i?.in||t,NaN):(e&&s.setDate(s.getDate()+e),s)}var b=i(85650),y=i(58505);function v(t,e){let i,s,r=()=>(0,g.w)(e?.in,NaN),a=e?.additionalDigits??2,n=function(t){let e,i={},s=t.split(_.dateTimeDelimiter);if(s.length>2)return i;if(/:/.test(s[0])?e=s[0]:(i.date=s[0],e=s[1],_.timeZoneDelimiter.test(i.date)&&(i.date=t.split(_.timeZoneDelimiter)[0],e=t.substr(i.date.length,t.length))),e){let t=_.timezone.exec(e);t?(i.time=e.replace(t[1],""),i.timezone=t[1]):i.time=e}return i}(t);if(n.date){let t=function(t,e){let i=RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),s=t.match(i);if(!s)return{year:NaN,restDateString:""};let r=s[1]?parseInt(s[1]):null,a=s[2]?parseInt(s[2]):null;return{year:null===a?r:100*a,restDateString:t.slice((s[1]||s[2]).length)}}(n.date,a);i=function(t,e){var i,s,r,a,n,o,l,h;if(null===e)return new Date(NaN);let d=t.match(w);if(!d)return new Date(NaN);let c=!!d[4],u=D(d[1]),f=D(d[2])-1,p=D(d[3]),g=D(d[4]),m=D(d[5])-1;if(c){return(i=0,s=g,r=m,s>=1&&s<=53&&r>=0&&r<=6)?function(t,e,i){let s=new Date(0);s.setUTCFullYear(t,0,4);let r=s.getUTCDay()||7;return s.setUTCDate(s.getUTCDate()+((e-1)*7+i+1-r)),s}(e,g,m):new Date(NaN)}{let t=new Date(0);return(a=e,n=f,o=p,n>=0&&n<=11&&o>=1&&o<=(S[n]||(P(a)?29:28))&&(l=e,(h=u)>=1&&h<=(P(l)?366:365)))?(t.setUTCFullYear(e,f,Math.max(u,p)),t):new Date(NaN)}}(t.restDateString,t.year)}if(!i||isNaN(+i))return r();let o=+i,l=0;if(n.time&&isNaN(l=function(t){var e,i,s;let r=t.match(M);if(!r)return NaN;let a=C(r[1]),n=C(r[2]),o=C(r[3]);return(e=a,i=n,s=o,24===e?0===i&&0===s:s>=0&&s<60&&i>=0&&i<60&&e>=0&&e<25)?a*y.s0+n*y.Cg+1e3*o:NaN}(n.time)))return r();if(n.timezone){if(isNaN(s=function(t){var e,i;if("Z"===t)return 0;let s=t.match(k);if(!s)return 0;let r="+"===s[1]?-1:1,a=parseInt(s[2]),n=s[3]&&parseInt(s[3])||0;return(e=0,(i=n)>=0&&i<=59)?r*(a*y.s0+n*y.Cg):NaN}(n.timezone)))return r()}else{let t=new Date(o+l),i=(0,m.a)(0,e?.in);return i.setFullYear(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()),i.setHours(t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.getUTCMilliseconds()),i}return(0,m.a)(o+l+s,e?.in)}let _={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},w=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,M=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,k=/^([+-])(\d{2})(?::?(\d{2}))?$/;function D(t){return t?parseInt(t):1}function C(t){return t&&parseFloat(t.replace(",","."))||0}let S=[31,null,31,30,31,30,31,31,30,31,30,31];function P(t){return t%400==0||t%4==0&&t%100!=0}var O=i(62244),E=i(44493),T=i(85726),N=i(29523),A=i(3261),j=i(96834),L=i(91821),I=i(52581),R=i(59327),z=i(78122),F=i(93613),W=i(48730),B=i(53411),V=i(61611),H=i(65668),Y=i(96882),$=i(25541),U=i(53520),Z=i(97930),X=i(94652);function q(t){return t+.5|0}let G=(t,e,i)=>Math.max(Math.min(t,i),e);function K(t){return G(q(2.55*t),0,255)}function J(t){return G(q(255*t),0,255)}function Q(t){return G(q(t/2.55)/100,0,1)}function tt(t){return G(q(100*t),0,100)}let te={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ti=[..."0123456789ABCDEF"],ts=t=>ti[15&t],tr=t=>ti[(240&t)>>4]+ti[15&t],ta=t=>(240&t)>>4==(15&t),tn=t=>ta(t.r)&&ta(t.g)&&ta(t.b)&&ta(t.a),to=(t,e)=>t<255?e(t):"",tl=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function th(t,e,i){let s=e*Math.min(i,1-i),r=(e,r=(e+t/30)%12)=>i-s*Math.max(Math.min(r-3,9-r,1),-1);return[r(0),r(8),r(4)]}function td(t,e,i){let s=(s,r=(s+t/60)%6)=>i-i*e*Math.max(Math.min(r,4-r,1),0);return[s(5),s(3),s(1)]}function tc(t,e,i){let s,r=th(t,1,.5);for(e+i>1&&(s=1/(e+i),e*=s,i*=s),s=0;s<3;s++)r[s]*=1-e-i,r[s]+=e;return r}function tu(t){let e,i,s,r=t.r/255,a=t.g/255,n=t.b/255,o=Math.max(r,a,n),l=Math.min(r,a,n),h=(o+l)/2;o!==l&&(s=o-l,i=h>.5?s/(2-o-l):s/(o+l),e=60*(e=r===o?(a-n)/s+6*(a<n):a===o?(n-r)/s+2:(r-a)/s+4)+.5);return[0|e,i||0,h]}function tf(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(J)}function tp(t){return(t%360+360)%360}let tg={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},tm={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},tx=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,tb=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,ty=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function tv(t,e,i){if(t){let s=tu(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),t.r=(s=tf(th,s,void 0,void 0))[0],t.g=s[1],t.b=s[2]}}function t_(t,e){return t?Object.assign(e||{},t):t}function tw(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=J(t[3]))):(e=t_(t,{r:0,g:0,b:0,a:1})).a=J(e.a),e}class tM{constructor(t){let e;if(t instanceof tM)return t;let i=typeof t;"object"===i?e=tw(t):"string"===i&&(e=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*te[t[1]],g:255&17*te[t[2]],b:255&17*te[t[3]],a:5===i?17*te[t[4]]:255}:(7===i||9===i)&&(e={r:te[t[1]]<<4|te[t[2]],g:te[t[3]]<<4|te[t[4]],b:te[t[5]]<<4|te[t[6]],a:9===i?te[t[7]]<<4|te[t[8]]:255})),e}(t)||function(t){s||((s=function(){let t,e,i,s,r,a={},n=Object.keys(tm),o=Object.keys(tg);for(t=0;t<n.length;t++){for(e=0,s=r=n[t];e<o.length;e++)i=o[e],r=r.replace(i,tg[i]);i=parseInt(tm[s],16),a[r]=[i>>16&255,i>>8&255,255&i]}return a}()).transparent=[0,0,0,0]);let e=s[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,i,s,r=tx.exec(t),a=255;if(r){if(r[7]!==e){let t=+r[7];a=r[8]?K(t):G(255*t,0,255)}return e=+r[1],i=+r[3],s=+r[5],e=255&(r[2]?K(e):G(e,0,255)),{r:e,g:i=255&(r[4]?K(i):G(i,0,255)),b:s=255&(r[6]?K(s):G(s,0,255)),a:a}}}(t):function(t){let e,i=tl.exec(t),s=255;if(!i)return;i[5]!==e&&(s=i[6]?K(+i[5]):J(+i[5]));let r=tp(+i[2]),a=i[3]/100,n=i[4]/100;return{r:(e="hwb"===i[1]?tf(tc,r,a,n):"hsv"===i[1]?tf(td,r,a,n):tf(th,r,a,n))[0],g:e[1],b:e[2],a:s}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=t_(this._rgb);return t&&(t.a=Q(t.a)),t}set rgb(t){this._rgb=tw(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${Q(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=tn(t=this._rgb)?ts:tr,t?"#"+e(t.r)+e(t.g)+e(t.b)+to(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=tu(t),i=e[0],s=tt(e[1]),r=tt(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${r}%, ${Q(t.a)})`:`hsl(${i}, ${s}%, ${r}%)`}(this._rgb):void 0}mix(t,e){if(t){let i,s=this.rgb,r=t.rgb,a=e===i?.5:e,n=2*a-1,o=s.a-r.a,l=((n*o==-1?n:(n+o)/(1+n*o))+1)/2;i=1-l,s.r=255&l*s.r+i*r.r+.5,s.g=255&l*s.g+i*r.g+.5,s.b=255&l*s.b+i*r.b+.5,s.a=a*s.a+(1-a)*r.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){let s=ty(Q(t.r)),r=ty(Q(t.g)),a=ty(Q(t.b));return{r:J(tb(s+i*(ty(Q(e.r))-s))),g:J(tb(r+i*(ty(Q(e.g))-r))),b:J(tb(a+i*(ty(Q(e.b))-a))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new tM(this.rgb)}alpha(t){return this._rgb.a=J(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=q(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return tv(this._rgb,2,t),this}darken(t){return tv(this._rgb,2,-t),this}saturate(t){return tv(this._rgb,1,t),this}desaturate(t){return tv(this._rgb,1,-t),this}rotate(t){var e,i;return e=this._rgb,(i=tu(e))[0]=tp(i[0]+t),e.r=(i=tf(th,i,void 0,void 0))[0],e.g=i[1],e.b=i[2],this}}function tk(){}let tD=(()=>{let t=0;return()=>t++})();function tC(t){return null==t}function tS(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function tP(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function tO(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function tE(t,e){return tO(t)?t:e}function tT(t,e){return void 0===t?e:t}let tN=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,tA=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function tj(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function tL(t,e,i,s){let r,a,n;if(tS(t))if(a=t.length,s)for(r=a-1;r>=0;r--)e.call(i,t[r],r);else for(r=0;r<a;r++)e.call(i,t[r],r);else if(tP(t))for(r=0,a=(n=Object.keys(t)).length;r<a;r++)e.call(i,t[n[r]],n[r])}function tI(t,e){let i,s,r,a;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(r=t[i],a=e[i],r.datasetIndex!==a.datasetIndex||r.index!==a.index)return!1;return!0}function tR(t){if(tS(t))return t.map(tR);if(tP(t)){let e=Object.create(null),i=Object.keys(t),s=i.length,r=0;for(;r<s;++r)e[i[r]]=tR(t[i[r]]);return e}return t}function tz(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function tF(t,e,i,s){if(!tz(t))return;let r=e[t],a=i[t];tP(r)&&tP(a)?tW(r,a,s):e[t]=tR(a)}function tW(t,e,i){let s,r=tS(e)?e:[e],a=r.length;if(!tP(t))return t;let n=(i=i||{}).merger||tF;for(let e=0;e<a;++e){if(!tP(s=r[e]))continue;let a=Object.keys(s);for(let e=0,r=a.length;e<r;++e)n(a[e],t,s,i)}return t}function tB(t,e){return tW(t,e,{merger:tV})}function tV(t,e,i){if(!tz(t))return;let s=e[t],r=i[t];tP(s)&&tP(r)?tB(s,r):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=tR(r))}let tH={"":t=>t,x:t=>t.x,y:t=>t.y};function tY(t,e){return(tH[e]||(tH[e]=function(t){let e=function(t){let e=t.split("."),i=[],s="";for(let t of e)(s+=t).endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function t$(t){return t.charAt(0).toUpperCase()+t.slice(1)}let tU=t=>void 0!==t,tZ=t=>"function"==typeof t,tX=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0},tq=Math.PI,tG=2*tq,tK=tG+tq,tJ=Number.POSITIVE_INFINITY,tQ=tq/180,t0=tq/2,t1=tq/4,t2=2*tq/3,t5=Math.log10,t3=Math.sign;function t4(t,e,i){return Math.abs(t-e)<i}function t8(t){let e=Math.round(t),i=Math.pow(10,Math.floor(t5(t=t4(t,e,t/1e3)?e:t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function t6(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function t9(t,e,i){let s,r,a;for(s=0,r=t.length;s<r;s++)isNaN(a=t[s][i])||(e.min=Math.min(e.min,a),e.max=Math.max(e.max,a))}function t7(t){return tq/180*t}function et(t){return 180/tq*t}function ee(t){if(!tO(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function ei(t,e){let i=e.x-t.x,s=e.y-t.y,r=Math.sqrt(i*i+s*s),a=Math.atan2(s,i);return a<-.5*tq&&(a+=tG),{angle:a,distance:r}}function es(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function er(t,e){return(t-e+tK)%tG-tq}function ea(t){return(t%tG+tG)%tG}function en(t,e,i,s){let r=ea(t),a=ea(e),n=ea(i),o=ea(a-r),l=ea(n-r),h=ea(r-a),d=ea(r-n);return r===a||r===n||s&&a===n||o>l&&h<d}function eo(t,e,i){return Math.max(e,Math.min(i,t))}function el(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function eh(t,e,i){let s;i=i||(i=>t[i]<e);let r=t.length-1,a=0;for(;r-a>1;)i(s=a+r>>1)?a=s:r=s;return{lo:a,hi:r}}let ed=(t,e,i,s)=>eh(t,i,s?s=>{let r=t[s][e];return r<i||r===i&&t[s+1][e]===i}:s=>t[s][e]<i),ec=(t,e,i)=>eh(t,i,s=>t[s][e]>=i),eu=["push","pop","shift","splice","unshift"];function ef(t,e){let i=t._chartjs;if(!i)return;let s=i.listeners,r=s.indexOf(e);-1!==r&&s.splice(r,1),s.length>0||(eu.forEach(e=>{delete t[e]}),delete t._chartjs)}function ep(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let eg="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function em(t,e){let i=[],s=!1;return function(...r){i=r,s||(s=!0,eg.call(window,()=>{s=!1,t.apply(e,i)}))}}let ex=t=>"start"===t?"left":"end"===t?"right":"center",eb=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,ey=(t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e;function ev(t,e,i){let s=e.length,r=0,a=s;if(t._sorted){let{iScale:n,vScale:o,_parsed:l}=t,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,d=n.axis,{min:c,max:u,minDefined:f,maxDefined:p}=n.getUserBounds();if(f){if(r=Math.min(ed(l,d,c).lo,i?s:ed(e,d,n.getPixelForValue(c)).lo),h){let t=l.slice(0,r+1).reverse().findIndex(t=>!tC(t[o.axis]));r-=Math.max(0,t)}r=eo(r,0,s-1)}if(p){let t=Math.max(ed(l,n.axis,u,!0).hi+1,i?0:ed(e,d,n.getPixelForValue(u),!0).hi+1);if(h){let e=l.slice(t-1).findIndex(t=>!tC(t[o.axis]));t+=Math.max(0,e)}a=eo(t,r,s)-r}else a=s-r}return{start:r,count:a}}function e_(t){let{xScale:e,yScale:i,_scaleRanges:s}=t,r={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=r,!0;let a=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,r),a}let ew=t=>0===t||1===t,eM=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*tG/i)),ek=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*tG/i)+1,eD={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*t0)+1,easeOutSine:t=>Math.sin(t*t0),easeInOutSine:t=>-.5*(Math.cos(tq*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>ew(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>ew(t)?t:eM(t,.075,.3),easeOutElastic:t=>ew(t)?t:ek(t,.075,.3),easeInOutElastic:t=>ew(t)?t:t<.5?.5*eM(2*t,.1125,.45):.5+.5*ek(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-eD.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*eD.easeInBounce(2*t):.5*eD.easeOutBounce(2*t-1)+.5};function eC(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function eS(t){return eC(t)?t:new tM(t)}function eP(t){return eC(t)?t:new tM(t).saturate(.5).darken(.1).hexString()}let eO=["x","y","borderWidth","radius","tension"],eE=["color","borderColor","backgroundColor"],eT=new Map;function eN(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),s=eT.get(i);return s||(s=new Intl.NumberFormat(t,e),eT.set(i,s)),s})(e,i).format(t)}let eA={values:t=>tS(t)?t:""+t,numeric(t,e,i){let s;if(0===t)return"0";let r=this.chart.options.locale,a=t;if(i.length>1){var n,o;let e,r=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(r<1e-4||r>1e15)&&(s="scientific"),n=t,Math.abs(e=(o=i).length>3?o[2].value-o[1].value:o[1].value-o[0].value)>=1&&n!==Math.floor(n)&&(e=n-Math.floor(n)),a=e}let l=t5(Math.abs(a)),h=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),d={notation:s,minimumFractionDigits:h,maximumFractionDigits:h};return Object.assign(d,this.options.ticks.format),eN(t,r,d)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(t5(t))))||e>.8*i.length?eA.numeric.call(this,t,e,i):""}};var ej={formatters:eA};let eL=Object.create(null),eI=Object.create(null);function eR(t,e){if(!e)return t;let i=e.split(".");for(let e=0,s=i.length;e<s;++e){let s=i[e];t=t[s]||(t[s]=Object.create(null))}return t}function ez(t,e,i){return"string"==typeof e?tW(eR(t,e),i):tW(eR(t,""),e)}class eF{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>eP(e.backgroundColor),this.hoverBorderColor=(t,e)=>eP(e.borderColor),this.hoverColor=(t,e)=>eP(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return ez(this,t,e)}get(t){return eR(this,t)}describe(t,e){return ez(eI,t,e)}override(t,e){return ez(eL,t,e)}route(t,e,i,s){let r=eR(this,t),a=eR(this,i),n="_"+e;Object.defineProperties(r,{[n]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[n],e=a[s];return tP(t)?Object.assign({},e,t):tT(t,e)},set(t){this[n]=t}}})}apply(t){t.forEach(t=>t(this))}}var eW=new eF({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:eE},numbers:{type:"number",properties:eO}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:ej.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function eB(t,e,i,s,r){let a=e[r];return a||(a=e[r]=t.measureText(r).width,i.push(r)),a>s&&(s=a),s}function eV(t,e,i){let s=t.currentDevicePixelRatio,r=0!==i?Math.max(i/2,.5):0;return Math.round((e-r)*s)/s+r}function eH(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function eY(t,e,i,s){e$(t,e,i,s,null)}function e$(t,e,i,s,r){let a,n,o,l,h,d,c,u,f=e.pointStyle,p=e.rotation,g=e.radius,m=(p||0)*tQ;if(f&&"object"==typeof f&&("[object HTMLImageElement]"===(a=f.toString())||"[object HTMLCanvasElement]"===a)){t.save(),t.translate(i,s),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),t.restore();return}if(!isNaN(g)&&!(g<=0)){switch(t.beginPath(),f){default:r?t.ellipse(i,s,r/2,g,0,0,tG):t.arc(i,s,g,0,tG),t.closePath();break;case"triangle":d=r?r/2:g,t.moveTo(i+Math.sin(m)*d,s-Math.cos(m)*g),m+=t2,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*g),m+=t2,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*g),t.closePath();break;case"rectRounded":h=.516*g,n=Math.cos(m+t1)*(l=g-h),c=Math.cos(m+t1)*(r?r/2-h:l),o=Math.sin(m+t1)*l,u=Math.sin(m+t1)*(r?r/2-h:l),t.arc(i-c,s-o,h,m-tq,m-t0),t.arc(i+u,s-n,h,m-t0,m),t.arc(i+c,s+o,h,m,m+t0),t.arc(i-u,s+n,h,m+t0,m+tq),t.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,d=r?r/2:l,t.rect(i-d,s-l,2*d,2*l);break}m+=t1;case"rectRot":c=Math.cos(m)*(r?r/2:g),n=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(r?r/2:g),t.moveTo(i-c,s-o),t.lineTo(i+u,s-n),t.lineTo(i+c,s+o),t.lineTo(i-u,s+n),t.closePath();break;case"crossRot":m+=t1;case"cross":c=Math.cos(m)*(r?r/2:g),n=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(r?r/2:g),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n);break;case"star":c=Math.cos(m)*(r?r/2:g),n=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(r?r/2:g),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n),m+=t1,c=Math.cos(m)*(r?r/2:g),n=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(r?r/2:g),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-n),t.lineTo(i-u,s+n);break;case"line":n=r?r/2:Math.cos(m)*g,o=Math.sin(m)*g,t.moveTo(i-n,s-o),t.lineTo(i+n,s+o);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(r?r/2:g),s+Math.sin(m)*g);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function eU(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function eZ(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function eX(t){t.restore()}function eq(t,e,i,s,r){if(!e)return t.lineTo(i.x,i.y);if("middle"===r){let s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===r!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function eG(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function eK(t,e,i,s,r,a={}){let n,o,l=tS(e)?e:[e],h=a.strokeWidth>0&&""!==a.strokeColor;for(t.save(),t.font=r.string,a.translation&&t.translate(a.translation[0],a.translation[1]),tC(a.rotation)||t.rotate(a.rotation),a.color&&(t.fillStyle=a.color),a.textAlign&&(t.textAlign=a.textAlign),a.textBaseline&&(t.textBaseline=a.textBaseline),n=0;n<l.length;++n)o=l[n],a.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,a.backdrop),h&&(a.strokeColor&&(t.strokeStyle=a.strokeColor),tC(a.strokeWidth)||(t.lineWidth=a.strokeWidth),t.strokeText(o,i,s,a.maxWidth)),t.fillText(o,i,s,a.maxWidth),function(t,e,i,s,r){if(r.strikethrough||r.underline){let a=t.measureText(s),n=e-a.actualBoundingBoxLeft,o=e+a.actualBoundingBoxRight,l=i-a.actualBoundingBoxAscent,h=i+a.actualBoundingBoxDescent,d=r.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=r.decorationWidth||2,t.moveTo(n,d),t.lineTo(o,d),t.stroke()}}(t,i,s,o,a),s+=Number(r.lineHeight);t.restore()}function eJ(t,e){let{x:i,y:s,w:r,h:a,radius:n}=e;t.arc(i+n.topLeft,s+n.topLeft,n.topLeft,1.5*tq,tq,!0),t.lineTo(i,s+a-n.bottomLeft),t.arc(i+n.bottomLeft,s+a-n.bottomLeft,n.bottomLeft,tq,t0,!0),t.lineTo(i+r-n.bottomRight,s+a),t.arc(i+r-n.bottomRight,s+a-n.bottomRight,n.bottomRight,t0,0,!0),t.lineTo(i+r,s+n.topRight),t.arc(i+r-n.topRight,s+n.topRight,n.topRight,0,-t0,!0),t.lineTo(i+n.topLeft,s)}let eQ=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,e0=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,e1=t=>+t||0;function e2(t,e){let i={},s=tP(e),r=s?Object.keys(e):e,a=tP(t)?s?i=>tT(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of r)i[t]=e1(a(t));return i}function e5(t){return e2(t,{top:"y",right:"x",bottom:"y",left:"x"})}function e3(t){return e2(t,["topLeft","topRight","bottomLeft","bottomRight"])}function e4(t){let e=e5(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function e8(t,e){t=t||{},e=e||eW.font;let i=tT(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=tT(t.style,e.style);s&&!(""+s).match(e0)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let r={family:tT(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(eQ);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(tT(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:tT(t.weight,e.weight),string:""};return r.string=!r||tC(r.size)||tC(r.family)?null:(r.style?r.style+" ":"")+(r.weight?r.weight+" ":"")+r.size+"px "+r.family,r}function e6(t,e,i,s){let r,a,n,o=!0;for(r=0,a=t.length;r<a;++r)if(void 0!==(n=t[r])&&(void 0!==e&&"function"==typeof n&&(n=n(e),o=!1),void 0!==i&&tS(n)&&(n=n[i%n.length],o=!1),void 0!==n))return s&&!o&&(s.cacheable=!1),n}function e9(t,e){return Object.assign(Object.create(t),e)}function e7(t,e=[""],i,s,r=()=>t[0]){let a=i||t;return void 0===s&&(s=ih("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:a,_fallback:s,_getTarget:r,override:i=>e7([i,...t],e,a,s)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>ir(i,s,()=>(function(t,e,i,s){let r;for(let a of e)if(void 0!==(r=ih(ii(a,t),i)))return is(t,r)?io(i,s,t,r):r})(s,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>id(t).includes(e),ownKeys:t=>id(t),set(t,e,i){let s=t._storage||(t._storage=r());return t[e]=s[e]=i,delete t._keys,!0}})}function it(t,e,i,s){return new Proxy({_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:ie(t,s),setContext:e=>it(t,e,i,s),override:r=>it(t.override(r),e,i,s)},{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>ir(t,e,()=>(function(t,e,i){let{_proxy:s,_context:r,_subProxy:a,_descriptors:n}=t,o=s[e];return tZ(o)&&n.isScriptable(e)&&(o=function(t,e,i,s){let{_proxy:r,_context:a,_subProxy:n,_stack:o}=i;if(o.has(t))throw Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(a,n||s);return o.delete(t),is(t,l)&&(l=io(r._scopes,r,t,l)),l}(e,o,t,i)),tS(o)&&o.length&&(o=function(t,e,i,s){let{_proxy:r,_context:a,_subProxy:n,_descriptors:o}=i;if(void 0!==a.index&&s(t))return e[a.index%e.length];if(tP(e[0])){let i=e,s=r._scopes.filter(t=>t!==i);for(let l of(e=[],i)){let i=io(s,r,t,l);e.push(it(i,a,n&&n[t],o))}}return e}(e,o,t,n.isIndexable)),is(e,o)&&(o=it(o,r,a&&a[e],n)),o})(t,e,i)),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function ie(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:r=e.allKeys}=t;return{allKeys:r,scriptable:i,indexable:s,isScriptable:tZ(i)?i:()=>i,isIndexable:tZ(s)?s:()=>s}}let ii=(t,e)=>t?t+t$(e):e,is=(t,e)=>tP(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function ir(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let s=i();return t[e]=s,s}let ia=(t,e)=>!0===t?e:"string"==typeof t?tY(e,t):void 0;function io(t,e,i,s){var r;let a=e._rootScopes,n=(r=e._fallback,tZ(r)?r(i,s):r),o=[...t,...a],l=new Set;l.add(s);let h=il(l,o,i,n||i,s);return null!==h&&(void 0===n||n===i||null!==(h=il(l,o,n,h,s)))&&e7(Array.from(l),[""],a,n,()=>(function(t,e,i){let s=t._getTarget();e in s||(s[e]={});let r=s[e];return tS(r)&&tP(i)?i:r||{}})(e,i,s))}function il(t,e,i,s,r){for(;i;)i=function(t,e,i,s,r){for(let n of e){let e=ia(i,n);if(e){var a;t.add(e);let n=(a=e._fallback,tZ(a)?a(i,r):a);if(void 0!==n&&n!==i&&n!==s)return n}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}(t,e,i,s,r);return i}function ih(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function id(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function ic(t,e,i,s){let r,a,n,{iScale:o}=t,{key:l="r"}=this._parsing,h=Array(s);for(r=0;r<s;++r)n=e[a=r+i],h[r]={r:o.parse(tY(n,l),a)};return h}let iu=Number.EPSILON||1e-14,ip=(t,e)=>e<t.length&&!t[e].skip&&t[e],ig=t=>"x"===t?"y":"x";function im(t,e,i){return Math.max(Math.min(t,i),e)}function ix(){return"undefined"!=typeof window&&"undefined"!=typeof document}function ib(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function iy(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}let iv=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),i_=["top","right","bottom","left"];function iw(t,e,i){let s={};i=i?"-"+i:"";for(let r=0;r<4;r++){let a=i_[r];s[a]=parseFloat(t[e+"-"+a+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}let iM=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function ik(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:s}=e,r=iv(i),a="border-box"===r.boxSizing,n=iw(r,"padding"),o=iw(r,"border","width"),{x:l,y:h,box:d}=function(t,e){let i,s,r=t.touches,a=r&&r.length?r[0]:t,{offsetX:n,offsetY:o}=a,l=!1;if(iM(n,o,t.target))i=n,s=o;else{let t=e.getBoundingClientRect();i=a.clientX-t.left,s=a.clientY-t.top,l=!0}return{x:i,y:s,box:l}}(t,i),c=n.left+(d&&o.left),u=n.top+(d&&o.top),{width:f,height:p}=e;return a&&(f-=n.width+o.width,p-=n.height+o.height),{x:Math.round((l-c)/f*i.width/s),y:Math.round((h-u)/p*i.height/s)}}let iD=t=>Math.round(10*t)/10;function iC(t,e,i){let s=e||1,r=Math.floor(t.height*s),a=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let n=t.canvas;return n.style&&(i||!n.style.height&&!n.style.width)&&(n.style.height=`${t.height}px`,n.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||n.height!==r||n.width!==a)&&(t.currentDevicePixelRatio=s,n.height=r,n.width=a,t.ctx.setTransform(s,0,0,s,0,0),!0)}let iS=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};ix()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function iP(t,e){let i=iv(t).getPropertyValue(e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function iO(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function iE(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function iT(t,e,i,s){let r={x:t.cp2x,y:t.cp2y},a={x:e.cp1x,y:e.cp1y},n=iO(t,r,i),o=iO(r,a,i),l=iO(a,e,i),h=iO(n,o,i),d=iO(o,l,i);return iO(h,d,i)}function iN(t,e,i){var s;return t?(s=i,{x:t=>e+e+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function iA(t,e){let i,s;("ltr"===e||"rtl"===e)&&(s=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function ij(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function iL(t){return"angle"===t?{between:en,compare:er,normalize:ea}:{between:el,compare:(t,e)=>t-e,normalize:t=>t}}function iI({start:t,end:e,count:i,loop:s,style:r}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:r}}function iR(t,e,i){let s,r,a;if(!i)return[t];let{property:n,start:o,end:l}=i,h=e.length,{compare:d,between:c,normalize:u}=iL(n),{start:f,end:p,loop:g,style:m}=function(t,e,i){let s,{property:r,start:a,end:n}=i,{between:o,normalize:l}=iL(r),h=e.length,{start:d,end:c,loop:u}=t;if(u){for(d+=h,c+=h,s=0;s<h&&o(l(e[d%h][r]),a,n);++s)d--,c--;d%=h,c%=h}return c<d&&(c+=h),{start:d,end:c,loop:u,style:t.style}}(t,e,i),x=[],b=!1,y=null,v=()=>c(o,a,s)&&0!==d(o,a),_=()=>0===d(l,s)||c(l,a,s),w=()=>b||v(),M=()=>!b||_();for(let t=f,i=f;t<=p;++t)(r=e[t%h]).skip||(s=u(r[n]))!==a&&(b=c(s,o,l),null===y&&w()&&(y=0===d(s,o)?t:i),null!==y&&M()&&(x.push(iI({start:y,end:t,loop:g,count:h,style:m})),y=null),i=t,a=s);return null!==y&&x.push(iI({start:y,end:p,loop:g,count:h,style:m})),x}function iz(t,e){let i=[],s=t.segments;for(let r=0;r<s.length;r++){let a=iR(s[r],t.points,e);a.length&&i.push(...a)}return i}function iF(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){let r=t._chart.getContext(),a=iW(t.options),{_datasetIndex:n,options:{spanGaps:o}}=t,l=i.length,h=[],d=a,c=e[0].start,u=c;function f(t,e,s,r){let a=o?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=a;for(;i[e%l].skip;)e+=a;t%l!=e%l&&(h.push({start:t%l,end:e%l,loop:s,style:r}),d=r,c=e%l)}}for(let t of e){let e,a=i[(c=o?c:t.start)%l];for(u=c+1;u<=t.end;u++){let o=i[u%l];(function(t,e){if(!e)return!1;let i=[],s=function(t,e){return eC(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)})(e=iW(s.setContext(e9(r,{type:"segment",p0:a,p1:o,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:n}))),d)&&f(c,u-1,t.loop,d),a=o,d=e}c<u-1&&f(c,u-1,t.loop,d)}return h}(t,e,i,s):e}function iW(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function iB(t,e,i){return t.options.clip?t[i]:e[i]}function iV(t,e){let i=e._clip;if(i.disabled)return!1;let s=function(t,e){let{xScale:i,yScale:s}=t;return i&&s?{left:iB(i,e,"left"),right:iB(i,e,"right"),top:iB(s,e,"top"),bottom:iB(s,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:s.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:s.right+(!0===i.right?0:i.right),top:!1===i.top?0:s.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:s.bottom+(!0===i.bottom?0:i.bottom)}}class iH{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let r=e.listeners[s],a=e.duration;r.forEach(s=>s({chart:t,initial:e.initial,numSteps:a,currentStep:Math.min(i-e.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=eg.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{let r;if(!i.running||!i.items.length)return;let a=i.items,n=a.length-1,o=!1;for(;n>=0;--n)(r=a[n])._active?(r._total>i.duration&&(i.duration=r._total),r.tick(t),o=!0):(a[n]=a[a.length-1],a.pop());o&&(s.draw(),this._notify(s,i,t,"progress")),a.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=a.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!!e&&!!e.running&&!!e.items.length}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var iY=new iH;let i$="transparent",iU={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){let s=eS(t||i$),r=s.valid&&eS(e||i$);return r&&r.valid?r.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class iZ{constructor(t,e,i,s){let r=e[i];s=e6([t.to,s,r,t.from]);let a=e6([t.from,r,s]);this._active=!0,this._fn=t.fn||iU[t.type||typeof a],this._easing=eD[t.easing]||eD.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=a,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],r=i-this._start,a=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(a,t.duration)),this._total+=r,this._loop=!!t.loop,this._to=e6([t.to,e,s,t.from]),this._from=e6([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e,i=t-this._start,s=this._duration,r=this._prop,a=this._from,n=this._loop,o=this._to;if(this._active=a!==o&&(n||i<s),!this._active){this._target[r]=o,this._notify(!0);return}if(i<0){this._target[r]=a;return}e=i/s%2,e=n&&e>1?2-e:e,e=this._easing(Math.min(1,Math.max(0,e))),this._target[r]=this._fn(a,o,e)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class iX{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!tP(t))return;let e=Object.keys(eW.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{let r=t[s];if(!tP(r))return;let a={};for(let t of e)a[t]=r[t];(tS(r.properties)&&r.properties||[s]).forEach(t=>{t!==s&&i.has(t)||i.set(t,a)})})}_animateOptions(t,e){let i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}(t,i);if(!s)return[];let r=this._createAnimations(s,i);return i.$shared&&(function(t,e){let i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){let r=t[s[e]];r&&r.active()&&i.push(r.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),r}_createAnimations(t,e){let i,s=this._properties,r=[],a=t.$animations||(t.$animations={}),n=Object.keys(e),o=Date.now();for(i=n.length-1;i>=0;--i){let l=n[i];if("$"===l.charAt(0))continue;if("options"===l){r.push(...this._animateOptions(t,e));continue}let h=e[l],d=a[l],c=s.get(l);if(d)if(c&&d.active()){d.update(c,h,o);continue}else d.cancel();if(!c||!c.duration){t[l]=h;continue}a[l]=d=new iZ(c,t,l,h),r.push(d)}return r}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);let i=this._createAnimations(t,e);if(i.length)return iY.add(this._chart,i),!0}}function iq(t,e){let i=t&&t.options||{},s=i.reverse,r=void 0===i.min?e:0,a=void 0===i.max?e:0;return{start:s?a:r,end:s?r:a}}function iG(t,e){let i,s,r=[],a=t._getSortedDatasetMetas(e);for(i=0,s=a.length;i<s;++i)r.push(a[i].index);return r}function iK(t,e,i,s={}){let r,a,n,o,l=t.keys,h="single"===s.mode;if(null===e)return;let d=!1;for(r=0,a=l.length;r<a;++r){if((n=+l[r])===i){if(d=!0,s.all)continue;break}tO(o=t.values[n])&&(h||0===e||t3(e)===t3(o))&&(e+=o)}return d||s.all?e:0}function iJ(t,e){let i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function iQ(t,e,i,s){for(let r of e.getMatchingVisibleMetas(s).reverse()){let e=t[r.index];if(i&&e>0||!i&&e<0)return r.index}return null}function i0(t,e){let i,{chart:s,_cachedMeta:r}=t,a=s._stacks||(s._stacks={}),{iScale:n,vScale:o,index:l}=r,h=n.axis,d=o.axis,c=`${n.id}.${o.id}.${r.stack||r.type}`,u=e.length;for(let t=0;t<u;++t){let s=e[t],{[h]:n,[d]:u}=s;(i=(s._stacks||(s._stacks={}))[d]=function(t,e,i){let s=t[e]||(t[e]={});return s[i]||(s[i]={})}(a,c,n))[l]=u,i._top=iQ(i,o,!0,r.type),i._bottom=iQ(i,o,!1,r.type),(i._visualValues||(i._visualValues={}))[l]=u}}function i1(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function i2(t,e){let i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s)for(let r of e=e||t._parsed){let t=r._stacks;if(!t||void 0===t[s]||void 0===t[s][i])return;delete t[s][i],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[i]&&delete t[s]._visualValues[i]}}let i5=t=>"reset"===t||"none"===t,i3=(t,e)=>e?t:Object.assign({},t),i4=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:iG(i,!0),values:null};class i8{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=iJ(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&i2(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,r=e.xAxisID=tT(i.xAxisID,i1(t,"x")),a=e.yAxisID=tT(i.yAxisID,i1(t,"y")),n=e.rAxisID=tT(i.rAxisID,i1(t,"r")),o=e.indexAxis,l=e.iAxisID=s(o,r,a,n),h=e.vAxisID=s(o,a,r,n);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(a),e.rScale=this.getScaleForId(n),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&ef(this._data,this),t._stacked&&i2(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(tP(e)){let t=this._cachedMeta;this._data=function(t,e){let i,s,r,{iScale:a,vScale:n}=e,o="x"===a.axis?"x":"y",l="x"===n.axis?"x":"y",h=Object.keys(t),d=Array(h.length);for(i=0,s=h.length;i<s;++i)r=h[i],d[i]={[o]:r,[l]:t[r]};return d}(e,t)}else if(i!==e){if(i){ef(i,this);let t=this._cachedMeta;i2(t),t._parsed=[]}e&&Object.isExtensible(e)&&function(t,e){if(t._chartjs)return t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),eu.forEach(e=>{let i="_onData"+t$(e),s=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let r=s.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),r}})})}(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let r=e._stacked;e._stacked=iJ(e.vScale,e),e.stack!==i.stack&&(s=!0,i2(e),e.stack=i.stack),this._resyncElements(t),(s||r!==e._stacked)&&(i0(this,e._parsed),e._stacked=iJ(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let i,s,r,{_cachedMeta:a,_data:n}=this,{iScale:o,_stacked:l}=a,h=o.axis,d=0===t&&e===n.length||a._sorted,c=t>0&&a._parsed[t-1];if(!1===this._parsing)a._parsed=n,a._sorted=!0,r=n;else{r=tS(n[t])?this.parseArrayData(a,n,t,e):tP(n[t])?this.parseObjectData(a,n,t,e):this.parsePrimitiveData(a,n,t,e);let o=()=>null===s[h]||c&&s[h]<c[h];for(i=0;i<e;++i)a._parsed[i+t]=s=r[i],d&&(o()&&(d=!1),c=s);a._sorted=d}l&&i0(this,r)}parsePrimitiveData(t,e,i,s){let r,a,{iScale:n,vScale:o}=t,l=n.axis,h=o.axis,d=n.getLabels(),c=n===o,u=Array(s);for(r=0;r<s;++r)a=r+i,u[r]={[l]:c||n.parse(d[a],a),[h]:o.parse(e[a],a)};return u}parseArrayData(t,e,i,s){let r,a,n,{xScale:o,yScale:l}=t,h=Array(s);for(r=0;r<s;++r)n=e[a=r+i],h[r]={x:o.parse(n[0],a),y:l.parse(n[1],a)};return h}parseObjectData(t,e,i,s){let r,a,n,{xScale:o,yScale:l}=t,{xAxisKey:h="x",yAxisKey:d="y"}=this._parsing,c=Array(s);for(r=0;r<s;++r)n=e[a=r+i],c[r]={x:o.parse(tY(n,h),a),y:l.parse(tY(n,d),a)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,r=this._cachedMeta,a=e[t.axis];return iK({keys:iG(s,!0),values:e._stacks[t.axis]._visualValues},a,r.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let r=i[e.axis],a=null===r?NaN:r,n=s&&i._stacks[e.axis];s&&n&&(s.values=n,a=iK(s,r,this._cachedMeta.index)),t.min=Math.min(t.min,a),t.max=Math.max(t.max,a)}getMinMax(t,e){let i,s,r=this._cachedMeta,a=r._parsed,n=r._sorted&&t===r.iScale,o=a.length,l=this._getOtherScale(t),h=i4(e,r,this.chart),d={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:c,max:u}=function(t){let{min:e,max:i,minDefined:s,maxDefined:r}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:r?i:Number.POSITIVE_INFINITY}}(l);function f(){let e=(s=a[i])[l.axis];return!tO(s[t.axis])||c>e||u<e}for(i=0;i<o&&(f()||(this.updateRangeFromParsed(d,t,s,h),!n));++i);if(n){for(i=o-1;i>=0;--i)if(!f()){this.updateRangeFromParsed(d,t,s,h);break}}return d}getAllParsedValues(t){let e,i,s,r=this._cachedMeta._parsed,a=[];for(e=0,i=r.length;e<i;++e)tO(s=r[e][t.axis])&&a.push(s);return a}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,r=this.getParsed(t);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(t){var e;let i,s,r,a,n=this._cachedMeta;this.update(t||"default"),tP(e=tT(this.options.clip,function(t,e,i){if(!1===i)return!1;let s=iq(t,i),r=iq(e,i);return{top:r.end,right:s.end,bottom:r.start,left:s.start}}(n.xScale,n.yScale,this.getMaxOverflow())))?(i=e.top,s=e.right,r=e.bottom,a=e.left):i=s=r=a=e,n._clip={top:i,right:s,bottom:r,left:a,disabled:!1===e}}update(t){}draw(){let t,e=this._ctx,i=this.chart,s=this._cachedMeta,r=s.data||[],a=i.chartArea,n=[],o=this._drawStart||0,l=this._drawCount||r.length-o,h=this.options.drawActiveElementsOnTop;for(s.dataset&&s.dataset.draw(e,a,o,l),t=o;t<o+l;++t){let i=r[t];i.hidden||(i.active&&h?n.push(i):i.draw(e,a))}for(t=0;t<n.length;++t)n[t].draw(e,a)}getStyle(t,e){let i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){var s,r;let a,n=this.getDataset();if(t>=0&&t<this._cachedMeta.data.length){let e=this._cachedMeta.data[t];(a=e.$context||(s=this.getContext(),e.$context=e9(s,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"}))).parsed=this.getParsed(t),a.raw=n.data[t],a.index=a.dataIndex=t}else(a=this.$context||(this.$context=e9(this.chart.getContext(),{active:!1,dataset:void 0,datasetIndex:r=this.index,index:r,mode:"default",type:"dataset"}))).dataset=n,a.index=a.datasetIndex=this.index;return a.active=!!e,a.mode=i,a}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s="active"===e,r=this._cachedDataOpts,a=t+"-"+e,n=r[a],o=this.enableOptionSharing&&tU(i);if(n)return i3(n,o);let l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),d=s?[`${t}Hover`,"hover",t,""]:[t,""],c=l.getOptionScopes(this.getDataset(),h),u=Object.keys(eW.elements[t]),f=l.resolveNamedOptions(c,u,()=>this.getContext(i,s,e),d);return f.$shared&&(f.$shared=o,r[a]=Object.freeze(i3(f,o))),f}_resolveAnimations(t,e,i){let s,r=this.chart,a=this._cachedDataOpts,n=`animation-${e}`,o=a[n];if(o)return o;if(!1!==r.options.animation){let r=this.chart.config,a=r.datasetAnimationScopeKeys(this._type,e),n=r.getOptionScopes(this.getDataset(),a);s=r.createResolver(n,this.getContext(t,i,e))}let l=new iX(r,s&&s.animations);return s&&s._cacheable&&(a[n]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||i5(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,r=this.getSharedOptions(i),a=this.includeOptions(e,r)||r!==s;return this.updateSharedOptions(r,e,i),{sharedOptions:r,includeOptions:a}}updateElement(t,e,i,s){i5(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!i5(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let r=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];let s=i.length,r=e.length,a=Math.min(r,s);a&&this.parse(0,a),r>s?this._insertElements(s,r-s,t):r<s&&this._removeElements(r,s-r)}_insertElements(t,e,i=!0){let s,r=this._cachedMeta,a=r.data,n=t+e,o=t=>{for(t.length+=e,s=t.length-1;s>=n;s--)t[s]=t[s-e]};for(o(a),s=t;s<n;++s)a[s]=new this.dataElementType;this._parsing&&o(r._parsed),this.parse(t,e),i&&this.updateElements(a,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&i2(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function i6(t,e,i,s){return tS(t)?!function(t,e,i,s){let r=i.parse(t[0],s),a=i.parse(t[1],s),n=Math.min(r,a),o=Math.max(r,a),l=n,h=o;Math.abs(n)>Math.abs(o)&&(l=o,h=n),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:r,end:a,min:n,max:o}}(t,e,i,s):e[i.axis]=i.parse(t,s),e}function i9(t,e,i,s){let r,a,n,o,l=t.iScale,h=t.vScale,d=l.getLabels(),c=l===h,u=[];for(r=i,a=i+s;r<a;++r)o=e[r],(n={})[l.axis]=c||l.parse(d[r],r),u.push(i6(o,n,h,r));return u}function i7(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function st(t,e,i,s){var r,a,n;return t=s?se((r=t,a=e,n=i,t=r===a?n:r===n?a:r),i,e):se(t,e,i)}function se(t,e,i){return"start"===t?e:"end"===t?i:t}class si extends i8{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return i9(t,e,i,s)}parseArrayData(t,e,i,s){return i9(t,e,i,s)}parseObjectData(t,e,i,s){let r,a,n,o,{iScale:l,vScale:h}=t,{xAxisKey:d="x",yAxisKey:c="y"}=this._parsing,u="x"===l.axis?d:c,f="x"===h.axis?d:c,p=[];for(r=i,a=i+s;r<a;++r)o=e[r],(n={})[l.axis]=l.parse(tY(o,u),r),p.push(i6(tY(o,f),n,h,r));return p}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let r=i._custom;r&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let{iScale:e,vScale:i}=this._cachedMeta,s=this.getParsed(t),r=s._custom,a=i7(r)?"["+r.start+", "+r.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+e.getLabelForValue(s[e.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let r="reset"===s,{index:a,_cachedMeta:{vScale:n}}=this,o=n.getBasePixel(),l=n.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:c}=this._getSharedOptions(e,s);for(let u=e;u<e+i;u++){let e=this.getParsed(u),i=r||tC(e[n.axis])?{base:o,head:o}:this._calculateBarValuePixels(u),f=this._calculateBarIndexPixels(u,h),p=(e._stacks||{})[n.axis],g={horizontal:l,base:i.base,enableBorderRadius:!p||i7(e._custom)||a===p._top||a===p._bottom,x:l?i.head:f.center,y:l?f.center:i.head,height:l?f.size:Math.abs(i.size),width:l?Math.abs(i.size):f.size};c&&(g.options=d||this.resolveDataElementOptions(u,t[u].active?"active":s));let m=g.options||t[u].options;!function(t,e,i,s){let r,a,n,o,l,h=e.borderSkipped,d={};if(!h){t.borderSkipped=d;return}if(!0===h){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:c,end:u,reverse:f,top:p,bottom:g}=(t.horizontal?(r=t.base>t.x,a="left",n="right"):(r=t.base<t.y,a="bottom",n="top"),r?(o="end",l="start"):(o="start",l="end"),{start:a,end:n,reverse:r,top:o,bottom:l});"middle"===h&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?h=p:(i._bottom||0)===s?h=g:(d[st(g,c,u,f)]=!0,h=p)),d[st(h,c,u,f)]=!0,t.borderSkipped=d}(g,m,p,a),function(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?.33*(1===i):e}(g,m,h.ratio),this.updateElement(t[u],u,g,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),r=i.options.stacked,a=[],n=this._cachedMeta.controller.getParsed(e),o=n&&n[i.axis],l=t=>{let e=t._parsed.find(t=>t[i.axis]===o),s=e&&e[t.vScale.axis];if(tC(s)||isNaN(s))return!0};for(let i of s)if(!(void 0!==e&&l(i))&&((!1===r||-1===a.indexOf(i.stack)||void 0===r&&void 0===i.stack)&&a.push(i.stack),i.index===t))break;return a.length||a.push(void 0),a}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){let s=this._getStacks(t,i),r=void 0!==e?s.indexOf(e):-1;return -1===r?s.length-1:r}_getRuler(){let t,e,i=this.options,s=this._cachedMeta,r=s.iScale,a=[];for(t=0,e=s.data.length;t<e;++t)a.push(r.getPixelForValue(this.getParsed(t)[r.axis],t));let n=i.barThickness;return{min:n||function(t){let e,i,s,r,a=t.iScale,n=function(t,e){if(!t._cache.$bar){let i=t.getMatchingVisibleMetas(e),s=[];for(let e=0,r=i.length;e<r;e++)s=s.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=ep(s.sort((t,e)=>t-e))}return t._cache.$bar}(a,t.type),o=a._length,l=()=>{32767!==s&&-32768!==s&&(tU(r)&&(o=Math.min(o,Math.abs(s-r)||o)),r=s)};for(e=0,i=n.length;e<i;++e)s=a.getPixelForValue(n[e]),l();for(e=0,r=void 0,i=a.ticks.length;e<i;++e)s=a.getPixelForTick(e),l();return o}(s),pixels:a,start:r._startPixel,end:r._endPixel,stackCount:this._getStackCount(),scale:r,grouped:i.grouped,ratio:n?1:i.categoryPercentage*i.barPercentage}}_calculateBarValuePixels(t){let e,i,{_cachedMeta:{vScale:s,_stacked:r,index:a},options:{base:n,minBarLength:o}}=this,l=n||0,h=this.getParsed(t),d=h._custom,c=i7(d),u=h[s.axis],f=0,p=r?this.applyStack(s,h,r):u;p!==u&&(f=p-u,p=u),c&&(u=d.barStart,p=d.barEnd-d.barStart,0!==u&&t3(u)!==t3(d.barEnd)&&(f=0),f+=u);let g=tC(n)||c?f:n,m=s.getPixelForValue(g);if(Math.abs(i=(e=this.chart.getDataVisibility(t)?s.getPixelForValue(f+p):m)-m)<o){var x;i=(0!==(x=i)?t3(x):(s.isHorizontal()?1:-1)*(s.min>=l?1:-1))*o,u===l&&(m-=i/2);let t=s.getPixelForDecimal(0),n=s.getPixelForDecimal(1),d=Math.min(t,n);e=(m=Math.max(Math.min(m,Math.max(t,n)),d))+i,r&&!c&&(h._stacks[s.axis]._visualValues[a]=s.getValueForPixel(e)-s.getValueForPixel(m))}if(m===s.getPixelForValue(l)){let t=t3(i)*s.getLineWidthForValue(l)/2;m+=t,i-=t}return{size:i,base:m,head:e,center:e+i/2}}_calculateBarIndexPixels(t,e){let i,s,r=e.scale,a=this.options,n=a.skipNull,o=tT(a.maxBarThickness,1/0);if(e.grouped){let r=n?this._getStackCount(t):e.stackCount,l="flex"===a.barThickness?function(t,e,i,s){let r=e.pixels,a=r[t],n=t>0?r[t-1]:null,o=t<r.length-1?r[t+1]:null,l=i.categoryPercentage;null===n&&(n=a-(null===o?e.end-e.start:o-a)),null===o&&(o=a+a-n);let h=a-(a-Math.min(n,o))/2*l;return{chunk:Math.abs(o-n)/2*l/s,ratio:i.barPercentage,start:h}}(t,e,a,r):function(t,e,i,s){let r,a,n=i.barThickness;return tC(n)?(r=e.min*i.categoryPercentage,a=i.barPercentage):(r=n*s,a=1),{chunk:r/s,ratio:a,start:e.pixels[t]-r/2}}(t,e,a,r),h=this._getStackIndex(this.index,this._cachedMeta.stack,n?t:void 0);i=l.start+l.chunk*h+l.chunk/2,s=Math.min(o,l.chunk*l.ratio)}else i=r.getPixelForValue(this.getParsed(t)[r.axis],t),s=Math.min(o,e.min*e.ratio);return{base:i-s/2,head:i+s/2,center:i,size:s}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,r=0;for(;r<s;++r)null===this.getParsed(r)[e.axis]||i[r].hidden||i[r].draw(this._ctx)}}class ss extends i8{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let r=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<r.length;t++)r[t]._custom=this.resolveDataElementOptions(t+i).radius;return r}parseArrayData(t,e,i,s){let r=super.parseArrayData(t,e,i,s);for(let t=0;t<r.length;t++){let s=e[i+t];r[t]._custom=tT(s[2],this.resolveDataElementOptions(t+i).radius)}return r}parseObjectData(t,e,i,s){let r=super.parseObjectData(t,e,i,s);for(let t=0;t<r.length;t++){let s=e[i+t];r[t]._custom=tT(s&&s.r&&+s.r,this.resolveDataElementOptions(t+i).radius)}return r}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:r}=e,a=this.getParsed(t),n=s.getLabelForValue(a.x),o=r.getLabelForValue(a.y),l=a._custom;return{label:i[t]||"",value:"("+n+", "+o+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let r="reset"===s,{iScale:a,vScale:n}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(e,s),h=a.axis,d=n.axis;for(let c=e;c<e+i;c++){let e=t[c],i=!r&&this.getParsed(c),u={},f=u[h]=r?a.getPixelForDecimal(.5):a.getPixelForValue(i[h]),p=u[d]=r?n.getBasePixel():n.getPixelForValue(i[d]);u.skip=isNaN(f)||isNaN(p),l&&(u.options=o||this.resolveDataElementOptions(c,e.active?"active":s),r&&(u.options.radius=0)),this.updateElement(e,c,u,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let r=s.radius;return"active"!==e&&(s.radius=0),s.radius+=tT(i&&i._custom,r),s}}class sr extends i8{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,r)=>{let a=t.getDatasetMeta(0).controller.getStyle(r);return{text:e,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,fontColor:s,lineWidth:a.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let r,a,n=t=>+i[t];if(tP(i[t])){let{key:t="value"}=this._parsing;n=e=>+tY(i[e],t)}for(r=t,a=t+e;r<a;++r)s._parsed[r]=n(r)}}_getRotation(){return t7(this.options.rotation-90)}_getCircumference(){return t7(this.options.circumference)}_getRotationExtents(){let t=tG,e=-tG;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let s=this.chart.getDatasetMeta(i).controller,r=s._getRotation(),a=s._getCircumference();t=Math.min(t,r),e=Math.max(e,r+a)}return{rotation:t,circumference:e-t}}update(t){let{chartArea:e}=this.chart,i=this._cachedMeta,s=i.data,r=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,a=Math.max((Math.min(e.width,e.height)-r)/2,0),n=Math.min(tN(this.options.cutout,a),1),o=this._getRingWeight(this.index),{circumference:l,rotation:h}=this._getRotationExtents(),{ratioX:d,ratioY:c,offsetX:u,offsetY:f}=function(t,e,i){let s=1,r=1,a=0,n=0;if(e<tG){let o=t+e,l=Math.cos(t),h=Math.sin(t),d=Math.cos(o),c=Math.sin(o),u=(e,s,r)=>en(e,t,o,!0)?1:Math.max(s,s*i,r,r*i),f=(e,s,r)=>en(e,t,o,!0)?-1:Math.min(s,s*i,r,r*i),p=u(0,l,d),g=u(t0,h,c),m=f(tq,l,d),x=f(tq+t0,h,c);s=(p-m)/2,r=(g-x)/2,a=-(p+m)/2,n=-(g+x)/2}return{ratioX:s,ratioY:r,offsetX:a,offsetY:n}}(h,l,n),p=Math.max(Math.min((e.width-r)/d,(e.height-r)/c)/2,0),g=tA(this.options.radius,p),m=Math.max(g*n,0),x=(g-m)/this._getVisibleDatasetWeightTotal();this.offsetX=u*g,this.offsetY=f*g,i.total=this.calculateTotal(),this.outerRadius=g-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*o,0),this.updateElements(s,0,s.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,r=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*r/tG)}updateElements(t,e,i,s){let r,a="reset"===s,n=this.chart,o=n.chartArea,l=n.options.animation,h=(o.left+o.right)/2,d=(o.top+o.bottom)/2,c=a&&l.animateScale,u=c?0:this.innerRadius,f=c?0:this.outerRadius,{sharedOptions:p,includeOptions:g}=this._getSharedOptions(e,s),m=this._getRotation();for(r=0;r<e;++r)m+=this._circumference(r,a);for(r=e;r<e+i;++r){let e=this._circumference(r,a),i=t[r],n={x:h+this.offsetX,y:d+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:f,innerRadius:u};g&&(n.options=p||this.resolveDataElementOptions(r,i.active?"active":s)),m+=e,this.updateElement(i,r,n,s)}}calculateTotal(){let t,e=this._cachedMeta,i=e.data,s=0;for(t=0;t<i.length;t++){let r=e._parsed[t];null!==r&&!isNaN(r)&&this.chart.getDataVisibility(t)&&!i[t].hidden&&(s+=Math.abs(r))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?Math.abs(t)/e*tG:0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=eN(e._parsed[t],i.options.locale);return{label:s[t]||"",value:r}}getMaxBorderWidth(t){let e,i,s,r,a,n=0,o=this.chart;if(!t){for(e=0,i=o.data.datasets.length;e<i;++e)if(o.isDatasetVisible(e)){t=(s=o.getDatasetMeta(e)).data,r=s.controller;break}}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(a=r.resolveDataElementOptions(e)).borderAlign&&(n=Math.max(n,a.borderWidth||0,a.hoverBorderWidth||0));return n}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(tT(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class sa extends i8{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:r}=e,a=this.chart._animationsDisabled,{start:n,count:o}=ev(e,s,a);this._drawStart=n,this._drawCount=o,e_(e)&&(n=0,o=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=s;let l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!a,options:l},t),this.updateElements(s,n,o,t)}updateElements(t,e,i,s){let r="reset"===s,{iScale:a,vScale:n,_stacked:o,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,s),c=a.axis,u=n.axis,{spanGaps:f,segment:p}=this.options,g=t6(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||r||"none"===s,x=e+i,b=t.length,y=e>0&&this.getParsed(e-1);for(let i=0;i<b;++i){let f=t[i],b=m?f:{};if(i<e||i>=x){b.skip=!0;continue}let v=this.getParsed(i),_=tC(v[u]),w=b[c]=a.getPixelForValue(v[c],i),M=b[u]=r||_?n.getBasePixel():n.getPixelForValue(o?this.applyStack(n,v,o):v[u],i);b.skip=isNaN(w)||isNaN(M)||_,b.stop=i>0&&Math.abs(v[c]-y[c])>g,p&&(b.parsed=v,b.raw=l.data[i]),d&&(b.options=h||this.resolveDataElementOptions(i,f.active?"active":s)),m||this.updateElement(f,i,b,s),y=v}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];return s.length?Math.max(i,s[0].size(this.resolveDataElementOptions(0)),s[s.length-1].size(this.resolveDataElementOptions(s.length-1)))/2:i}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class sn extends i8{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,r)=>{let a=t.getDatasetMeta(0).controller.getStyle(r);return{text:e,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,fontColor:s,lineWidth:a.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=eN(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:r}}parseObjectData(t,e,i,s){return ic.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{let s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.max(Math.min(e.right-e.left,e.bottom-e.top)/2,0),r=Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0),a=(s-r)/t.getVisibleDatasetCount();this.outerRadius=s-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,e,i,s){let r,a="reset"===s,n=this.chart,o=n.options.animation,l=this._cachedMeta.rScale,h=l.xCenter,d=l.yCenter,c=l.getIndexAngle(0)-.5*tq,u=c,f=360/this.countVisibleElements();for(r=0;r<e;++r)u+=this._computeAngle(r,s,f);for(r=e;r<e+i;r++){let e=t[r],i=u,p=u+this._computeAngle(r,s,f),g=n.getDataVisibility(r)?l.getDistanceFromCenterForValue(this.getParsed(r).r):0;u=p,a&&(o.animateScale&&(g=0),o.animateRotate&&(i=p=c));let m={x:h,y:d,innerRadius:0,outerRadius:g,startAngle:i,endAngle:p,options:this.resolveDataElementOptions(r,e.active?"active":s)};this.updateElement(e,r,m,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?t7(this.resolveDataElementOptions(t,e).angle||i):0}}class so extends sr{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class sl extends i8{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return ic.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],r=e.iScale.getLabels();if(i.points=s,"resize"!==t){let e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);let a={_loop:!0,_fullLoop:r.length===s.length,options:e};this.updateElement(i,void 0,a,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let r=this._cachedMeta.rScale,a="reset"===s;for(let n=e;n<e+i;n++){let e=t[n],i=this.resolveDataElementOptions(n,e.active?"active":s),o=r.getPointPositionForValue(n,this.getParsed(n).r),l=a?r.xCenter:o.x,h=a?r.yCenter:o.y,d={x:l,y:h,angle:o.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,n,d,s)}}}class sh extends i8{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:r}=e,a=this.getParsed(t),n=s.getLabelForValue(a.x),o=r.getLabelForValue(a.y);return{label:i[t]||"",value:"("+n+", "+o+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:r,count:a}=ev(e,i,s);if(this._drawStart=r,this._drawCount=a,e_(e)&&(r=0,a=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:r,_dataset:a}=e;r._chart=this.chart,r._datasetIndex=this.index,r._decimated=!!a._decimated,r.points=i;let n=this.resolveDatasetElementOptions(t);n.segment=this.options.segment,this.updateElement(r,void 0,{animated:!s,options:n},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,r,a,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){let r="reset"===s,{iScale:a,vScale:n,_stacked:o,_dataset:l}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),d=this.getSharedOptions(h),c=this.includeOptions(s,d),u=a.axis,f=n.axis,{spanGaps:p,segment:g}=this.options,m=t6(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||r||"none"===s,b=e>0&&this.getParsed(e-1);for(let h=e;h<e+i;++h){let e=t[h],i=this.getParsed(h),p=x?e:{},y=tC(i[f]),v=p[u]=a.getPixelForValue(i[u],h),_=p[f]=r||y?n.getBasePixel():n.getPixelForValue(o?this.applyStack(n,i,o):i[f],h);p.skip=isNaN(v)||isNaN(_)||y,p.stop=h>0&&Math.abs(i[u]-b[u])>m,g&&(p.parsed=i,p.raw=l.data[h]),c&&(p.options=d||this.resolveDataElementOptions(h,e.active?"active":s)),x||this.updateElement(e,h,p,s),b=i}this.updateSharedOptions(d,s,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}let i=t.dataset,s=i.options&&i.options.borderWidth||0;return e.length?Math.max(s,e[0].size(this.resolveDataElementOptions(0)),e[e.length-1].size(this.resolveDataElementOptions(e.length-1)))/2:s}}function sd(){throw Error("This method is not implemented: Check that a complete date adapter is provided.")}class sc{static override(t){Object.assign(sc.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return sd()}parse(){return sd()}format(){return sd()}add(){return sd()}diff(){return sd()}startOf(){return sd()}endOf(){return sd()}}var su={_date:sc};function sf(t,e,i,s,r){let a=t.getSortedVisibleDatasetMetas(),n=i[e];for(let t=0,i=a.length;t<i;++t){let{index:i,data:o}=a[t],{lo:l,hi:h}=function(t,e,i,s){let{controller:r,data:a,_sorted:n}=t,o=r._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&"r"!==e&&n&&a.length){let n=o._reversePixels?ec:ed;if(s){if(r._sharedOptions){let t=a[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){let t=n(a,e,i-s),r=n(a,e,i+s);return{lo:t.lo,hi:r.hi}}}}else{let s=n(a,e,i);if(l){let{vScale:e}=r._cachedMeta,{_parsed:i}=t,a=i.slice(0,s.lo+1).reverse().findIndex(t=>!tC(t[e.axis]));s.lo-=Math.max(0,a);let n=i.slice(s.hi).findIndex(t=>!tC(t[e.axis]));s.hi+=Math.max(0,n)}return s}}return{lo:0,hi:a.length-1}}(a[t],e,n,r);for(let t=l;t<=h;++t){let e=o[t];e.skip||s(e,i,t)}}}function sp(t,e,i,s,r){let a=[];return(r||t.isPointInArea(e))&&sf(t,i,e,function(i,n,o){(r||eU(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&a.push({element:i,datasetIndex:n,index:o})},!0),a}function sg(t,e,i,s,r,a){let n;return a||t.isPointInArea(e)?"r"!==i||s?function(t,e,i,s,r,a){let n=[],o=function(t){let e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){return Math.sqrt(Math.pow(e?Math.abs(t.x-s.x):0,2)+Math.pow(i?Math.abs(t.y-s.y):0,2))}}(i),l=Number.POSITIVE_INFINITY;return sf(t,i,e,function(i,h,d){let c=i.inRange(e.x,e.y,r);if(s&&!c)return;let u=i.getCenterPoint(r);if(!(a||t.isPointInArea(u))&&!c)return;let f=o(e,u);f<l?(n=[{element:i,datasetIndex:h,index:d}],l=f):f===l&&n.push({element:i,datasetIndex:h,index:d})}),n}(t,e,i,s,r,a):(n=[],sf(t,i,e,function(t,i,s){let{startAngle:a,endAngle:o}=t.getProps(["startAngle","endAngle"],r),{angle:l}=ei(t,{x:e.x,y:e.y});en(l,a,o)&&n.push({element:t,datasetIndex:i,index:s})}),n):[]}function sm(t,e,i,s,r){let a=[],n="x"===i?"inXRange":"inYRange",o=!1;return(sf(t,i,e,(t,s,l)=>{t[n]&&t[n](e[i],r)&&(a.push({element:t,datasetIndex:s,index:l}),o=o||t.inRange(e.x,e.y,r))}),s&&!o)?[]:a}var sx={modes:{index(t,e,i,s){let r=ik(e,t),a=i.axis||"x",n=i.includeInvisible||!1,o=i.intersect?sp(t,r,a,s,n):sg(t,r,a,!1,s,n),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{let e=o[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,s){let r=ik(e,t),a=i.axis||"xy",n=i.includeInvisible||!1,o=i.intersect?sp(t,r,a,s,n):sg(t,r,a,!1,s,n);if(o.length>0){let e=o[0].datasetIndex,i=t.getDatasetMeta(e).data;o=[];for(let t=0;t<i.length;++t)o.push({element:i[t],datasetIndex:e,index:t})}return o},point(t,e,i,s){let r=ik(e,t);return sp(t,r,i.axis||"xy",s,i.includeInvisible||!1)},nearest(t,e,i,s){let r=ik(e,t),a=i.axis||"xy",n=i.includeInvisible||!1;return sg(t,r,a,i.intersect,s,n)},x(t,e,i,s){let r=ik(e,t);return sm(t,r,"x",i.intersect,s)},y(t,e,i,s){let r=ik(e,t);return sm(t,r,"y",i.intersect,s)}}};let sb=["left","top","right","bottom"];function sy(t,e){return t.filter(t=>t.pos===e)}function sv(t,e){return t.filter(t=>-1===sb.indexOf(t.pos)&&t.box.axis===e)}function s_(t,e){return t.sort((t,i)=>{let s=e?i:t,r=e?t:i;return s.weight===r.weight?s.index-r.index:s.weight-r.weight})}function sw(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function sM(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function sk(t,e,i,s){let r,a,n,o,l,h,d=[];for(r=0,a=t.length,l=0;r<a;++r){(o=(n=t[r]).box).update(n.width||e.w,n.height||e.h,function(t,e){let i=e.maxPadding;var s=t?["left","right"]:["top","bottom"];let r={left:0,top:0,right:0,bottom:0};return s.forEach(t=>{r[t]=Math.max(e[t],i[t])}),r}(n.horizontal,e));let{same:a,other:c}=function(t,e,i,s){let{pos:r,box:a}=i,n=t.maxPadding;if(!tP(r)){i.size&&(t[r]-=i.size);let e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?a.height:a.width),i.size=e.size/e.count,t[r]+=i.size}a.getPadding&&sM(n,a.getPadding());let o=Math.max(0,e.outerWidth-sw(n,t,"left","right")),l=Math.max(0,e.outerHeight-sw(n,t,"top","bottom")),h=o!==t.w,d=l!==t.h;return t.w=o,t.h=l,i.horizontal?{same:h,other:d}:{same:d,other:h}}(e,i,n,s);l|=a&&d.length,h=h||c,o.fullSize||d.push(n)}return l&&sk(d,e,i,s)||h}function sD(t,e,i,s,r){t.top=i,t.left=e,t.right=e+s,t.bottom=i+r,t.width=s,t.height=r}function sC(t,e,i,s){let r=i.padding,{x:a,y:n}=e;for(let o of t){let t=o.box,l=s[o.stack]||{count:1,placed:0,weight:1},h=o.stackWeight/l.weight||1;if(o.horizontal){let s=e.w*h,a=l.size||t.height;tU(l.start)&&(n=l.start),t.fullSize?sD(t,r.left,n,i.outerWidth-r.right-r.left,a):sD(t,e.left+l.placed,n,s,a),l.start=n,l.placed+=s,n=t.bottom}else{let s=e.h*h,n=l.size||t.width;tU(l.start)&&(a=l.start),t.fullSize?sD(t,a,r.top,n,i.outerHeight-r.bottom-r.top):sD(t,a,e.top+l.placed,n,s),l.start=a,l.placed+=s,a=t.right}}e.x=a,e.y=n}var sS={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){let i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;let r=e4(t.options.layout.padding),a=Math.max(e-r.width,0),n=Math.max(i-r.height,0),o=function(t){let e=function(t){let e,i,s,r,a,n,o=[];for(e=0,i=(t||[]).length;e<i;++e)s=t[e],({position:r,options:{stack:a,stackWeight:n=1}}=s),o.push({index:e,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:a&&r+a,stackWeight:n});return o}(t),i=s_(e.filter(t=>t.box.fullSize),!0),s=s_(sy(e,"left"),!0),r=s_(sy(e,"right")),a=s_(sy(e,"top"),!0),n=s_(sy(e,"bottom")),o=sv(e,"x"),l=sv(e,"y");return{fullSize:i,leftAndTop:s.concat(a),rightAndBottom:r.concat(l).concat(n).concat(o),chartArea:sy(e,"chartArea"),vertical:s.concat(r).concat(l),horizontal:a.concat(n).concat(o)}}(t.boxes),l=o.vertical,h=o.horizontal;tL(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});let d=Object.freeze({outerWidth:e,outerHeight:i,padding:r,availableWidth:a,availableHeight:n,vBoxMaxWidth:a/2/(l.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),hBoxMaxHeight:n/2}),c=Object.assign({},r);sM(c,e4(s));let u=Object.assign({maxPadding:c,w:a,h:n,x:r.left,y:r.top},r),f=function(t,e){let i,s,r,a=function(t){let e={};for(let i of t){let{stack:t,pos:s,stackWeight:r}=i;if(!t||!sb.includes(s))continue;let a=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=r}return e}(t),{vBoxMaxWidth:n,hBoxMaxHeight:o}=e;for(i=0,s=t.length;i<s;++i){let{fullSize:s}=(r=t[i]).box,l=a[r.stack],h=l&&r.stackWeight/l.weight;r.horizontal?(r.width=h?h*n:s&&e.availableWidth,r.height=o):(r.width=n,r.height=h?h*o:s&&e.availableHeight)}return a}(l.concat(h),d);sk(o.fullSize,u,d,f),sk(l,u,d,f),sk(h,u,d,f)&&sk(l,u,d,f);let p=u.maxPadding;function g(t){let e=Math.max(p[t]-u[t],0);return u[t]+=e,e}u.y+=g("top"),u.x+=g("left"),g("right"),g("bottom"),sC(o.leftAndTop,u,d,f),u.x+=u.w,u.y+=u.h,sC(o.rightAndBottom,u,d,f),t.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},tL(o.chartArea,e=>{let i=e.box;Object.assign(i,t.chartArea),i.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class sP{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class sO extends sP{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let sE="$chartjs",sT={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},sN=t=>null===t||""===t,sA=!!iS&&{passive:!0};function sj(t,e){for(let i of t)if(i===e||i.contains(e))return!0}function sL(t,e,i){let s=t.canvas,r=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||sj(i.addedNodes,s))&&!sj(i.removedNodes,s);e&&i()});return r.observe(document,{childList:!0,subtree:!0}),r}function sI(t,e,i){let s=t.canvas,r=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||sj(i.removedNodes,s))&&!sj(i.addedNodes,s);e&&i()});return r.observe(document,{childList:!0,subtree:!0}),r}let sR=new Map,sz=0;function sF(){let t=window.devicePixelRatio;t!==sz&&(sz=t,sR.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function sW(t,e,i){let s=t.canvas,r=s&&ib(s);if(!r)return;let a=em((t,e)=>{let s=r.clientWidth;i(t,e),s<r.clientWidth&&i()},window),n=new ResizeObserver(t=>{let e=t[0],i=e.contentRect.width,s=e.contentRect.height;(0!==i||0!==s)&&a(i,s)});return n.observe(r),sR.size||window.addEventListener("resize",sF),sR.set(t,a),n}function sB(t,e,i){i&&i.disconnect(),"resize"===e&&(sR.delete(t),sR.size||window.removeEventListener("resize",sF))}function sV(t,e,i){let s=t.canvas,r=em(e=>{null!==t.ctx&&i(function(t,e){let i=sT[t.type]||t.type,{x:s,y:r}=ik(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==r?r:null}}(e,t))},t);return s&&s.addEventListener(e,r,sA),r}class sH extends sP{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(!function(t,e){let i=t.style,s=t.getAttribute("height"),r=t.getAttribute("width");if(t[sE]={initial:{height:s,width:r,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",sN(r)){let e=iP(t,"width");void 0!==e&&(t.width=e)}if(sN(s))if(""===t.style.height)t.height=t.width/(e||2);else{let e=iP(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[sE])return!1;let i=e[sE].initial;["height","width"].forEach(t=>{let s=i[t];tC(s)?e.removeAttribute(t):e.setAttribute(t,s)});let s=i.style||{};return Object.keys(s).forEach(t=>{e.style[t]=s[t]}),e.width=e.width,delete e[sE],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),r={attach:sL,detach:sI,resize:sW}[e]||sV;s[e]=r(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];s&&((({attach:sB,detach:sB,resize:sB})[e]||function(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,sA)})(t,e,s),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return function(t,e,i,s){let r=iv(t),a=iw(r,"margin"),n=iy(r.maxWidth,t,"clientWidth")||tJ,o=iy(r.maxHeight,t,"clientHeight")||tJ,l=function(t,e,i){let s,r;if(void 0===e||void 0===i){let a=t&&ib(t);if(a){let t=a.getBoundingClientRect(),n=iv(a),o=iw(n,"border","width"),l=iw(n,"padding");e=t.width-l.width-o.width,i=t.height-l.height-o.height,s=iy(n.maxWidth,a,"clientWidth"),r=iy(n.maxHeight,a,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||tJ,maxHeight:r||tJ}}(t,e,i),{width:h,height:d}=l;if("content-box"===r.boxSizing){let t=iw(r,"border","width"),e=iw(r,"padding");h-=e.width+t.width,d-=e.height+t.height}return h=Math.max(0,h-a.width),d=Math.max(0,s?h/s:d-a.height),h=iD(Math.min(h,n,l.maxWidth)),d=iD(Math.min(d,o,l.maxHeight)),h&&!d&&(d=iD(h/2)),(void 0!==e||void 0!==i)&&s&&l.height&&d>l.height&&(h=iD(Math.floor((d=l.height)*s))),{width:h,height:d}}(t,e,i,s)}isAttached(t){let e=t&&ib(t);return!!(e&&e.isConnected)}}class sY{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return t6(this.x)&&t6(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),s}}function s$(t,e,i,s,r){let a,n,o,l=tT(s,0),h=Math.min(tT(r,t.length),t.length),d=0;for(i=Math.ceil(i),r&&(i=(a=r-s)/Math.floor(a/i)),o=l;o<0;)o=Math.round(l+ ++d*i);for(n=Math.max(l,0);n<h;n++)n===o&&(e.push(t[n]),o=Math.round(l+ ++d*i))}let sU=t=>"left"===t?"right":"right"===t?"left":t,sZ=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,sX=(t,e)=>Math.min(e||t,t);function sq(t,e){let i=[],s=t.length/e,r=t.length,a=0;for(;a<r;a+=s)i.push(t[Math.floor(a)]);return i}function sG(t){return t.drawTicks?t.tickLength:0}function sK(t,e){if(!t.display)return 0;let i=e8(t.font,e),s=e4(t.padding);return(tS(t.text)?t.text.length:1)*i.lineHeight+s.height}class sJ extends sY{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=tE(t,Number.POSITIVE_INFINITY),e=tE(e,Number.NEGATIVE_INFINITY),i=tE(i,Number.POSITIVE_INFINITY),s=tE(s,Number.NEGATIVE_INFINITY),{min:tE(t,i),max:tE(e,s),minDefined:tO(t),maxDefined:tO(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:r,maxDefined:a}=this.getUserBounds();if(r&&a)return{min:i,max:s};let n=this.getMatchingVisibleMetas();for(let o=0,l=n.length;o<l;++o)e=n[o].controller.getMinMax(this,t),r||(i=Math.min(i,e.min)),a||(s=Math.max(s,e.max));return i=a&&i>s?s:i,s=r&&i>s?i:s,{min:tE(i,tE(s,i)),max:tE(s,tE(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){tj(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:r,ticks:a}=this.options,n=a.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){let{min:s,max:r}=t,a=tA(e,(r-s)/2),n=(t,e)=>i&&0===t?0:t+e;return{min:n(s,-Math.abs(a)),max:n(r,a)}}(this,r,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let o=n<this.ticks.length;this._convertTicksToLabels(o?sq(this.ticks,n):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||"auto"===a.source)&&(this.ticks=function(t,e){let i=t.options.ticks,s=function(t){let e=t.options.offset,i=t._tickSize();return Math.floor(Math.min(t._length/i+ +!e,t._maxLength/i))}(t),r=Math.min(i.maxTicksLimit||s,s),a=i.major.enabled?function(t){let e,i,s=[];for(e=0,i=t.length;e<i;e++)t[e].major&&s.push(e);return s}(e):[],n=a.length,o=a[0],l=a[n-1],h=[];if(n>r)return function(t,e,i,s){let r,a=0,n=i[0];for(r=0,s=Math.ceil(s);r<t.length;r++)r===n&&(e.push(t[r]),n=i[++a*s])}(e,h,a,n/r),h;let d=function(t,e,i){let s=function(t){let e,i,s=t.length;if(s<2)return!1;for(i=t[0],e=1;e<s;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),r=e.length/i;if(!s)return Math.max(r,1);let a=function(t){let e,i=[],s=Math.sqrt(t);for(e=1;e<s;e++)t%e==0&&(i.push(e),i.push(t/e));return s===(0|s)&&i.push(s),i.sort((t,e)=>t-e).pop(),i}(s);for(let t=0,e=a.length-1;t<e;t++){let e=a[t];if(e>r)return e}return Math.max(r,1)}(a,e,r);if(n>0){let t,i,s=n>1?Math.round((l-o)/(n-1)):null;for(s$(e,h,d,tC(s)?0:o-s,o),t=0,i=n-1;t<i;t++)s$(e,h,d,a[t],a[t+1]);return s$(e,h,d,l,tC(s)?e.length:l+s),h}return s$(e,h,d),h}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),o&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){tj(this.options.afterUpdate,[this])}beforeSetDimensions(){tj(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){tj(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),tj(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){tj(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e,i,s,r=this.options.ticks;for(e=0,i=t.length;e<i;e++)(s=t[e]).label=tj(r.callback,[s.value,e,t],this)}afterTickToLabelConversion(){tj(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){tj(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t,e,i,s=this.options,r=s.ticks,a=sX(this.ticks.length,s.ticks.maxTicksLimit),n=r.minRotation||0,o=r.maxRotation,l=n;if(!this._isVisible()||!r.display||n>=o||a<=1||!this.isHorizontal()){this.labelRotation=n;return}let h=this._getLabelSizes(),d=h.widest.width,c=h.highest.height,u=eo(this.chart.width-d,0,this.maxWidth);d+6>(t=s.offset?this.maxWidth/a:u/(a-1))&&(t=u/(a-(s.offset?.5:1)),e=this.maxHeight-sG(s.grid)-r.padding-sK(s.title,this.chart.options.font),i=Math.sqrt(d*d+c*c),l=Math.max(n,Math.min(o,l=et(Math.min(Math.asin(eo((h.highest.height+6)/t,-1,1)),Math.asin(eo(e/i,-1,1))-Math.asin(eo(c/i,-1,1))))))),this.labelRotation=l}afterCalculateLabelRotation(){tj(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){tj(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:r}}=this,a=this._isVisible(),n=this.isHorizontal();if(a){let a=sK(s,e.options.font);if(n?(t.width=this.maxWidth,t.height=sG(r)+a):(t.height=this.maxHeight,t.width=sG(r)+a),i.display&&this.ticks.length){let{first:e,last:s,widest:r,highest:a}=this._getLabelSizes(),o=2*i.padding,l=t7(this.labelRotation),h=Math.cos(l),d=Math.sin(l);if(n){let e=i.mirror?0:d*r.width+h*a.height;t.height=Math.min(this.maxHeight,t.height+e+o)}else{let e=i.mirror?0:h*r.width+d*a.height;t.width=Math.min(this.maxWidth,t.width+e+o)}this._calculatePadding(e,s,d,h)}}this._handleMargins(),n?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:r,padding:a},position:n}=this.options,o=0!==this.labelRotation,l="top"!==n&&"x"===this.axis;if(this.isHorizontal()){let n=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1),d=0,c=0;o?l?(d=s*t.width,c=i*e.height):(d=i*t.height,c=s*e.width):"start"===r?c=e.width:"end"===r?d=t.width:"inner"!==r&&(d=t.width/2,c=e.width/2),this.paddingLeft=Math.max((d-n+a)*this.width/(this.width-n),0),this.paddingRight=Math.max((c-h+a)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===r?(i=0,s=t.height):"end"===r&&(i=e.height,s=0),this.paddingTop=i+a,this.paddingBottom=s+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){tj(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)tC(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=sq(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let s,r,a,n,o,l,h,d,c,u,f,{ctx:p,_longestTextCache:g}=this,m=[],x=[],b=Math.floor(e/sX(e,i)),y=0,v=0;for(s=0;s<e;s+=b){if(n=t[s].label,p.font=l=(o=this._resolveTickFontOptions(s)).string,h=g[l]=g[l]||{data:{},gc:[]},d=o.lineHeight,c=u=0,tC(n)||tS(n)){if(tS(n))for(r=0,a=n.length;r<a;++r)tC(f=n[r])||tS(f)||(c=eB(p,h.data,h.gc,c,f),u+=d)}else c=eB(p,h.data,h.gc,c,n),u=d;m.push(c),x.push(u),y=Math.max(c,y),v=Math.max(u,v)}tL(g,t=>{let i,s=t.gc,r=s.length/2;if(r>e){for(i=0;i<r;++i)delete t.data[s[i]];s.splice(0,r)}});let _=m.indexOf(y),w=x.indexOf(v),M=t=>({width:m[t]||0,height:x[t]||0});return{first:M(0),last:M(e-1),widest:M(_),highest:M(w),widths:m,heights:x}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return eo(this._alignToPixels?eV(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){var e,i;let s=this.ticks||[];if(t>=0&&t<s.length){let i=s[t];return i.$context||(e=this.getContext(),i.$context=e9(e,{tick:i,index:t,type:"tick"}))}return this.$context||(this.$context=(i=this.chart.getContext(),e9(i,{scale:this,type:"scale"})))}_tickSize(){let t=this.options.ticks,e=t7(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),r=this._getLabelSizes(),a=t.autoSkipPadding||0,n=r?r.widest.width+a:0,o=r?r.highest.height+a:0;return this.isHorizontal()?o*i>n*s?n/i:o/s:o*s<n*i?o/i:n/s}_isVisible(){let t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e,i,s,r,a,n,o,l,h,d,c,u,f=this.axis,p=this.chart,g=this.options,{grid:m,position:x,border:b}=g,y=m.offset,v=this.isHorizontal(),_=this.ticks.length+ +!!y,w=sG(m),M=[],k=b.setContext(this.getContext()),D=k.display?k.width:0,C=D/2,S=function(t){return eV(p,t,D)};if("top"===x)e=S(this.bottom),n=this.bottom-w,l=e-C,d=S(t.top)+C,u=t.bottom;else if("bottom"===x)e=S(this.top),d=t.top,u=S(t.bottom)-C,n=e+C,l=this.top+w;else if("left"===x)e=S(this.right),a=this.right-w,o=e-C,h=S(t.left)+C,c=t.right;else if("right"===x)e=S(this.left),h=t.left,c=S(t.right)-C,a=e+C,o=this.left+w;else if("x"===f){if("center"===x)e=S((t.top+t.bottom)/2+.5);else if(tP(x)){let t=Object.keys(x)[0],i=x[t];e=S(this.chart.scales[t].getPixelForValue(i))}d=t.top,u=t.bottom,l=(n=e+C)+w}else if("y"===f){if("center"===x)e=S((t.left+t.right)/2);else if(tP(x)){let t=Object.keys(x)[0],i=x[t];e=S(this.chart.scales[t].getPixelForValue(i))}o=(a=e-C)-w,h=t.left,c=t.right}let P=tT(g.ticks.maxTicksLimit,_),O=Math.max(1,Math.ceil(_/P));for(i=0;i<_;i+=O){let t=this.getContext(i),e=m.setContext(t),f=b.setContext(t),g=e.lineWidth,x=e.color,_=f.dash||[],w=f.dashOffset,k=e.tickWidth,D=e.tickColor,C=e.tickBorderDash||[],S=e.tickBorderDashOffset;void 0!==(s=function(t,e,i){let s,r=t.ticks.length,a=Math.min(e,r-1),n=t._startPixel,o=t._endPixel,l=t.getPixelForTick(a);if(!i||(s=1===r?Math.max(l-n,o-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(a-1))/2,!((l+=a<e?s:-s)<n-1e-6)&&!(l>o+1e-6)))return l}(this,i,y))&&(r=eV(p,s,g),v?a=o=h=c=r:n=l=d=u=r,M.push({tx1:a,ty1:n,tx2:o,ty2:l,x1:h,y1:d,x2:c,y2:u,width:g,color:x,borderDash:_,borderDashOffset:w,tickWidth:k,tickColor:D,tickBorderDash:C,tickBorderDashOffset:S}))}return this._ticksLength=_,this._borderValue=e,M}_computeLabelItems(t){let e,i,s,r,a,n,o,l,h,d,c,u=this.axis,f=this.options,{position:p,ticks:g}=f,m=this.isHorizontal(),x=this.ticks,{align:b,crossAlign:y,padding:v,mirror:_}=g,w=sG(f.grid),M=w+v,k=_?-v:M,D=-t7(this.labelRotation),C=[],S="middle";if("top"===p)a=this.bottom-k,n=this._getXAxisLabelAlignment();else if("bottom"===p)a=this.top+k,n=this._getXAxisLabelAlignment();else if("left"===p){let t=this._getYAxisLabelAlignment(w);n=t.textAlign,r=t.x}else if("right"===p){let t=this._getYAxisLabelAlignment(w);n=t.textAlign,r=t.x}else if("x"===u){if("center"===p)a=(t.top+t.bottom)/2+M;else if(tP(p)){let t=Object.keys(p)[0],e=p[t];a=this.chart.scales[t].getPixelForValue(e)+M}n=this._getXAxisLabelAlignment()}else if("y"===u){if("center"===p)r=(t.left+t.right)/2-M;else if(tP(p)){let t=Object.keys(p)[0],e=p[t];r=this.chart.scales[t].getPixelForValue(e)}n=this._getYAxisLabelAlignment(w).textAlign}"y"===u&&("start"===b?S="top":"end"===b&&(S="bottom"));let P=this._getLabelSizes();for(e=0,i=x.length;e<i;++e){let t;s=x[e].label;let u=g.setContext(this.getContext(e));o=this.getPixelForTick(e)+g.labelOffset,h=(l=this._resolveTickFontOptions(e)).lineHeight;let f=(d=tS(s)?s.length:1)/2,b=u.color,v=u.textStrokeColor,w=u.textStrokeWidth,M=n;if(m?(r=o,"inner"===n&&(M=e===i-1?this.options.reverse?"left":"right":0===e?this.options.reverse?"right":"left":"center"),c="top"===p?"near"===y||0!==D?-d*h+h/2:"center"===y?-P.highest.height/2-f*h+h:-P.highest.height+h/2:"near"===y||0!==D?h/2:"center"===y?P.highest.height/2-f*h:P.highest.height-d*h,_&&(c*=-1),0===D||u.showLabelBackdrop||(r+=h/2*Math.sin(D))):(a=o,c=(1-d)*h/2),u.showLabelBackdrop){let s=e4(u.backdropPadding),r=P.heights[e],a=P.widths[e],o=c-s.top,l=0-s.left;switch(S){case"middle":o-=r/2;break;case"bottom":o-=r}switch(n){case"center":l-=a/2;break;case"right":l-=a;break;case"inner":e===i-1?l-=a:e>0&&(l-=a/2)}t={left:l,top:o,width:a+s.width,height:r+s.height,color:u.backdropColor}}C.push({label:s,font:l,textOffset:c,options:{rotation:D,color:b,strokeColor:v,strokeWidth:w,textAlign:M,textBaseline:S,translation:[r,a],backdrop:t}})}return C}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-t7(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){let e,i,{position:s,ticks:{crossAlign:r,mirror:a,padding:n}}=this.options,o=this._getLabelSizes(),l=t+n,h=o.widest.width;return"left"===s?a?(i=this.right+n,"near"===r?e="left":"center"===r?(e="center",i+=h/2):(e="right",i+=h)):(i=this.right-l,"near"===r?e="right":"center"===r?(e="center",i-=h/2):(e="left",i=this.left)):"right"===s?a?(i=this.left+n,"near"===r?e="right":"center"===r?(e="center",i-=h/2):(e="left",i-=h)):(i=this.left+l,"near"===r?e="left":"center"===r?(e="center",i+=h/2):(e="right",i=this.right)):e="right",{textAlign:e,x:i}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:r,height:a}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,r,a),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){let e,i,s=this.options.grid,r=this.ctx,a=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),n=(t,e,i)=>{i.width&&i.color&&(r.save(),r.lineWidth=i.width,r.strokeStyle=i.color,r.setLineDash(i.borderDash||[]),r.lineDashOffset=i.borderDashOffset,r.beginPath(),r.moveTo(t.x,t.y),r.lineTo(e.x,e.y),r.stroke(),r.restore())};if(s.display)for(e=0,i=a.length;e<i;++e){let t=a[e];s.drawOnChartArea&&n({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),s.drawTicks&&n({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){let t,e,i,s,{chart:r,ctx:a,options:{border:n,grid:o}}=this,l=n.setContext(this.getContext()),h=n.display?l.width:0;if(!h)return;let d=o.setContext(this.getContext(0)).lineWidth,c=this._borderValue;this.isHorizontal()?(t=eV(r,this.left,h)-h/2,e=eV(r,this.right,d)+d/2,i=s=c):(i=eV(r,this.top,h)-h/2,s=eV(r,this.bottom,d)+d/2,t=e=c),a.save(),a.lineWidth=l.width,a.strokeStyle=l.color,a.beginPath(),a.moveTo(t,i),a.lineTo(e,s),a.stroke(),a.restore()}drawLabels(t){if(!this.options.ticks.display)return;let e=this.ctx,i=this._computeLabelArea();for(let s of(i&&eZ(e,i),this.getLabelItems(t))){let t=s.options,i=s.font;eK(e,s.label,0,s.textOffset,i,t)}i&&eX(e)}drawTitle(){let t,{ctx:e,options:{position:i,title:s,reverse:r}}=this;if(!s.display)return;let a=e8(s.font),n=e4(s.padding),o=s.align,l=a.lineHeight/2;"bottom"===i||"center"===i||tP(i)?(l+=n.bottom,tS(s.text)&&(l+=a.lineHeight*(s.text.length-1))):l+=n.top;let{titleX:h,titleY:d,maxWidth:c,rotation:u}=function(t,e,i,s){let r,a,n,{top:o,left:l,bottom:h,right:d,chart:c}=t,{chartArea:u,scales:f}=c,p=0,g=h-o,m=d-l;if(t.isHorizontal()){if(a=eb(s,l,d),tP(i)){let t=Object.keys(i)[0],s=i[t];n=f[t].getPixelForValue(s)+g-e}else n="center"===i?(u.bottom+u.top)/2+g-e:sZ(t,i,e);r=d-l}else{if(tP(i)){let t=Object.keys(i)[0],s=i[t];a=f[t].getPixelForValue(s)-m+e}else a="center"===i?(u.left+u.right)/2-m+e:sZ(t,i,e);n=eb(s,h,o),p="left"===i?-t0:t0}return{titleX:a,titleY:n,maxWidth:r,rotation:p}}(this,l,i,o);eK(e,s.text,0,0,a,{color:s.color,maxWidth:c,rotation:u,textAlign:(t=ex(o),(r&&"right"!==i||!r&&"right"===i)&&(t=sU(t)),t),textBaseline:"middle",translation:[h,d]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=tT(t.grid&&t.grid.z,-1),s=tT(t.border&&t.border.z,0);return this._isVisible()&&this.draw===sJ.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){let e,i,s=this.chart.getSortedVisibleDatasetMetas(),r=this.axis+"AxisID",a=[];for(e=0,i=s.length;e<i;++e){let i=s[e];i[r]!==this.id||t&&i.type!==t||a.push(i)}return a}_resolveTickFontOptions(t){return e8(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class sQ{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){var e;let i,s=Object.getPrototypeOf(t);"id"in(e=s)&&"defaults"in e&&(i=this.register(s));let r=this.items,a=t.id,n=this.scope+"."+a;if(!a)throw Error("class does not have id: "+t);return a in r||(r[a]=t,function(t,e,i){var s,r;let a=tW(Object.create(null),[i?eW.get(i):{},eW.get(e),t.defaults]);eW.set(e,a),t.defaultRoutes&&(s=e,Object.keys(r=t.defaultRoutes).forEach(t=>{let e=t.split("."),i=e.pop(),a=[s].concat(e).join("."),n=r[t].split("."),o=n.pop(),l=n.join(".");eW.route(a,i,l,o)})),t.descriptors&&eW.describe(e,t.descriptors)}(t,n,i),this.override&&eW.override(t.id,t.overrides)),n}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in eW[s]&&(delete eW[s][i],this.override&&delete eL[i])}}class s0{constructor(){this.controllers=new sQ(i8,"datasets",!0),this.elements=new sQ(sY,"elements"),this.plugins=new sQ(Object,"plugins"),this.scales=new sQ(sJ,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{let s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):tL(e,e=>{let s=i||this._getRegistryForType(e);this._exec(t,s,e)})})}_exec(t,e,i){let s=t$(t);tj(i["before"+s],[],i),e[t](i),tj(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(void 0===s)throw Error('"'+t+'" is not a registered '+i+".");return s}}var s1=new s0;class s2{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let r=s?this._descriptors(t).filter(s):this._descriptors(t),a=this._notify(r,t,e,i);return"afterDestroy"===e&&(this._notify(r,t,"stop"),this._notify(this._init,t,"uninstall")),a}_notify(t,e,i,s){for(let r of(s=s||{},t)){let t=r.plugin;if(!1===tj(t[i],[e,s,r.options],t)&&s.cancelable)return!1}return!0}invalidate(){tC(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=tT(i.options&&i.options.plugins,{}),r=function(t){let e={},i=[],s=Object.keys(s1.plugins.items);for(let t=0;t<s.length;t++)i.push(s1.getPlugin(s[t]));let r=t.plugins||[];for(let t=0;t<r.length;t++){let s=r[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,{plugins:e,localIds:i},s,r){let a=[],n=t.getContext();for(let l of e){var o;let e=l.id,h=(o=s[e],r||!1!==o?!0===o?{}:o:null);null!==h&&a.push({plugin:l,options:function(t,{plugin:e,local:i},s,r){let a=t.pluginScopeKeys(e),n=t.getOptionScopes(s,a);return i&&e.defaults&&n.push(e.defaults),t.createResolver(n,r,[""],{scriptable:!1,indexable:!1,allKeys:!0})}(t.config,{plugin:l,local:i[e]},h,n)})}return a}(t,r,s,e):[]}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function s5(t,e){let i=eW.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function s3(t){if("x"===t||"y"===t||"r"===t)return t}function s4(t,...e){if(s3(t))return t;for(let s of e){var i;let e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&s3(t[0].toLowerCase());if(e)return e}throw Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function s8(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function s6(t){let e=t.options||(t.options={});e.plugins=tT(e.plugins,{}),e.scales=function(t,e){let i=eL[t.type]||{scales:{}},s=e.scales||{},r=s5(t.type,e),a=Object.create(null);return Object.keys(s).forEach(e=>{let n=s[e];if(!tP(n))return console.error(`Invalid scale configuration for scale: ${e}`);if(n._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);let o=s4(e,n,function(t,e){if(e.data&&e.data.datasets){let i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return s8(t,"x",i[0])||s8(t,"y",i[0])}return{}}(e,t),eW.scales[n.type]),l=o===r?"_index_":"_value_",h=i.scales||{};a[e]=tB(Object.create(null),[{axis:o},n,h[o],h[l]])}),t.data.datasets.forEach(i=>{let r=i.type||t.type,n=i.indexAxis||s5(r,e),o=(eL[r]||{}).scales||{};Object.keys(o).forEach(t=>{let e,r=(e=t,"_index_"===t?e=n:"_value_"===t&&(e="x"===n?"y":"x"),e),l=i[r+"AxisID"]||r;a[l]=a[l]||Object.create(null),tB(a[l],[{axis:r},s[l],o[t]])})}),Object.keys(a).forEach(t=>{let e=a[t];tB(e,[eW.scales[e.type],eW.scale])}),a}(t,e)}function s9(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let s7=new Map,rt=new Set;function re(t,e){let i=s7.get(t);return i||(i=e(),s7.set(t,i),rt.add(i)),i}let ri=(t,e,i)=>{let s=tY(e,i);void 0!==s&&t.add(s)};class rs{constructor(t){this._config=function(t){return(t=t||{}).data=s9(t.data),s6(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=s9(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),s6(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return re(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return re(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return re(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return re(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:r}=this,a=this._cachedScopes(t,i),n=a.get(e);if(n)return n;let o=new Set;e.forEach(e=>{t&&(o.add(t),e.forEach(e=>ri(o,t,e))),e.forEach(t=>ri(o,s,t)),e.forEach(t=>ri(o,eL[r]||{},t)),e.forEach(t=>ri(o,eW,t)),e.forEach(t=>ri(o,eI,t))});let l=Array.from(o);return 0===l.length&&l.push(Object.create(null)),rt.has(e)&&a.set(e,l),l}chartOptionScopes(){let{options:t,type:e}=this;return[t,eL[e]||{},eW.datasets[e]||{},{type:e},eW,eI]}resolveNamedOptions(t,e,i,s=[""]){let r={$shared:!0},{resolver:a,subPrefixes:n}=rr(this._resolverCache,t,s),o=a;if(function(t,e){let{isScriptable:i,isIndexable:s}=ie(t);for(let r of e){let e=i(r),a=s(r),n=(a||e)&&t[r];if(e&&(tZ(n)||ra(n))||a&&tS(n))return!0}return!1}(a,e)){r.$shared=!1,i=tZ(i)?i():i;let e=this.createResolver(t,i,n);o=it(a,i,e)}for(let t of e)r[t]=o[t];return r}createResolver(t,e,i=[""],s){let{resolver:r}=rr(this._resolverCache,t,i);return tP(e)?it(r,e,void 0,s):r}}function rr(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));let r=i.join(),a=s.get(r);return a||(a={resolver:e7(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},s.set(r,a)),a}let ra=t=>tP(t)&&Object.getOwnPropertyNames(t).some(e=>tZ(t[e])),rn=["top","bottom","left","right","chartArea"];function ro(t,e){return"top"===t||"bottom"===t||-1===rn.indexOf(t)&&"x"===e}function rl(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function rh(t){let e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),tj(i&&i.onComplete,[t],e)}function rd(t){let e=t.chart,i=e.options.animation;tj(i&&i.onProgress,[t],e)}function rc(t){return ix()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}let ru={},rf=t=>{let e=rc(t);return Object.values(ru).filter(t=>t.canvas===e).pop()};class rp{static defaults=eW;static instances=ru;static overrides=eL;static registry=s1;static version="4.4.9";static getChart=rf;static register(...t){s1.add(...t),rg()}static unregister(...t){s1.remove(...t),rg()}constructor(t,e){let i=this.config=new rs(e),s=rc(t),r=rf(s);if(r)throw Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");let a=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||(!ix()||"undefined"!=typeof OffscreenCanvas&&s instanceof OffscreenCanvas?sO:sH)),this.platform.updateConfig(i);let n=this.platform.acquireContext(s,a.aspectRatio),o=n&&n.canvas,l=o&&o.height,h=o&&o.width;if(this.id=tD(),this.ctx=n,this.canvas=o,this.width=h,this.height=l,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new s2,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...s){return e?(clearTimeout(i),i=setTimeout(t,e,s)):t.apply(this,s),e}}(t=>this.update(t),a.resizeDelay||0),this._dataChanges=[],ru[this.id]=this,!n||!o)return void console.error("Failed to create chart: can't acquire context from the given item");iY.listen(this,"complete",rh),iY.listen(this,"progress",rd),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:r}=this;return tC(t)?e&&r?r:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return s1}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():iC(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return eH(this.canvas,this.ctx),this}stop(){return iY.stop(this),this}resize(t,e){iY.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(s,t,e,r),n=i.devicePixelRatio||this.platform.getDevicePixelRatio(),o=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,iC(this,n,!0)&&(this.notifyPlugins("resize",{size:a}),tj(i.onResize,[this,a],this),this.attached&&this._doResize(o)&&this.render())}ensureScalesHaveIDs(){tL(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{}),r=[];e&&(r=r.concat(Object.keys(e).map(t=>{let i=e[t],s=s4(t,i),r="r"===s,a="x"===s;return{options:i,dposition:r?"chartArea":a?"bottom":"left",dtype:r?"radialLinear":a?"category":"linear"}}))),tL(r,e=>{let r=e.options,a=r.id,n=s4(a,r),o=tT(r.type,e.dtype);(void 0===r.position||ro(r.position,n)!==ro(e.dposition))&&(r.position=e.dposition),s[a]=!0;let l=null;a in i&&i[a].type===o?l=i[a]:i[(l=new(s1.getScale(o))({id:a,type:o,ctx:this.ctx,chart:this})).id]=l,l.init(r,t)}),tL(s,(t,e)=>{t||delete i[e]}),tL(i,t=>{sS.configure(this,t,t.options),sS.addBox(this,t)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(rl("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){let t,e,i=[],s=this.data.datasets;for(this._removeUnreferencedMetasets(),t=0,e=s.length;t<e;t++){let e=s[t],r=this.getDatasetMeta(t),a=e.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(t),r=this.getDatasetMeta(t)),r.type=a,r.indexAxis=e.indexAxis||s5(a,this.options),r.order=e.order||0,r.index=t,r.label=""+e.label,r.visible=this.isDatasetVisible(t),r.controller)r.controller.updateIndex(t),r.controller.linkScales();else{let e=s1.getController(a),{datasetElementType:s,dataElementType:n}=eW.datasets[a];Object.assign(e,{dataElementType:s1.getElement(n),datasetElementType:s&&s1.getElement(s)}),r.controller=new e(this,t),i.push(r.controller)}}return this._updateMetasets(),i}_resetElements(){tL(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;let r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let t=0,e=this.data.datasets.length;t<e;t++){let{controller:e}=this.getDatasetMeta(t),i=!s&&-1===r.indexOf(e);e.buildOrUpdateElements(i),a=Math.max(+e.getMaxOverflow(),a)}a=this._minPadding=i.layout.autoPadding?a:0,this._updateLayout(a),s||tL(r,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(rl("z","_idx"));let{_active:n,_lastEvent:o}=this;o?this._eventHandler(o,!0):n.length&&this._updateHoverStyles(n,n,!0),this.render()}_updateScales(){tL(this.scales,t=>{sS.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options;tX(new Set(Object.keys(this._listeners)),new Set(t.events))&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this;for(let{method:i,start:s,count:r}of this._getUniformDataChanges()||[]){var e="_removeElements"===i?-r:r;for(let i of Object.keys(t)){let r=+i;if(r>=s){let a=t[i];delete t[i],(e>0||r>s)&&(t[r+e]=a)}}}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),s=i(0);for(let t=1;t<e;t++)if(!tX(s,i(t)))return;return Array.from(s).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;sS.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],tL(this.boxes,t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,tZ(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(iY.has(this)?this.attached&&!iY.running(this)&&iY.start(this):(this.draw(),rh({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e,i,s=this._sortedMetasets,r=[];for(e=0,i=s.length;e<i;++e){let i=s[e];(!t||i.visible)&&r.push(i)}return r}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=iV(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(s&&eZ(e,s),t.controller.draw(),s&&eX(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return eU(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let r=sx.modes[e];return"function"==typeof r?r(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(t=>t&&t._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=e9(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",r=this.getDatasetMeta(t),a=r.controller._resolveAnimations(void 0,s);tU(e)?(r.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),a.update(r,{visible:i}),this.update(e=>e.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),iY.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),eH(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ru[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};tL(this.options.events,t=>i(t,s))}bindResponsiveEvents(){let t;this._responsiveListeners||(this._responsiveListeners={});let e=this._responsiveListeners,i=this.platform,s=(t,s)=>{i.addEventListener(this,t,s),e[t]=s},r=(t,s)=>{e[t]&&(i.removeEventListener(this,t,s),delete e[t])},a=(t,e)=>{this.canvas&&this.resize(t,e)},n=()=>{r("attach",n),this.attached=!0,this.resize(),s("resize",a),s("detach",t)};t=()=>{this.attached=!1,r("resize",a),this._stop(),this._resize(0,0),s("attach",n)},i.isAttached(this.canvas)?n():t()}unbindEvents(){tL(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},tL(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s,r,a,n=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+n+"DatasetHoverStyle"](),r=0,a=t.length;r<a;++r){let e=(s=t[r])&&this.getDatasetMeta(s.datasetIndex).controller;e&&e[n+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{let i=this.getDatasetMeta(t);if(!i)throw Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});tI(i,e)||(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){let s=this.options.hover,r=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),a=r(e,t),n=i?t:r(t,e);a.length&&this.updateHoverStyle(a,s.mode,!1),n.length&&s.mode&&this.updateHoverStyle(n,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;let r=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(r||i.changed)&&this.render(),this}_handleEvent(t,e,i){var s;let{_active:r=[],options:a}=this,n=this._getActiveElements(t,r,i,e),o="mouseup"===t.type||"click"===t.type||"contextmenu"===t.type,l=(s=this._lastEvent,i&&"mouseout"!==t.type?o?s:t:null);i&&(this._lastEvent=null,tj(a.onHover,[t,n,this],this),o&&tj(a.onClick,[t,n,this],this));let h=!tI(n,r);return(h||e)&&(this._active=n,this._updateHoverStyles(n,r,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;let r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,s)}}function rg(){return tL(rp.instances,t=>t._plugins.invalidate())}function rm(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function rx(t,e,i,s,r,a){let{x:n,y:o,startAngle:l,pixelMargin:h,innerRadius:d}=e,c=Math.max(e.outerRadius+s+i-h,0),u=d>0?d+s+i+h:0,f=0,p=r-l;if(s){let t=c>0?c-s:0,e=((d>0?d-s:0)+t)/2;f=(p-(0!==e?p*e/(e+s):p))/2}let g=Math.max(.001,p*c-i/tq)/c,m=(p-g)/2,x=l+m+f,b=r-m-f,{outerStart:y,outerEnd:v,innerStart:_,innerEnd:w}=function(t,e,i,s){let r=e2(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]),a=(i-e)/2,n=Math.min(a,s*e/2),o=t=>{let e=(i-Math.min(a,t))*s/2;return eo(t,0,Math.min(a,e))};return{outerStart:o(r.outerStart),outerEnd:o(r.outerEnd),innerStart:eo(r.innerStart,0,n),innerEnd:eo(r.innerEnd,0,n)}}(e,u,c,b-x),M=c-y,k=c-v,D=x+y/M,C=b-v/k,S=u+_,P=u+w,O=x+_/S,E=b-w/P;if(t.beginPath(),a){let e=(D+C)/2;if(t.arc(n,o,c,D,e),t.arc(n,o,c,e,C),v>0){let e=rm(k,C,n,o);t.arc(e.x,e.y,v,C,b+t0)}let i=rm(P,b,n,o);if(t.lineTo(i.x,i.y),w>0){let e=rm(P,E,n,o);t.arc(e.x,e.y,w,b+t0,E+Math.PI)}let s=(b-w/u+(x+_/u))/2;if(t.arc(n,o,u,b-w/u,s,!0),t.arc(n,o,u,s,x+_/u,!0),_>0){let e=rm(S,O,n,o);t.arc(e.x,e.y,_,O+Math.PI,x-t0)}let r=rm(M,x,n,o);if(t.lineTo(r.x,r.y),y>0){let e=rm(M,D,n,o);t.arc(e.x,e.y,y,x-t0,D)}}else{t.moveTo(n,o);let e=Math.cos(D)*c+n,i=Math.sin(D)*c+o;t.lineTo(e,i);let s=Math.cos(C)*c+n,r=Math.sin(C)*c+o;t.lineTo(s,r)}t.closePath()}class rb extends sY{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let{angle:s,distance:r}=ei(this.getProps(["x","y"],i),{x:t,y:e}),{startAngle:a,endAngle:n,innerRadius:o,outerRadius:l,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,c=tT(h,n-a),u=en(s,a,n)&&a!==n,f=c>=tG||u,p=el(r,o+d,l+d);return f&&p}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:r,innerRadius:a,outerRadius:n}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:o,spacing:l}=this.options,h=(s+r)/2,d=(a+n+l+o)/2;return{x:e+Math.cos(h)*d,y:i+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/4,r=(e.spacing||0)/2,a=e.circular;if(this.pixelMargin=.33*("inner"===e.borderAlign),this.fullCircles=i>tG?Math.floor(i/tG):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let n=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(n)*s,Math.sin(n)*s);let o=s*(1-Math.sin(Math.min(tq,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,s,r){let{fullCircles:a,startAngle:n,circumference:o}=e,l=e.endAngle;if(a){rx(t,e,i,s,l,r);for(let e=0;e<a;++e)t.fill();isNaN(o)||(l=n+(o%tG||tG))}rx(t,e,i,s,l,r),t.fill()}(t,this,o,r,a),function(t,e,i,s,r){let{fullCircles:a,startAngle:n,circumference:o,options:l}=e,{borderWidth:h,borderJoinStyle:d,borderDash:c,borderDashOffset:u}=l,f="inner"===l.borderAlign;if(!h)return;t.setLineDash(c||[]),t.lineDashOffset=u,f?(t.lineWidth=2*h,t.lineJoin=d||"round"):(t.lineWidth=h,t.lineJoin=d||"bevel");let p=e.endAngle;if(a){rx(t,e,i,s,p,r);for(let e=0;e<a;++e)t.stroke();isNaN(o)||(p=n+(o%tG||tG))}f&&function(t,e,i){let{startAngle:s,pixelMargin:r,x:a,y:n,outerRadius:o,innerRadius:l}=e,h=r/o;t.beginPath(),t.arc(a,n,o,s-h,i+h),l>r?(h=r/l,t.arc(a,n,l,i+h,s-h,!0)):t.arc(a,n,r,i+t0,s-t0),t.closePath(),t.clip()}(t,e,p),a||(rx(t,e,i,s,p,r),t.stroke())}(t,this,o,r,a),t.restore()}}function ry(t,e,i=e){t.lineCap=tT(i.borderCapStyle,e.borderCapStyle),t.setLineDash(tT(i.borderDash,e.borderDash)),t.lineDashOffset=tT(i.borderDashOffset,e.borderDashOffset),t.lineJoin=tT(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=tT(i.borderWidth,e.borderWidth),t.strokeStyle=tT(i.borderColor,e.borderColor)}function rv(t,e,i){t.lineTo(i.x,i.y)}function r_(t,e,i={}){let s=t.length,{start:r=0,end:a=s-1}=i,{start:n,end:o}=e,l=Math.max(r,n),h=Math.min(a,o);return{count:s,start:l,loop:e.loop,ilen:h<l&&!(r<n&&a<n||r>o&&a>o)?s+h-l:h-l}}function rw(t,e,i,s){let r,a,n,{points:o,options:l}=e,{count:h,start:d,loop:c,ilen:u}=r_(o,i,s),f=l.stepped?eq:l.tension||"monotone"===l.cubicInterpolationMode?eG:rv,{move:p=!0,reverse:g}=s||{};for(r=0;r<=u;++r)(a=o[(d+(g?u-r:r))%h]).skip||(p?(t.moveTo(a.x,a.y),p=!1):f(t,n,a,g,l.stepped),n=a);return c&&f(t,n,a=o[(d+(g?u:0))%h],g,l.stepped),!!c}function rM(t,e,i,s){let r,a,n,o,l,h,d=e.points,{count:c,start:u,ilen:f}=r_(d,i,s),{move:p=!0,reverse:g}=s||{},m=0,x=0,b=t=>(u+(g?f-t:t))%c,y=()=>{o!==l&&(t.lineTo(m,l),t.lineTo(m,o),t.lineTo(m,h))};for(p&&(a=d[b(0)],t.moveTo(a.x,a.y)),r=0;r<=f;++r){if((a=d[b(r)]).skip)continue;let e=a.x,i=a.y,s=0|e;s===n?(i<o?o=i:i>l&&(l=i),m=(x*m+e)/++x):(y(),t.lineTo(e,i),n=s,x=0,o=l=i),h=i}y()}function rk(t){let e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?rw:rM}let rD="function"==typeof Path2D;class rC extends sY{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;!function(t,e,i,s,r){let a,n,o,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,s,r,a=ig(e),n=t.length,o=Array(n).fill(0),l=Array(n),h=ip(t,0);for(i=0;i<n;++i)if(s=r,r=h,h=ip(t,i+1),r){if(h){let t=h[e]-r[e];o[i]=0!==t?(h[a]-r[a])/t:0}l[i]=s?h?t3(o[i-1])!==t3(o[i])?0:(o[i-1]+o[i])/2:o[i-1]:o[i]}!function(t,e,i){let s,r,a,n,o,l=t.length,h=ip(t,0);for(let d=0;d<l-1;++d)if(o=h,h=ip(t,d+1),o&&h){if(t4(e[d],0,iu)){i[d]=i[d+1]=0;continue}(n=Math.pow(s=i[d]/e[d],2)+Math.pow(r=i[d+1]/e[d],2))<=9||(a=3/Math.sqrt(n),i[d]=s*a*e[d],i[d+1]=r*a*e[d])}}(t,o,l),function(t,e,i="x"){let s,r,a,n=ig(i),o=t.length,l=ip(t,0);for(let h=0;h<o;++h){if(r=a,a=l,l=ip(t,h+1),!a)continue;let o=a[i],d=a[n];r&&(s=(o-r[i])/3,a[`cp1${i}`]=o-s,a[`cp1${n}`]=d-s*e[h]),l&&(s=(l[i]-o)/3,a[`cp2${i}`]=o+s,a[`cp2${n}`]=d+s*e[h])}}(t,l,e)}(t,r);else{let i=s?t[t.length-1]:t[0];for(a=0,n=t.length;a<n;++a)l=function(t,e,i,s){let r=t.skip?e:t,a=i.skip?e:i,n=es(e,r),o=es(a,e),l=n/(n+o),h=o/(n+o);l=isNaN(l)?0:l,h=isNaN(h)?0:h;let d=s*l,c=s*h;return{previous:{x:e.x-d*(a.x-r.x),y:e.y-d*(a.y-r.y)},next:{x:e.x+c*(a.x-r.x),y:e.y+c*(a.y-r.y)}}}(i,o=t[a],t[Math.min(a+1,n-!s)%n],e.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,i=o}e.capBezierPoints&&function(t,e){let i,s,r,a,n,o=eU(t[0],e);for(i=0,s=t.length;i<s;++i)n=a,a=o,o=i<s-1&&eU(t[i+1],e),a&&(r=t[i],n&&(r.cp1x=im(r.cp1x,e.left,e.right),r.cp1y=im(r.cp1y,e.top,e.bottom)),o&&(r.cp2x=im(r.cp2x,e.left,e.right),r.cp2y=im(r.cp2y,e.top,e.bottom)))}(t,i)}(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){let i=t.points,s=t.options.spanGaps,r=i.length;if(!r)return[];let a=!!t._loop,{start:n,end:o}=function(t,e,i,s){let r=0,a=e-1;if(i&&!s)for(;r<e&&!t[r].skip;)r++;for(;r<e&&t[r].skip;)r++;for(r%=e,i&&(a+=r);a>r&&t[a%e].skip;)a--;return{start:r,end:a%=e}}(i,r,a,s);if(!0===s)return iF(t,[{start:n,end:o,loop:a}],i,e);let l=o<n?o+r:o,h=!!t._fullLoop&&0===n&&o===r-1;return iF(t,function(t,e,i,s){let r,a=t.length,n=[],o=e,l=t[e];for(r=e+1;r<=i;++r){let i=t[r%a];i.skip||i.stop?l.skip||(s=!1,n.push({start:e%a,end:(r-1)%a,loop:s}),e=o=i.stop?r:null):(o=r,l.skip&&(e=r)),l=i}return null!==o&&n.push({start:e%a,end:o%a,loop:s}),n}(i,n,l,h),i,e)}(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i,s,r=this.options,a=t[e],n=this.points,o=iz(this,{property:e,start:a,end:a});if(!o.length)return;let l=[],h=r.stepped?iE:r.tension||"monotone"===r.cubicInterpolationMode?iT:iO;for(i=0,s=o.length;i<s;++i){let{start:s,end:d}=o[i],c=n[s],u=n[d];if(c===u){l.push(c);continue}let f=Math.abs((a-c[e])/(u[e]-c[e])),p=h(c,u,f,r.stepped);p[e]=t[e],l.push(p)}return 1===l.length?l[0]:l}pathSegment(t,e,i){return rk(this)(t,this,e,i)}path(t,e,i){let s=this.segments,r=rk(this),a=this._loop;for(let n of(e=e||0,i=i||this.points.length-e,s))a&=r(t,this,n,{start:e,end:e+i-1});return!!a}draw(t,e,i,s){let r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),function(t,e,i,s){if(rD&&!e.options.segment){let r;(r=e._path)||(r=e._path=new Path2D,e.path(r,i,s)&&r.closePath()),ry(t,e.options),t.stroke(r)}else{let{segments:r,options:a}=e,n=rk(e);for(let o of r)ry(t,a,o.style),t.beginPath(),n(t,e,o,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}}(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function rS(t,e,i,s){let r=t.options,{[i]:a}=t.getProps([i],s);return Math.abs(e-a)<r.radius+r.hitRadius}class rP extends sY{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:r,y:a}=this.getProps(["x","y"],i);return Math.pow(t-r,2)+Math.pow(e-a,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return rS(this,t,"x",e)}inYRange(t,e){return rS(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0,i=(e=Math.max(e,e&&t.hoverRadius||0))&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;!this.skip&&!(i.radius<.1)&&eU(this,e,this.size(i)/2)&&(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,eY(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}function rO(t,e){let i,s,r,a,n,{x:o,y:l,base:h,width:d,height:c}=t.getProps(["x","y","base","width","height"],e);return t.horizontal?(n=c/2,i=Math.min(o,h),s=Math.max(o,h),r=l-n,a=l+n):(i=o-(n=d/2),s=o+n,r=Math.min(l,h),a=Math.max(l,h)),{left:i,top:r,right:s,bottom:a}}function rE(t,e,i,s){return t?0:eo(e,i,s)}function rT(t,e,i,s){let r=null===e,a=null===i,n=t&&!(r&&a)&&rO(t,s);return n&&(r||el(e,n.left,n.right))&&(a||el(i,n.top,n.bottom))}function rN(t,e){t.rect(e.x,e.y,e.w,e.h)}function rA(t,e,i={}){let s=t.x!==i.x?-e:0,r=t.y!==i.y?-e:0,a=(t.x+t.w!==i.x+i.w?e:0)-s,n=(t.y+t.h!==i.y+i.h?e:0)-r;return{x:t.x+s,y:t.y+r,w:t.w+a,h:t.h+n,radius:t.radius}}class rj extends sY{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e;let{inflateAmount:i,options:{borderColor:s,backgroundColor:r}}=this,{inner:a,outer:n}=function(t){let e=rO(t),i=e.right-e.left,s=e.bottom-e.top,r=function(t,e,i){let s=t.options.borderWidth,r=t.borderSkipped,a=e5(s);return{t:rE(r.top,a.top,0,i),r:rE(r.right,a.right,0,e),b:rE(r.bottom,a.bottom,0,i),l:rE(r.left,a.left,0,e)}}(t,i/2,s/2),a=function(t,e,i){let{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),r=t.options.borderRadius,a=e3(r),n=Math.min(e,i),o=t.borderSkipped,l=s||tP(r);return{topLeft:rE(!l||o.top||o.left,a.topLeft,0,n),topRight:rE(!l||o.top||o.right,a.topRight,0,n),bottomLeft:rE(!l||o.bottom||o.left,a.bottomLeft,0,n),bottomRight:rE(!l||o.bottom||o.right,a.bottomRight,0,n)}}(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:a},inner:{x:e.left+r.l,y:e.top+r.t,w:i-r.l-r.r,h:s-r.t-r.b,radius:{topLeft:Math.max(0,a.topLeft-Math.max(r.t,r.l)),topRight:Math.max(0,a.topRight-Math.max(r.t,r.r)),bottomLeft:Math.max(0,a.bottomLeft-Math.max(r.b,r.l)),bottomRight:Math.max(0,a.bottomRight-Math.max(r.b,r.r))}}}}(this),o=(e=n.radius).topLeft||e.topRight||e.bottomLeft||e.bottomRight?eJ:rN;t.save(),(n.w!==a.w||n.h!==a.h)&&(t.beginPath(),o(t,rA(n,i,a)),t.clip(),o(t,rA(a,-i,n)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),o(t,rA(a,i)),t.fillStyle=r,t.fill(),t.restore()}inRange(t,e,i){return rT(this,t,e,i)}inXRange(t,e){return rT(this,t,null,e)}inYRange(t,e){return rT(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:r}=this.getProps(["x","y","base","horizontal"],t);return{x:r?(e+s)/2:e,y:r?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}let rL=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],rI=rL.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function rR(t,e,i,s){if(s)return;let r=e[t],a=i[t];return"angle"===t&&(r=ea(r),a=ea(a)),{property:t,start:r,end:a}}function rz(t,e,i){for(;e>t;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function rF(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}function rW(t,e){let i=[],s=!1;return tS(t)?(s=!0,i=t):i=function(t,e){let{x:i=null,y:s=null}=t||{},r=e.points,a=[];return e.segments.forEach(({start:t,end:e})=>{e=rz(t,e,r);let n=r[t],o=r[e];null!==s?(a.push({x:n.x,y:s}),a.push({x:o.x,y:s})):null!==i&&(a.push({x:i,y:n.y}),a.push({x:i,y:o.y}))}),a}(t,e),i.length?new rC({points:i,options:{tension:0},_loop:s,_fullLoop:s}):null}class rB{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:s,y:r,radius:a}=this;return e=e||{start:0,end:tG},t.arc(s,r,a,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:s}=this,r=t.angle;return{x:e+Math.cos(r)*s,y:i+Math.sin(r)*s,angle:r}}}function rV(t,e,i){let{segments:s,points:r}=e,a=!0,n=!1;for(let o of(t.beginPath(),s)){let{start:s,end:l}=o,h=r[s],d=r[rz(s,l,r)];a?(t.moveTo(h.x,h.y),a=!1):(t.lineTo(h.x,i),t.lineTo(h.x,h.y)),(n=!!e.pathSegment(t,o,{move:n}))?t.closePath():t.lineTo(d.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function rH(t,e){let{line:i,target:s,property:r,color:a,scale:n,clip:o}=e;for(let{source:e,target:l,start:h,end:d}of function(t,e,i){let s=t.segments,r=t.points,a=e.points,n=[];for(let t of s){let{start:s,end:o}=t;o=rz(s,o,r);let l=rR(i,r[s],r[o],t.loop);if(!e.segments){n.push({source:t,target:l,start:r[s],end:r[o]});continue}for(let s of iz(e,l)){let e=rR(i,a[s.start],a[s.end],s.loop);for(let a of iR(t,r,e))n.push({source:a,target:s,start:{[i]:rF(l,e,"start",Math.max)},end:{[i]:rF(l,e,"end",Math.min)}})}}return n}(i,s,r)){let c,{style:{backgroundColor:u=a}={}}=e,f=!0!==s;t.save(),t.fillStyle=u,function(t,e,i,s){let r=e.chart.chartArea,{property:a,start:n,end:o}=s||{};if("x"===a||"y"===a){let e,s,l,h;"x"===a?(e=n,s=r.top,l=o,h=r.bottom):(e=r.left,s=n,l=r.right,h=o),t.beginPath(),i&&(e=Math.max(e,i.left),l=Math.min(l,i.right),s=Math.max(s,i.top),h=Math.min(h,i.bottom)),t.rect(e,s,l-e,h-s),t.clip()}}(t,n,o,f&&rR(r,h,d)),t.beginPath();let p=!!i.pathSegment(t,e);if(f){p?t.closePath():rY(t,s,d,r);let e=!!s.pathSegment(t,l,{move:p,reverse:!0});(c=p&&e)||rY(t,s,h,r)}t.closePath(),t.fill(c?"evenodd":"nonzero"),t.restore()}}function rY(t,e,i,s){let r=e.interpolate(i,s);r&&t.lineTo(r.x,r.y)}let r$=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},rU=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class rZ extends sY{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=tj(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let t,e,{options:i,ctx:s}=this;if(!i.display){this.width=this.height=0;return}let r=i.labels,a=e8(r.font),n=a.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:h}=r$(r,n);s.font=a.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(o,n,l,h)+10):(e=this.maxHeight,t=this._fitCols(o,a,l,h)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:r,maxWidth:a,options:{labels:{padding:n}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+n,d=t;r.textAlign="left",r.textBaseline="middle";let c=-1,u=-h;return this.legendItems.forEach((t,f)=>{let p=i+e/2+r.measureText(t.text).width;(0===f||l[l.length-1]+p+2*n>a)&&(d+=h,l[l.length-(f>0?0:1)]=0,u+=h,c++),o[f]={left:0,top:u,row:c,width:p,height:s},l[l.length-1]+=p+n}),d}_fitCols(t,e,i,s){let{ctx:r,maxHeight:a,options:{labels:{padding:n}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],h=a-t,d=n,c=0,u=0,f=0,p=0;return this.legendItems.forEach((t,a)=>{var g,m,x,b,y,v,_,w,M,k,D,C;let S,P,{itemWidth:O,itemHeight:E}=(g=i,m=e,x=r,b=t,y=s,{itemWidth:(v=b,_=g,w=m,M=x,(S=v.text)&&"string"!=typeof S&&(S=S.reduce((t,e)=>t.length>e.length?t:e)),_+w.size/2+M.measureText(S).width),itemHeight:(k=y,D=b,C=m.lineHeight,P=k,"string"!=typeof D.text&&(P=rX(D,C)),P)});a>0&&u+E+2*n>h&&(d+=c+n,l.push({width:c,height:u}),f+=c+n,p++,c=u=0),o[a]={left:f,top:u,col:p,width:O,height:E},c=Math.max(c,O),u+=E+n}),d+=c,l.push({width:c,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:r}}=this,a=iN(r,this.left,this.width);if(this.isHorizontal()){let r=0,n=eb(i,this.left+s,this.right-this.lineWidths[r]);for(let o of e)r!==o.row&&(r=o.row,n=eb(i,this.left+s,this.right-this.lineWidths[r])),o.top+=this.top+t+s,o.left=a.leftForLtr(a.x(n),o.width),n+=o.width+s}else{let r=0,n=eb(i,this.top+t+s,this.bottom-this.columnSizes[r].height);for(let o of e)o.col!==r&&(r=o.col,n=eb(i,this.top+t+s,this.bottom-this.columnSizes[r].height)),o.top=n,o.left+=this.left+s,o.left=a.leftForLtr(a.x(o.left),o.width),n+=o.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){let t=this.ctx;eZ(t,this),this._draw(),eX(t)}}_draw(){let t,{options:e,columnSizes:i,lineWidths:s,ctx:r}=this,{align:a,labels:n}=e,o=eW.color,l=iN(e.rtl,this.left,this.width),h=e8(n.font),{padding:d}=n,c=h.size,u=c/2;this.drawTitle(),r.textAlign=l.textAlign("left"),r.textBaseline="middle",r.lineWidth=.5,r.font=h.string;let{boxWidth:f,boxHeight:p,itemHeight:g}=r$(n,c),m=function(t,e,i){if(isNaN(f)||f<=0||isNaN(p)||p<0)return;r.save();let s=tT(i.lineWidth,1);if(r.fillStyle=tT(i.fillStyle,o),r.lineCap=tT(i.lineCap,"butt"),r.lineDashOffset=tT(i.lineDashOffset,0),r.lineJoin=tT(i.lineJoin,"miter"),r.lineWidth=s,r.strokeStyle=tT(i.strokeStyle,o),r.setLineDash(tT(i.lineDash,[])),n.usePointStyle){let a={radius:p*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:s};e$(r,a,l.xPlus(t,f/2),e+u,n.pointStyleWidth&&f)}else{let a=e+Math.max((c-p)/2,0),n=l.leftForLtr(t,f),o=e3(i.borderRadius);r.beginPath(),Object.values(o).some(t=>0!==t)?eJ(r,{x:n,y:a,w:f,h:p,radius:o}):r.rect(n,a,f,p),r.fill(),0!==s&&r.stroke()}r.restore()},x=function(t,e,i){eK(r,i.text,t,e+g/2,h,{strikethrough:i.hidden,textAlign:l.textAlign(i.textAlign)})},b=this.isHorizontal(),y=this._computeTitleHeight();t=b?{x:eb(a,this.left+d,this.right-s[0]),y:this.top+d+y,line:0}:{x:this.left+d,y:eb(a,this.top+y+d,this.bottom-i[0].height),line:0},iA(this.ctx,e.textDirection);let v=g+d;this.legendItems.forEach((o,c)=>{r.strokeStyle=o.fontColor,r.fillStyle=o.fontColor;let p=r.measureText(o.text).width,g=l.textAlign(o.textAlign||(o.textAlign=n.textAlign)),_=f+u+p,w=t.x,M=t.y;if(l.setWidth(this.width),b?c>0&&w+_+d>this.right&&(M=t.y+=v,t.line++,w=t.x=eb(a,this.left+d,this.right-s[t.line])):c>0&&M+v>this.bottom&&(w=t.x=w+i[t.line].width+d,t.line++,M=t.y=eb(a,this.top+y+d,this.bottom-i[t.line].height)),m(l.x(w),M,o),w=ey(g,w+f+u,b?w+_:this.right,e.rtl),x(l.x(w),M,o),b)t.x+=_+d;else if("string"!=typeof o.text){let e=h.lineHeight;t.y+=rX(o,e)+d}else t.y+=v}),ij(this.ctx,e.textDirection)}drawTitle(){let t,e=this.options,i=e.title,s=e8(i.font),r=e4(i.padding);if(!i.display)return;let a=iN(e.rtl,this.left,this.width),n=this.ctx,o=i.position,l=s.size/2,h=r.top+l,d=this.left,c=this.width;if(this.isHorizontal())c=Math.max(...this.lineWidths),t=this.top+h,d=eb(e.align,d,this.right-c);else{let i=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);t=h+eb(e.align,this.top,this.bottom-i-e.labels.padding-this._computeTitleHeight())}let u=eb(o,d,d+c);n.textAlign=a.textAlign(ex(o)),n.textBaseline="middle",n.strokeStyle=i.color,n.fillStyle=i.color,n.font=s.string,eK(n,i.text,u,t,s)}_computeTitleHeight(){let t=this.options.title,e=e8(t.font),i=e4(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,r;if(el(t,this.left,this.right)&&el(e,this.top,this.bottom)){for(i=0,r=this.legendHitBoxes;i<r.length;++i)if(el(t,(s=r[i]).left,s.left+s.width)&&el(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){var e,i;let s=this.options;if(e=t.type,i=s,("mousemove"!==e&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))return;let r=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){let e=this._hoveredItem,i=rU(e,r);e&&!i&&tj(s.onLeave,[t,e,this],this),this._hoveredItem=r,r&&!i&&tj(s.onHover,[t,r,this],this)}else r&&tj(s.onClick,[t,r,this],this)}}function rX(t,e){return e*(t.text?t.text.length:0)}class rq extends sY{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let s=tS(i.text)?i.text.length:1;this._padding=e4(i.padding);let r=s*e8(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){let t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){let e,i,s,{top:r,left:a,bottom:n,right:o,options:l}=this,h=l.align,d=0;return this.isHorizontal()?(i=eb(h,a,o),s=r+t,e=o-a):("left"===l.position?(i=a+t,s=eb(h,n,r),d=-.5*tq):(i=o-t,s=eb(h,r,n),d=.5*tq),e=n-r),{titleX:i,titleY:s,maxWidth:e,rotation:d}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=e8(e.font),s=i.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:n,rotation:o}=this._drawArgs(s);eK(t,e.text,0,0,i,{color:e.color,maxWidth:n,rotation:o,textAlign:ex(e.align),textBaseline:"middle",translation:[r,a]})}}new WeakMap;let rG={average(t){let e,i;if(!t.length)return!1;let s=new Set,r=0,a=0;for(e=0,i=t.length;e<i;++e){let i=t[e].element;if(i&&i.hasValue()){let t=i.tooltipPosition();s.add(t.x),r+=t.y,++a}}return 0!==a&&0!==s.size&&{x:[...s].reduce((t,e)=>t+e)/s.size,y:r/a}},nearest(t,e){let i,s,r;if(!t.length)return!1;let a=e.x,n=e.y,o=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){let s=t[i].element;if(s&&s.hasValue()){let t=es(e,s.getCenterPoint());t<o&&(o=t,r=s)}}if(r){let t=r.tooltipPosition();a=t.x,n=t.y}return{x:a,y:n}}};function rK(t,e){return e&&(tS(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function rJ(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function rQ(t,e){let i=t.chart.ctx,{body:s,footer:r,title:a}=t,{boxWidth:n,boxHeight:o}=e,l=e8(e.bodyFont),h=e8(e.titleFont),d=e8(e.footerFont),c=a.length,u=r.length,f=s.length,p=e4(e.padding),g=p.height,m=0,x=s.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);x+=t.beforeBody.length+t.afterBody.length,c&&(g+=c*h.lineHeight+(c-1)*e.titleSpacing+e.titleMarginBottom),x&&(g+=f*(e.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(x-f)*l.lineHeight+(x-1)*e.bodySpacing),u&&(g+=e.footerMarginTop+u*d.lineHeight+(u-1)*e.footerSpacing);let b=0,y=function(t){m=Math.max(m,i.measureText(t).width+b)};return i.save(),i.font=h.string,tL(t.title,y),i.font=l.string,tL(t.beforeBody.concat(t.afterBody),y),b=e.displayColors?n+2+e.boxPadding:0,tL(s,t=>{tL(t.before,y),tL(t.lines,y),tL(t.after,y)}),b=0,i.font=d.string,tL(t.footer,y),i.restore(),{width:m+=p.width,height:g}}function r0(t,e,i){let s=i.yAlign||e.yAlign||function(t,e){let{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||function(t,e,i,s){let{x:r,width:a}=i,{width:n,chartArea:{left:o,right:l}}=t,h="center";return"center"===s?h=r<=(o+l)/2?"left":"right":r<=a/2?h="left":r>=n-a/2&&(h="right"),function(t,e,i,s){let{x:r,width:a}=s,n=i.caretSize+i.caretPadding;if("left"===t&&r+a+n>e.width||"right"===t&&r-a-n<0)return!0}(h,t,e,i)&&(h="center"),h}(t,e,i,s),yAlign:s}}function r1(t,e,i,s){let{caretSize:r,caretPadding:a,cornerRadius:n}=t,{xAlign:o,yAlign:l}=i,h=r+a,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:f}=e3(n),p=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,o),g=function(t,e,i){let{y:s,height:r}=t;return"top"===e?s+=i:"bottom"===e?s-=r+i:s-=r/2,s}(e,l,h);return"center"===l?"left"===o?p+=h:"right"===o&&(p-=h):"left"===o?p-=Math.max(d,u)+r:"right"===o&&(p+=Math.max(c,f)+r),{x:eo(p,0,s.width-e.width),y:eo(g,0,s.height-e.height)}}function r2(t,e,i){let s=e4(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function r5(t,e){let i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}let r3={beforeTitle:tk,title(t){if(t.length>0){let e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:tk,beforeBody:tk,beforeLabel:tk,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");let i=t.formattedValue;return tC(i)||(e+=i),e},labelColor(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:tk,afterBody:tk,beforeFooter:tk,footer:tk,afterFooter:tk};function r4(t,e,i,s){let r=t[e].call(i,s);return void 0===r?r3[e].call(i,s):r}class r8 extends sY{static positioners=rG;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,r=new iX(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){var t;return this.$context||(this.$context=(t=this.chart.getContext(),e9(t,{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"})))}getTitle(t,e){let{callbacks:i}=e,s=r4(i,"beforeTitle",this,t),r=r4(i,"title",this,t),a=r4(i,"afterTitle",this,t),n=[];return n=rK(n,rJ(s)),n=rK(n,rJ(r)),n=rK(n,rJ(a))}getBeforeBody(t,e){return rK([],rJ(r4(e.callbacks,"beforeBody",this,t)))}getBody(t,e){let{callbacks:i}=e,s=[];return tL(t,t=>{let e={before:[],lines:[],after:[]},r=r5(i,t);rK(e.before,rJ(r4(r,"beforeLabel",this,t))),rK(e.lines,r4(r,"label",this,t)),rK(e.after,rJ(r4(r,"afterLabel",this,t))),s.push(e)}),s}getAfterBody(t,e){return rK([],rJ(r4(e.callbacks,"afterBody",this,t)))}getFooter(t,e){let{callbacks:i}=e,s=r4(i,"beforeFooter",this,t),r=r4(i,"footer",this,t),a=r4(i,"afterFooter",this,t),n=[];return n=rK(n,rJ(s)),n=rK(n,rJ(r)),n=rK(n,rJ(a))}_createItems(t){let e,i,s=this._active,r=this.chart.data,a=[],n=[],o=[],l=[];for(e=0,i=s.length;e<i;++e)l.push(function(t,e){let{element:i,datasetIndex:s,index:r}=e,a=t.getDatasetMeta(s).controller,{label:n,value:o}=a.getLabelAndValue(r);return{chart:t,label:n,parsed:a.getParsed(r),raw:t.data.datasets[s].data[r],formattedValue:o,dataset:a.getDataset(),dataIndex:r,datasetIndex:s,element:i}}(this.chart,s[e]));return t.filter&&(l=l.filter((e,i,s)=>t.filter(e,i,s,r))),t.itemSort&&(l=l.sort((e,i)=>t.itemSort(e,i,r))),tL(l,e=>{let i=r5(t.callbacks,e);a.push(r4(i,"labelColor",this,e)),n.push(r4(i,"labelPointStyle",this,e)),o.push(r4(i,"labelTextColor",this,e))}),this.labelColors=a,this.labelPointStyles=n,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){let i,s=this.options.setContext(this.getContext()),r=this._active,a=[];if(r.length){let t=rG[s.position].call(this,r,this._eventPosition);a=this._createItems(s),this.title=this.getTitle(a,s),this.beforeBody=this.getBeforeBody(a,s),this.body=this.getBody(a,s),this.afterBody=this.getAfterBody(a,s),this.footer=this.getFooter(a,s);let e=this._size=rQ(this,s),n=Object.assign({},t,e),o=r0(this.chart,s,n),l=r1(s,n,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,i={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(i={opacity:0});this._tooltipItems=a,this.$context=void 0,i&&this._resolveAnimations().update(this,i),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let r=this.getCaretPosition(t,i,s);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,i){let s,r,a,n,o,l,{xAlign:h,yAlign:d}=this,{caretSize:c,cornerRadius:u}=i,{topLeft:f,topRight:p,bottomLeft:g,bottomRight:m}=e3(u),{x:x,y:b}=t,{width:y,height:v}=e;return"center"===d?(o=b+v/2,"left"===h?(r=(s=x)-c,n=o+c,l=o-c):(r=(s=x+y)+c,n=o-c,l=o+c),a=s):(r="left"===h?x+Math.max(f,g)+c:"right"===h?x+y-Math.max(p,m)-c:this.caretX,"top"===d?(o=(n=b)-c,s=r-c,a=r+c):(o=(n=b+v)+c,s=r+c,a=r-c),l=n),{x1:s,x2:r,x3:a,y1:n,y2:o,y3:l}}drawTitle(t,e,i){let s,r,a,n=this.title,o=n.length;if(o){let l=iN(i.rtl,this.x,this.width);for(a=0,t.x=r2(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",s=e8(i.titleFont),r=i.titleSpacing,e.fillStyle=i.titleColor,e.font=s.string;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+r,a+1===o&&(t.y+=i.titleMarginBottom-r)}}_drawColorBox(t,e,i,s,r){let a=this.labelColors[i],n=this.labelPointStyles[i],{boxHeight:o,boxWidth:l}=r,h=e8(r.bodyFont),d=r2(this,"left",r),c=s.x(d),u=o<h.lineHeight?(h.lineHeight-o)/2:0,f=e.y+u;if(r.usePointStyle){let e={radius:Math.min(l,o)/2,pointStyle:n.pointStyle,rotation:n.rotation,borderWidth:1},i=s.leftForLtr(c,l)+l/2,h=f+o/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,eY(t,e,i,h),t.strokeStyle=a.borderColor,t.fillStyle=a.backgroundColor,eY(t,e,i,h)}else{t.lineWidth=tP(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,t.strokeStyle=a.borderColor,t.setLineDash(a.borderDash||[]),t.lineDashOffset=a.borderDashOffset||0;let e=s.leftForLtr(c,l),i=s.leftForLtr(s.xPlus(c,1),l-2),n=e3(a.borderRadius);Object.values(n).some(t=>0!==t)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,eJ(t,{x:e,y:f,w:l,h:o,radius:n}),t.fill(),t.stroke(),t.fillStyle=a.backgroundColor,t.beginPath(),eJ(t,{x:i,y:f+1,w:l-2,h:o-2,radius:n}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(e,f,l,o),t.strokeRect(e,f,l,o),t.fillStyle=a.backgroundColor,t.fillRect(i,f+1,l-2,o-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let s,r,a,n,o,l,{body:h}=this,{bodySpacing:d,bodyAlign:c,displayColors:u,boxHeight:f,boxWidth:p,boxPadding:g}=i,m=e8(i.bodyFont),x=m.lineHeight,b=0,y=iN(i.rtl,this.x,this.width),v=function(i){e.fillText(i,y.x(t.x+b),t.y+x/2),t.y+=x+d},_=y.textAlign(c);for(e.textAlign=c,e.textBaseline="middle",e.font=m.string,t.x=r2(this,_,i),e.fillStyle=i.bodyColor,tL(this.beforeBody,v),b=u&&"right"!==_?"center"===c?p/2+g:p+2+g:0,a=0,o=h.length;a<o;++a){for(s=h[a],e.fillStyle=this.labelTextColors[a],tL(s.before,v),r=s.lines,u&&r.length&&(this._drawColorBox(e,t,a,y,i),x=Math.max(m.lineHeight,f)),n=0,l=r.length;n<l;++n)v(r[n]),x=m.lineHeight;tL(s.after,v)}b=0,x=m.lineHeight,tL(this.afterBody,v),t.y-=d}drawFooter(t,e,i){let s,r,a=this.footer,n=a.length;if(n){let o=iN(i.rtl,this.x,this.width);for(t.x=r2(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=o.textAlign(i.footerAlign),e.textBaseline="middle",s=e8(i.footerFont),e.fillStyle=i.footerColor,e.font=s.string,r=0;r<n;++r)e.fillText(a[r],o.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:r,yAlign:a}=this,{x:n,y:o}=t,{width:l,height:h}=i,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:f}=e3(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(n+d,o),"top"===a&&this.drawCaret(t,e,i,s),e.lineTo(n+l-c,o),e.quadraticCurveTo(n+l,o,n+l,o+c),"center"===a&&"right"===r&&this.drawCaret(t,e,i,s),e.lineTo(n+l,o+h-f),e.quadraticCurveTo(n+l,o+h,n+l-f,o+h),"bottom"===a&&this.drawCaret(t,e,i,s),e.lineTo(n+u,o+h),e.quadraticCurveTo(n,o+h,n,o+h-u),"center"===a&&"left"===r&&this.drawCaret(t,e,i,s),e.lineTo(n,o+d),e.quadraticCurveTo(n,o,n+d,o),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,r=i&&i.y;if(s||r){let i=rG[t.position].call(this,this._active,this._eventPosition);if(!i)return;let a=this._size=rQ(this,t),n=Object.assign({},i,this._size),o=r0(e,t,n),l=r1(t,n,o,e);(s._to!==l.x||r._to!==l.y)&&(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=a.width,this.height=a.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},r={x:this.x,y:this.y};i=.001>Math.abs(i)?0:i;let a=e4(e.padding),n=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&n&&(t.save(),t.globalAlpha=i,this.drawBackground(r,t,s,e),iA(t,e.textDirection),r.y+=a.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),ij(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:t,index:e})=>{let i=this.chart.getDatasetMeta(t);if(!i)throw Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),r=!tI(i,s),a=this._positionChanged(s,e);(r||a)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,r=this._active||[],a=this._getActiveElements(t,r,e,i),n=this._positionChanged(a,t),o=e||!tI(a,r)||n;return o&&(this._active=a,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),o}_getActiveElements(t,e,i,s){let r=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));let a=this.chart.getElementsAtEventForMode(t,r.mode,r,i);return r.reverse&&a.reverse(),a}_positionChanged(t,e){let{caretX:i,caretY:s,options:r}=this,a=rG[r.position].call(this,t,e);return!1!==a&&(i!==a.x||s!==a.y)}}let r6=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i),r9=(t,e)=>null===t?null:eo(Math.round(t),0,e);function r7(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class at extends sJ{static id="category";static defaults={ticks:{callback:r7}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let t=this.getLabels();for(let{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(tC(t))return null;let i=this.getLabels();return r9(e=isFinite(e)&&i[e]===t?e:function(t,e,i,s){let r=t.indexOf(e);return -1===r?r6(t,e,i,s):r!==t.lastIndexOf(e)?i:r}(i,t,tT(e,t),this._addedLabels),i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],r=this.getLabels();r=0===t&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-!i,1),this._startValue=this.min-.5*!!i;for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return r7.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function ae(t,e,{horizontal:i,minRotation:s}){let r=t7(s),a=(i?Math.sin(r):Math.cos(r))||.001,n=.75*e*(""+t).length;return Math.min(e/a,n)}class ai extends sJ{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return tC(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:r}=this,a=t=>s=e?s:t,n=t=>r=i?r:t;if(t){let t=t3(s),e=t3(r);t<0&&e<0?n(0):t>0&&e>0&&a(0)}if(s===r){let e=0===r?1:Math.abs(.05*r);n(r+e),t||a(s-e)}this.min=s,this.max=r}getTickLimit(){let t,{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),e&&(t=Math.min(e,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit(),s=function(t,e){let i,s,r,a,n=[],{bounds:o,step:l,min:h,max:d,precision:c,count:u,maxTicks:f,maxDigits:p,includeBounds:g}=t,m=l||1,x=f-1,{min:b,max:y}=e,v=!tC(h),_=!tC(d),w=!tC(u),M=(y-b)/(p+1),k=t8((y-b)/x/m)*m;if(k<1e-14&&!v&&!_)return[{value:b},{value:y}];(a=Math.ceil(y/k)-Math.floor(b/k))>x&&(k=t8(a*k/x/m)*m),tC(c)||(k=Math.ceil(k*(i=Math.pow(10,c)))/i),"ticks"===o?(s=Math.floor(b/k)*k,r=Math.ceil(y/k)*k):(s=b,r=y),v&&_&&l&&function(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}((d-h)/l,k/1e3)?(a=Math.round(Math.min((d-h)/k,f)),k=(d-h)/a,s=h,r=d):w?(s=v?h:s,k=((r=_?d:r)-s)/(a=u-1)):a=t4(a=(r-s)/k,Math.round(a),k/1e3)?Math.round(a):Math.ceil(a);let D=Math.max(ee(k),ee(s));s=Math.round(s*(i=Math.pow(10,tC(c)?D:c)))/i,r=Math.round(r*i)/i;let C=0;for(v&&(g&&s!==h?(n.push({value:h}),s<h&&C++,t4(Math.round((s+C*k)*i)/i,h,ae(h,M,t))&&C++):s<h&&C++);C<a;++C){let t=Math.round((s+C*k)*i)/i;if(_&&t>d)break;n.push({value:t})}return _&&g&&r!==d?n.length&&t4(n[n.length-1].value,d,ae(d,M,t))?n[n.length-1].value=d:n.push({value:d}):_&&r!==d||n.push({value:r}),n}({maxTicks:i=Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&t9(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return eN(t,this.chart.options.locale,this.options.ticks.format)}}class as extends ai{static id="linear";static defaults={ticks:{callback:ej.formatters.numeric}};determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=tO(t)?t:0,this.max=tO(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=t7(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001;return Math.ceil(e/Math.min(40,this._resolveTickFontOptions(0).lineHeight/s))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let ar=t=>Math.floor(t5(t)),aa=(t,e)=>Math.pow(10,ar(t)+e);function an(t){return 1==t/Math.pow(10,ar(t))}function ao(t,e,i){let s=Math.pow(10,i),r=Math.floor(t/s);return Math.ceil(e/s)-r}class al extends sJ{static id="logarithmic";static defaults={ticks:{callback:ej.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=ai.prototype.parse.apply(this,[t,e]);if(0===i){this._zero=!0;return}return tO(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=tO(t)?Math.max(0,t):null,this.max=tO(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!tO(this._userMin)&&(this.min=t===aa(this.min,0)?aa(this.min,-1):aa(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,r=e=>i=t?i:e,a=t=>s=e?s:t;i===s&&(i<=0?(r(1),a(10)):(r(aa(i,-1)),a(aa(s,1)))),i<=0&&r(aa(s,-1)),s<=0&&a(aa(i,1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e=function(t,{min:e,max:i}){e=tE(t.min,e);let s=[],r=ar(e),a=function(t,e){let i=ar(e-t);for(;ao(t,e,i)>10;)i++;for(;10>ao(t,e,i);)i--;return Math.min(i,ar(t))}(e,i),n=a<0?Math.pow(10,Math.abs(a)):1,o=Math.pow(10,a),l=r>a?Math.pow(10,r):0,h=Math.round((e-l)*n)/n,d=Math.floor((e-l)/o/10)*o*10,c=Math.floor((h-d)/Math.pow(10,a)),u=tE(t.min,Math.round((l+d+c*Math.pow(10,a))*n)/n);for(;u<i;)s.push({value:u,major:an(u),significand:c}),c>=10?c=c<15?15:20:c++,c>=20&&(c=2,n=++a>=0?1:n),u=Math.round((l+d+c*Math.pow(10,a))*n)/n;let f=tE(t.max,u);return s.push({value:f,major:an(f),significand:c}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&t9(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":eN(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=t5(t),this._valueRange=t5(this.max)-t5(t)}getPixelForValue(t){return((void 0===t||0===t)&&(t=this.min),null===t||isNaN(t))?NaN:this.getPixelForDecimal(t===this.min?0:(t5(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function ah(t){let e=t.ticks;if(e.display&&t.display){let t=e4(e.backdropPadding);return tT(e.font&&e.font.size,eW.font.size)+t.height}return 0}function ad(t,e,i,s,r){return t===s||t===r?{start:e-i/2,end:e+i/2}:t<s||t>r?{start:e-i,end:e}:{start:e,end:e+i}}function ac(t,e,i,s){let{ctx:r}=t;if(i)r.arc(t.xCenter,t.yCenter,e,0,tG);else{let i=t.getPointPosition(0,e);r.moveTo(i.x,i.y);for(let a=1;a<s;a++)i=t.getPointPosition(a,e),r.lineTo(i.x,i.y)}}class au extends ai{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:ej.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=e4(ah(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=tO(t)&&!isNaN(t)?t:0,this.max=tO(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/ah(this.options))}generateTickLabels(t){ai.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{let i=tj(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){let t=this.options;t.display&&t.pointLabels.display?function(t){let e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],r=[],a=t._pointLabels.length,n=t.options.pointLabels,o=n.centerPointLabels?tq/a:0;for(let d=0;d<a;d++){var l,h;let a=n.setContext(t.getPointLabelContext(d));r[d]=a.padding;let c=t.getPointPosition(d,t.drawingArea+r[d],o),u=e8(a.font),f=(l=t.ctx,h=tS(h=t._pointLabels[d])?h:[h],{w:function(t,e,i,s){let r,a,n,o,l,h=(s=s||{}).data=s.data||{},d=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(h=s.data={},d=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let c=0,u=i.length;for(r=0;r<u;r++)if(null==(o=i[r])||tS(o)){if(tS(o))for(a=0,n=o.length;a<n;a++)null==(l=o[a])||tS(l)||(c=eB(t,h,d,c,l))}else c=eB(t,h,d,c,o);t.restore();let f=d.length/2;if(f>i.length){for(r=0;r<f;r++)delete h[d[r]];d.splice(0,f)}return c}(l,u.string,h),h:h.length*u.lineHeight});s[d]=f;let p=ea(t.getIndexAngle(d)+o),g=Math.round(et(p));!function(t,e,i,s,r){let a=Math.abs(Math.sin(i)),n=Math.abs(Math.cos(i)),o=0,l=0;s.start<e.l?(o=(e.l-s.start)/a,t.l=Math.min(t.l,e.l-o)):s.end>e.r&&(o=(s.end-e.r)/a,t.r=Math.max(t.r,e.r+o)),r.start<e.t?(l=(e.t-r.start)/n,t.t=Math.min(t.t,e.t-l)):r.end>e.b&&(l=(r.end-e.b)/n,t.b=Math.max(t.b,e.b+l))}(i,e,p,ad(g,c.x,f.w,0,180),ad(g,c.y,f.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){let s,r=[],a=t._pointLabels.length,n=t.options,{centerPointLabels:o,display:l}=n.pointLabels,h={extra:ah(n)/2,additionalAngle:o?tq/a:0};for(let n=0;n<a;n++){h.padding=i[n],h.size=e[n];let a=function(t,e,i){var s,r,a,n,o,l,h;let d=t.drawingArea,{extra:c,additionalAngle:u,padding:f,size:p}=i,g=t.getPointPosition(e,d+c+f,u),m=Math.round(et(ea(g.angle+t0))),x=(s=g.y,r=p.h,90===(a=m)||270===a?s-=r/2:(a>270||a<90)&&(s-=r),s),b=0===(n=m)||180===n?"center":n<180?"left":"right",y=(o=g.x,l=p.w,"right"===(h=b)?o-=l:"center"===h&&(o-=l/2),o);return{visible:!0,x:g.x,y:x,textAlign:b,left:y,top:x,right:y+p.w,bottom:x+p.h}}(t,n,h);r.push(a),"auto"===l&&(a.visible=function(t,e){if(!e)return!0;let{left:i,top:s,right:r,bottom:a}=t;return!(eU({x:i,y:s},e)||eU({x:i,y:a},e)||eU({x:r,y:s},e)||eU({x:r,y:a},e))}(a,s),a.visible&&(s=a))}return r}(t,s,r)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){return ea(t*(tG/(this._pointLabels.length||1))+t7(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(tC(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(tC(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){var i;let s=e[t];return i=this.getContext(),e9(i,{label:s,index:t,type:"pointLabel"})}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-t0+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:r}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:r}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),ac(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t,e,i,s=this.ctx,r=this.options,{angleLines:a,grid:n,border:o}=r,l=this._pointLabels.length;if(r.pointLabels.display&&function(t,e){let{ctx:i,options:{pointLabels:s}}=t;for(let r=e-1;r>=0;r--){let e=t._pointLabelItems[r];if(!e.visible)continue;let a=s.setContext(t.getPointLabelContext(r));!function(t,e,i){let{left:s,top:r,right:a,bottom:n}=i,{backdropColor:o}=e;if(!tC(o)){let i=e3(e.borderRadius),l=e4(e.backdropPadding);t.fillStyle=o;let h=s-l.left,d=r-l.top,c=a-s+l.width,u=n-r+l.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),eJ(t,{x:h,y:d,w:c,h:u,radius:i}),t.fill()):t.fillRect(h,d,c,u)}}(i,a,e);let n=e8(a.font),{x:o,y:l,textAlign:h}=e;eK(i,t._pointLabels[r],o,l+n.lineHeight/2,n,{color:a.color,textAlign:h,textBaseline:"middle"})}}(this,l),n.display&&this.ticks.forEach((t,i)=>{if(0!==i||0===i&&this.min<0){e=this.getDistanceFromCenterForValue(t.value);let s=this.getContext(i),r=n.setContext(s),a=o.setContext(s);!function(t,e,i,s,r){let a=t.ctx,n=e.circular,{color:o,lineWidth:l}=e;(n||s)&&o&&l&&!(i<0)&&(a.save(),a.strokeStyle=o,a.lineWidth=l,a.setLineDash(r.dash||[]),a.lineDashOffset=r.dashOffset,a.beginPath(),ac(t,i,n,s),a.closePath(),a.stroke(),a.restore())}(this,r,e,l,a)}}),a.display){for(s.save(),t=l-1;t>=0;t--){let n=a.setContext(this.getPointLabelContext(t)),{color:o,lineWidth:l}=n;l&&o&&(s.lineWidth=l,s.strokeStyle=o,s.setLineDash(n.borderDash),s.lineDashOffset=n.borderDashOffset,e=this.getDistanceFromCenterForValue(r.reverse?this.min:this.max),i=this.getPointPosition(t,e),s.beginPath(),s.moveTo(this.xCenter,this.yCenter),s.lineTo(i.x,i.y),s.stroke())}s.restore()}}drawBorder(){}drawLabels(){let t,e,i=this.ctx,s=this.options,r=s.ticks;if(!r.display)return;let a=this.getIndexAngle(0);i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(a),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((a,n)=>{if(0===n&&this.min>=0&&!s.reverse)return;let o=r.setContext(this.getContext(n)),l=e8(o.font);if(t=this.getDistanceFromCenterForValue(this.ticks[n].value),o.showLabelBackdrop){i.font=l.string,e=i.measureText(a.label).width,i.fillStyle=o.backdropColor;let s=e4(o.backdropPadding);i.fillRect(-e/2-s.left,-t-l.size/2-s.top,e+s.width,l.size+s.height)}eK(i,a.label,0,-t,l,{color:o.color,strokeColor:o.textStrokeColor,strokeWidth:o.textStrokeWidth})}),i.restore()}drawTitle(){}}let af={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},ap=Object.keys(af);function ag(t,e){return t-e}function am(t,e){if(tC(e))return null;let i=t._adapter,{parser:s,round:r,isoWeekday:a}=t._parseOpts,n=e;return("function"==typeof s&&(n=s(n)),tO(n)||(n="string"==typeof s?i.parse(n,s):i.parse(n)),null===n)?null:(r&&(n="week"===r&&(t6(a)||!0===a)?i.startOf(n,"isoWeek",a):i.startOf(n,r)),+n)}function ax(t,e,i,s){let r=ap.length;for(let a=ap.indexOf(t);a<r-1;++a){let t=af[ap[a]],r=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(r*t.size))<=s)return ap[a]}return ap[r-1]}function ab(t,e,i){if(i){if(i.length){let{lo:s,hi:r}=eh(i,e);t[i[s]>=e?i[s]:i[r]]=!0}}else t[e]=!0}function ay(t,e,i){let s,r,a=[],n={},o=e.length;for(s=0;s<o;++s)n[r=e[s]]=s,a.push({value:r,major:!1});return 0!==o&&i?function(t,e,i,s){let r,a,n=t._adapter,o=+n.startOf(e[0].value,s),l=e[e.length-1].value;for(r=o;r<=l;r=+n.add(r,1,s))(a=i[r])>=0&&(e[a].major=!0);return e}(t,a,n,i):a}class av extends sJ{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),s=this._adapter=new su._date(t.adapters.date);s.init(e),tB(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:am(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:r,minDefined:a,maxDefined:n}=this.getUserBounds();function o(t){a||isNaN(t.min)||(s=Math.min(s,t.min)),n||isNaN(t.max)||(r=Math.max(r,t.max))}a&&n||(o(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&o(this.getMinMax(!1))),s=tO(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),r=tO(r)&&!isNaN(r)?r:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let r=this.min,a=function(t,e,i){let s=0,r=t.length;for(;s<r&&t[s]<e;)s++;for(;r>s&&t[r-1]>i;)r--;return s>0||r<t.length?t.slice(s,r):t}(s,r,this.max);return this._unit=e.unit||(i.autoSkip?ax(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):function(t,e,i,s,r){for(let a=ap.length-1;a>=ap.indexOf(i);a--){let i=ap[a];if(af[i].common&&t._adapter.diff(r,s,i)>=e-1)return i}return ap[i?ap.indexOf(i):0]}(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=ap.indexOf(t)+1,i=ap.length;e<i;++e)if(af[ap[e]].common)return ap[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&a.reverse(),ay(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,s=0,r=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),r=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);let a=t.length<3?.5:.25;s=eo(s,0,a),r=eo(r,0,a),this._offsets={start:s,end:r,factor:1/(s+1+r)}}_generate(){let t,e,i=this._adapter,s=this.min,r=this.max,a=this.options,n=a.time,o=n.unit||ax(n.minUnit,s,r,this._getLabelCapacity(s)),l=tT(a.ticks.stepSize,1),h="week"===o&&n.isoWeekday,d=t6(h)||!0===h,c={},u=s;if(d&&(u=+i.startOf(u,"isoWeek",h)),u=+i.startOf(u,d?"day":o),i.diff(r,s,o)>1e5*l)throw Error(s+" and "+r+" are too far apart with stepSize of "+l+" "+o);let f="data"===a.ticks.source&&this.getDataTimestamps();for(t=u,e=0;t<r;t=+i.add(t,l,o),e++)ab(c,t,f);return(t===r||"ticks"===a.bounds||1===e)&&ab(c,t,f),Object.keys(c).sort(ag).map(t=>+t)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let i=this.options.time.displayFormats,s=this._unit,r=e||i[s];return this._adapter.format(t,r)}_tickFormatFunction(t,e,i,s){let r=this.options,a=r.ticks.callback;if(a)return tj(a,[t,e,i],this);let n=r.time.displayFormats,o=this._unit,l=this._majorUnit,h=o&&n[o],d=l&&n[l],c=i[e],u=l&&d&&c&&c.major;return this._adapter.format(t,s||(u?d:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)(s=t[e]).label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=t7(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(s),a=Math.sin(s),n=this._resolveTickFontOptions(0).size;return{w:i*r+n*a,h:i*a+n*r}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,r=this._tickFormatFunction(t,0,ay(this,[t],this._majorUnit),s),a=this._getLabelSize(r),n=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return n>0?n:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){let t,e,i=this._cache.labels||[];if(i.length)return i;let s=this.getLabels();for(t=0,e=s.length;t<e;++t)i.push(am(this,s[t]));return this._cache.labels=this._normalized?i:this.normalize(i)}normalize(t){return ep(t.sort(ag))}}function a_(t,e,i){let s,r,a,n,o=0,l=t.length-1;i?(e>=t[o].pos&&e<=t[l].pos&&({lo:o,hi:l}=ed(t,"pos",e)),{pos:s,time:a}=t[o],{pos:r,time:n}=t[l]):(e>=t[o].time&&e<=t[l].time&&({lo:o,hi:l}=ed(t,"time",e)),{time:s,pos:a}=t[o],{time:r,pos:n}=t[l]);let h=r-s;return h?a+(n-a)*(e-s)/h:a}class aw extends av{static id="timeseries";static defaults=av.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=a_(e,this.min),this._tableRange=a_(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let e,i,s,{min:r,max:a}=this,n=[],o=[];for(e=0,i=t.length;e<i;++e)(s=t[e])>=r&&s<=a&&n.push(s);if(n.length<2)return[{time:r,pos:0},{time:a,pos:1}];for(e=0,i=n.length;e<i;++e)Math.round((n[e+1]+n[e-1])/2)!==(s=n[e])&&o.push({time:s,pos:e/(i-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t}getDecimalForValue(t){return(a_(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return a_(this._table,i*this._tableRange+this._minPos,!0)}}let aM={modes:{point:(t,e)=>aD(t,e,{intersect:!0}),nearest:(t,e,i)=>(function(t,e,i){let s=Number.POSITIVE_INFINITY;return aD(t,e,i).reduce((t,r)=>{var a;let n=r.getCenterPoint(),o="x"===(a=i.axis)?{x:e.x,y:n.y}:"y"===a?{x:n.x,y:e.y}:n,l=es(e,o);return l<s?(t=[r],s=l):l===s&&t.push(r),t},[]).sort((t,e)=>t._index-e._index).slice(0,1)})(t,e,i),x:(t,e,i)=>aD(t,e,{intersect:i.intersect,axis:"x"}),y:(t,e,i)=>aD(t,e,{intersect:i.intersect,axis:"y"})}};function ak(t,e,i){return(aM.modes[i.mode]||aM.modes.nearest)(t,e,i)}function aD(t,e,i){return t.filter(t=>{var s;return i.intersect?t.inRange(e.x,e.y):"x"!==(s=i.axis)&&"y"!==s?t.inRange(e.x,e.y,"x",!0)||t.inRange(e.x,e.y,"y",!0):t.inRange(e.x,e.y,s,!0)})}function aC(t,e,i){let s=Math.cos(i),r=Math.sin(i),a=e.x,n=e.y;return{x:a+s*(t.x-a)-r*(t.y-n),y:n+r*(t.x-a)+s*(t.y-n)}}let aS=(t,e)=>e>t||t.length>e.length&&t.slice(0,e.length)===e,aP=(t,e,i)=>Math.min(i,Math.max(e,t)),aO=(t,e)=>t.value>=t.start-e&&t.value<=t.end+e;function aE(t,{x:e,y:i,x2:s,y2:r},a,{borderWidth:n,hitTolerance:o}){let l=(n+o)/2,h=t.x>=e-l-.001&&t.x<=s+l+.001,d=t.y>=i-l-.001&&t.y<=r+l+.001;return"x"===a?h:"y"===a?d:h&&d}function aT(t,{rect:e,center:i},s,{rotation:r,borderWidth:a,hitTolerance:n}){return aE(aC(t,i,t7(-r)),e,s,{borderWidth:a,hitTolerance:n})}function aN(t,e){let{centerX:i,centerY:s}=t.getProps(["centerX","centerY"],e);return{x:i,y:s}}let aA=t=>"string"==typeof t&&t.endsWith("%"),aj=t=>parseFloat(t)/100,aL=t=>aP(aj(t),0,1),aI=(t,e)=>({x:t,y:e,x2:t,y2:e,width:0,height:0}),aR={box:t=>aI(t.centerX,t.centerY),doughnutLabel:t=>aI(t.centerX,t.centerY),ellipse:t=>({centerX:t.centerX,centerY:t.centerX,radius:0,width:0,height:0}),label:t=>aI(t.centerX,t.centerY),line:t=>aI(t.x,t.y),point:t=>({centerX:t.centerX,centerY:t.centerY,radius:0,width:0,height:0}),polygon:t=>aI(t.centerX,t.centerY)};function az(t,e){return"start"===e?0:"end"===e?t:aA(e)?aL(e)*t:t/2}function aF(t,e,i=!0){return"number"==typeof e?e:aA(e)?(i?aL(e):aj(e))*t:t}function aW(t,e,{borderWidth:i,position:s,xAdjust:r,yAdjust:a},n){let o=tP(n),l=e.width+(o?n.width:0)+i,h=e.height+(o?n.height:0)+i,d=aB(s),c=a$(t.x,l,r,d.x),u=a$(t.y,h,a,d.y);return{x:c,y:u,x2:c+l,y2:u+h,width:l,height:h,centerX:c+l/2,centerY:u+h/2}}function aB(t,e="center"){return tP(t)?{x:tT(t.x,e),y:tT(t.y,e)}:{x:t=tT(t,e),y:t}}let aV=(t,e)=>t&&t.autoFit&&e<1;function aH(t,e){let i=t.font,s=tS(i)?i:[i];return aV(t,e)?s.map(function(t){let i=e8(t);return i.size=Math.floor(t.size*e),i.lineHeight=t.lineHeight,e8(i)}):s.map(t=>e8(t))}function aY(t){return t&&(tU(t.xValue)||tU(t.yValue))}function a$(t,e,i=0,s){return t-az(e,s)+i}function aU(t,e,i){let s=i.init;return s?!0===s?aX(e,i):function(t,e,i){let s=tj(i.init,[{chart:t,properties:e,options:i}]);return!0===s?aX(e,i):tP(s)?s:void 0}(t,e,i):void 0}function aZ(t,e,i){let s=!1;return e.forEach(e=>{tZ(t[e])?(s=!0,i[e]=t[e]):tU(i[e])&&delete i[e]}),s}function aX(t,e){return aR[e.type||"line"](t)}let aq=new Map,aG=t=>isNaN(t)||t<=0,aK=t=>t.reduce(function(t,e){return t+e.string},"");function aJ(t){if(t&&"object"==typeof t){let e=t.toString();return"[object HTMLImageElement]"===e||"[object HTMLCanvasElement]"===e}}function aQ(t,{x:e,y:i},s){s&&(t.translate(e,i),t.rotate(t7(s)),t.translate(-e,-i))}function a0(t,e){if(e&&e.borderWidth)return t.lineCap=e.borderCapStyle||"butt",t.setLineDash(e.borderDash),t.lineDashOffset=e.borderDashOffset,t.lineJoin=e.borderJoinStyle||"miter",t.lineWidth=e.borderWidth,t.strokeStyle=e.borderColor,!0}function a1(t,e){t.shadowColor=e.backgroundShadowColor,t.shadowBlur=e.shadowBlur,t.shadowOffsetX=e.shadowOffsetX,t.shadowOffsetY=e.shadowOffsetY}function a2(t,e){let i=e.content;if(aJ(i))return{width:aF(i.width,e.width),height:aF(i.height,e.height)};let s=aH(e),r=e.textStrokeWidth,a=tS(i)?i:[i],n=a.join()+aK(s)+r+(t._measureText?"-spriting":"");return aq.has(n)||aq.set(n,function(t,e,i,s){t.save();let r=e.length,a=0,n=s;for(let o=0;o<r;o++){let r=i[Math.min(o,i.length-1)];t.font=r.string;let l=e[o];a=Math.max(a,t.measureText(l).width+s),n+=r.lineHeight}return t.restore(),{width:a,height:n}}(t,a,s,r)),aq.get(n)}function a5(t,e,i){let{x:s,y:r,width:a,height:n}=e;t.save(),a1(t,i);let o=a0(t,i);t.fillStyle=i.backgroundColor,t.beginPath(),eJ(t,{x:s,y:r,w:a,h:n,radius:function(t,e,i){for(let e of Object.keys(t))t[e]=aP(t[e],0,i);return t}(e3(i.borderRadius),0,Math.min(a,n)/2)}),t.closePath(),t.fill(),o&&(t.shadowColor=i.borderShadowColor,t.stroke()),t.restore()}function a3(t,e,i,s){let r=i.content;if(aJ(r)){t.save(),t.globalAlpha=function(t,e){let i=t6(t)?t:e;return t6(i)?aP(i,0,1):1}(i.opacity,r.style.opacity),t.drawImage(r,e.x,e.y,e.width,e.height),t.restore();return}let a=tS(r)?r:[r],n=aH(i,s),o=i.color,l=tS(o)?o:[o],h=function(t,e){let{x:i,width:s}=t,r=e.textAlign;return"center"===r?i+s/2:"end"===r||"right"===r?i+s:i}(e,i),d=e.y+i.textStrokeWidth/2;t.save(),t.textBaseline="middle",t.textAlign=i.textAlign,function(t,e){if(e.textStrokeWidth>0)return t.lineJoin="round",t.miterLimit=2,t.lineWidth=e.textStrokeWidth,t.strokeStyle=e.textStrokeColor,!0}(t,i)&&function(t,{x:e,y:i},s,r){t.beginPath();let a=0;s.forEach(function(s,n){let o=r[Math.min(n,r.length-1)],l=o.lineHeight;t.font=o.string,t.strokeText(s,e,i+l/2+a),a+=l}),t.stroke()}(t,{x:h,y:d},a,n),function(t,{x:e,y:i},s,{fonts:r,colors:a}){let n=0;s.forEach(function(s,o){let l=a[Math.min(o,a.length-1)],h=r[Math.min(o,r.length-1)],d=h.lineHeight;t.beginPath(),t.font=h.string,t.fillStyle=l,t.fillText(s,e,i+d/2+n),n+=d,t.fill()})}(t,{x:h,y:d},a,{fonts:n,colors:l}),t.restore()}let a4=["left","bottom","top","right"],a8={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function a6(t,e,i){return tO(e="number"==typeof e?e:t.parse(e))?t.getPixelForValue(e):i}function a9(t,e,i){let s=e[i];if(s||"scaleID"===i)return s;let r=i.charAt(0),a=Object.values(t).filter(t=>t.axis&&t.axis===r);return a.length?a[0].id:r}function a7(t,e){if(t){let i=t.options.reverse;return{start:a6(t,e.min,i?e.end:e.start),end:a6(t,e.max,i?e.start:e.end)}}}function nt(t,e){let{chartArea:i,scales:s}=t,r=s[a9(s,e,"xScaleID")],a=s[a9(s,e,"yScaleID")],n=i.width/2,o=i.height/2;return r&&(n=a6(r,e.xValue,r.left+r.width/2)),a&&(o=a6(a,e.yValue,a.top+a.height/2)),{x:n,y:o}}function ne(t,e){let i=t.scales,s=i[a9(i,e,"xScaleID")],r=i[a9(i,e,"yScaleID")];if(!s&&!r)return{};let{left:a,right:n}=s||t.chartArea,{top:o,bottom:l}=r||t.chartArea,h=nr(s,{min:e.xMin,max:e.xMax,start:a,end:n});a=h.start,n=h.end;let d=nr(r,{min:e.yMin,max:e.yMax,start:l,end:o});return{x:a,y:o=d.start,x2:n,y2:l=d.end,width:n-a,height:l-o,centerX:a+(n-a)/2,centerY:o+(l-o)/2}}function ni(t,e){if(!aY(e)){let i=ne(t,e),s=e.radius;(!s||isNaN(s))&&(e.radius=s=Math.min(i.width,i.height)/2);let r=2*s,a=i.centerX+e.xAdjust,n=i.centerY+e.yAdjust;return{x:a-s,y:n-s,x2:a+s,y2:n+s,centerX:a,centerY:n,width:r,height:r,radius:s}}var i=t,s=e;let r=nt(i,s),a=2*s.radius;return{x:r.x-s.radius+s.xAdjust,y:r.y-s.radius+s.yAdjust,x2:r.x+s.radius+s.xAdjust,y2:r.y+s.radius+s.yAdjust,centerX:r.x+s.xAdjust,centerY:r.y+s.yAdjust,radius:s.radius,width:a,height:a}}function ns(t,e){let i=ne(t,e);return i.initProperties=aU(t,i,e),i.elements=[{type:"label",optionScope:"label",properties:function(t,e,i){let s=i.label;s.backgroundColor="transparent",s.callout.display=!1;let r=aB(s.position),a=e4(s.padding),n=a2(t.ctx,s),o=function({properties:t,options:e},i,s,r){let{x:a,x2:n,width:o}=t;return na({start:a,end:n,size:o,borderWidth:e.borderWidth},{position:s.x,padding:{start:r.left,end:r.right},adjust:e.label.xAdjust,size:i.width})}({properties:e,options:i},n,r,a),l=function({properties:t,options:e},i,s,r){let{y:a,y2:n,height:o}=t;return na({start:a,end:n,size:o,borderWidth:e.borderWidth},{position:s.y,padding:{start:r.top,end:r.bottom},adjust:e.label.yAdjust,size:i.height})}({properties:e,options:i},n,r,a),h=n.width+a.width,d=n.height+a.height;return{x:o,y:l,x2:o+h,y2:l+d,width:h,height:d,centerX:o+h/2,centerY:l+d/2,rotation:s.rotation}}(t,i,e),initProperties:i.initProperties}],i}function nr(t,e){let i=a7(t,e)||e;return{start:Math.min(i.start,i.end),end:Math.max(i.start,i.end)}}function na(t,e){let{start:i,end:s,borderWidth:r}=t,{position:a,padding:{start:n,end:o},adjust:l}=e,h=s-r-i-n-o-e.size;return i+r/2+l+az(h,a)}let nn=["enter","leave"],no=nn.concat("click");function nl({state:t,event:e},i,s,r){let a;for(let n of s)0>r.indexOf(n)&&(a=nh(n.options[i]||t.listeners[i],n,e)||a);return a}function nh(t,e,i){return!0===tj(t,[e.$context,i])}let nd=["afterDraw","beforeDraw"];function nc(t,e,i){if(t.hooked)return tj(e.options[i]||t.hooks[i],[e.$context])}function nu(t,e,i,s){var r,a,n;if(tO(e[i])&&(r=t.options,a=i,n=s,!(tU(r[a])||tU(r[n])))){let s=t[i]!==e[i];return t[i]=e[i],s}}function nf(t,e,i,s){for(let r of i){let i=t[r];if(tU(i)){let t=e.parse(i);s.min=Math.min(s.min,t),s.max=Math.max(s.max,t)}}}class np extends sY{inRange(t,e,i,s){let{x:r,y:a}=aC({x:t,y:e},this.getCenterPoint(s),t7(-this.options.rotation));return aE({x:r,y:a},this.getProps(["x","y","x2","y2"],s),i,this.options)}getCenterPoint(t){return aN(this,t)}draw(t){t.save(),aQ(t,this.getCenterPoint(),this.options.rotation),a5(t,this,this.options),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return ns(t,e)}}np.id="boxAnnotation",np.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,hitTolerance:0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},np.defaultRoutes={borderColor:"color",backgroundColor:"color"},np.descriptors={label:{_fallback:!0}};class ng extends sY{inRange(t,e,i,s){return aT({x:t,y:e},{rect:this.getProps(["x","y","x2","y2"],s),center:this.getCenterPoint(s)},i,{rotation:this.rotation,borderWidth:0,hitTolerance:this.options.hitTolerance})}getCenterPoint(t){return aN(this,t)}draw(t){let e=this.options;e.display&&e.content&&(function(t,e){let{_centerX:i,_centerY:s,_radius:r,_startAngle:a,_endAngle:n,_counterclockwise:o,options:l}=e;t.save();let h=a0(t,l);t.fillStyle=l.backgroundColor,t.beginPath(),t.arc(i,s,r,a,n,o),t.closePath(),t.fill(),h&&t.stroke(),t.restore()}(t,this),t.save(),aQ(t,this.getCenterPoint(),this.rotation),a3(t,this,e,this._fitRatio),t.restore())}resolveElementProperties(t,e){var i,s;let r=(i=t,s=e,i.getSortedVisibleDatasetMetas().reduce(function(t,e){let r=e.controller;return r instanceof sr&&function(t,e,i){if(!e.autoHide)return!0;for(let e=0;e<i.length;e++)if(!i[e].hidden&&t.getDataVisibility(e))return!0}(i,s,e.data)&&(!t||r.innerRadius<t.controller.innerRadius)&&r.options.circumference>=90?e:t},void 0));if(!r)return{};let{controllerMeta:a,point:n,radius:o}=function({chartArea:t},e,i){let{left:s,top:r,right:a,bottom:n}=t,{innerRadius:o,offsetX:l,offsetY:h}=i.controller,d=(s+a)/2+l,c=(r+n)/2+h,u={left:Math.max(d-o,s),right:Math.min(d+o,a),top:Math.max(c-o,r),bottom:Math.min(c+o,n)},f={x:(u.left+u.right)/2,y:(u.top+u.bottom)/2},p=e.spacing+e.borderWidth/2,g=o-p,m=f.y>c,x=function(t,e,i,s){let r=-2*e,a=Math.pow(r,2)-4*(Math.pow(e,2)+Math.pow(i-t,2)-Math.pow(s,2));if(a<=0)return{_startAngle:0,_endAngle:tG};let n=(-r-Math.sqrt(a))/2,o=(-r+Math.sqrt(a))/2;return{_startAngle:ei({x:e,y:i},{x:n,y:t}).angle,_endAngle:ei({x:e,y:i},{x:o,y:t}).angle}}(m?r+p:n-p,d,c,g);return{controllerMeta:{_centerX:d,_centerY:c,_radius:g,_counterclockwise:m,...x},point:f,radius:Math.min(o,Math.min(u.right-u.left,u.bottom-u.top)/2)}}(t,e,r),l=a2(t.ctx,e),h=function({width:t,height:e},i){return 2*i/Math.sqrt(Math.pow(t,2)+Math.pow(e,2))}(l,o);aV(e,h)&&(l={width:l.width*h,height:l.height*h});let{position:d,xAdjust:c,yAdjust:u}=e,f=aW(n,l,{borderWidth:0,position:d,xAdjust:c,yAdjust:u});return{initProperties:aU(t,f,e),...f,...a,rotation:e.rotation,_fitRatio:h}}}ng.id="doughnutLabelAnnotation",ng.defaults={autoFit:!0,autoHide:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderColor:"transparent",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:0,color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,spacing:1,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0},ng.defaultRoutes={};class nm extends sY{inRange(t,e,i,s){return aT({x:t,y:e},{rect:this.getProps(["x","y","x2","y2"],s),center:this.getCenterPoint(s)},i,{rotation:this.rotation,borderWidth:this.options.borderWidth,hitTolerance:this.options.hitTolerance})}getCenterPoint(t){return aN(this,t)}draw(t){let e=this.options,i=!tU(this._visible)||this._visible;e.display&&e.content&&i&&(t.save(),aQ(t,this.getCenterPoint(),this.rotation),function(t,e){let{pointX:i,pointY:s,options:r}=e,a=r.callout,n=a&&a.display&&function(t,e){let i=e.position;return a4.includes(i)?i:function(t,e){let{x:i,y:s,x2:r,y2:a,width:n,height:o,pointX:l,pointY:h,centerX:d,centerY:c,rotation:u}=t,f={x:d,y:c},p=e.start,g=aF(n,p),m=aF(o,p),x=[i,i+g,i+g,r],b=[s+m,a,s,a],y=[];for(let t=0;t<4;t++){let e=aC({x:x[t],y:b[t]},f,t7(u));y.push({position:a4[t],distance:es(e,{x:l,y:h})})}return y.sort((t,e)=>t.distance-e.distance)[0].position}(t,e)}(e,a);if(!n||function(t,e,i){let{pointX:s,pointY:r}=t,a=e.margin,n=s,o=r;return"left"===i?n+=a:"right"===i?n-=a:"top"===i?o+=a:"bottom"===i&&(o-=a),t.inRange(n,o)}(e,a,n))return;if(t.save(),t.beginPath(),!a0(t,a))return t.restore();let{separatorStart:o,separatorEnd:l}=function(t,e){let i,s,{x:r,y:a,x2:n,y2:o}=t,l=function(t,e){let{width:i,height:s,options:r}=t,a=r.callout.margin+r.borderWidth/2;return"right"===e?i+a:"bottom"===e?s+a:-a}(t,e);return s="left"===e||"right"===e?{x:(i={x:r+l,y:a}).x,y:o}:{x:n,y:(i={x:r,y:a+l}).y},{separatorStart:i,separatorEnd:s}}(e,n),{sideStart:h,sideEnd:d}=function(t,e,i){let s,r,{y:a,width:n,height:o,options:l}=t,h=l.callout.start,d=function(t,e){let i=e.side;return"left"===t||"top"===t?-i:i}(e,l.callout);return r="left"===e||"right"===e?{x:(s={x:i.x,y:a+aF(o,h)}).x+d,y:s.y}:{x:(s={x:i.x+aF(n,h),y:i.y}).x,y:s.y+d},{sideStart:s,sideEnd:r}}(e,n,o);(a.margin>0||0===r.borderWidth)&&(t.moveTo(o.x,o.y),t.lineTo(l.x,l.y)),t.moveTo(h.x,h.y),t.lineTo(d.x,d.y);let c=aC({x:i,y:s},e.getCenterPoint(),t7(-e.rotation));t.lineTo(c.x,c.y),t.stroke(),t.restore()}(t,this),a5(t,this,e),a3(t,function({x:t,y:e,width:i,height:s,options:r}){let a=r.borderWidth/2,n=e4(r.padding);return{x:t+n.left+a,y:e+n.top+a,width:i-n.left-n.right-r.borderWidth,height:s-n.top-n.bottom-r.borderWidth}}(this),e),t.restore())}resolveElementProperties(t,e){let i;if(aY(e))i=nt(t,e);else{let{centerX:s,centerY:r}=ne(t,e);i={x:s,y:r}}let s=e4(e.padding),r=aW(i,a2(t.ctx,e),e,s);return{initProperties:aU(t,r,e),pointX:i.x,pointY:i.y,...r,rotation:e.rotation}}}nm.id="labelAnnotation",nm.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},nm.defaultRoutes={borderColor:"color"};let nx=(t,e,i)=>({x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}),nb=(t,e,i)=>nx(e,i,Math.abs((t-e.y)/(i.y-e.y))).x,ny=(t,e,i)=>nx(e,i,Math.abs((t-e.x)/(i.x-e.x))).y,nv=t=>t*t,n_=(t,e,{x:i,y:s,x2:r,y2:a},n)=>"y"===n?{start:Math.min(s,a),end:Math.max(s,a),value:e}:{start:Math.min(i,r),end:Math.max(i,r),value:t},nw=(t,e,i,s)=>(1-s)*(1-s)*t+2*(1-s)*s*e+s*s*i,nM=(t,e,i,s)=>({x:nw(t.x,e.x,i.x,s),y:nw(t.y,e.y,i.y,s)}),nk=(t,e,i,s)=>2*(1-s)*(e-t)+2*s*(i-e),nD=(t,e,i,s)=>-Math.atan2(nk(t.x,e.x,i.x,s),nk(t.y,e.y,i.y,s))+.5*tq;class nC extends sY{inRange(t,e,i,s){let r=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==i&&"y"!==i){let i={mouseX:t,mouseY:e},{path:a,ctx:n}=this;if(a){a0(n,this.options),n.lineWidth+=this.options.hitTolerance;let{chart:r}=this.$context,o=t*r.currentDevicePixelRatio,l=e*r.currentDevicePixelRatio,h=n.isPointInStroke(a,o,l)||nO(this,i,s);return n.restore(),h}return function(t,{mouseX:e,mouseY:i},s=.001,r){let a,n,{x:o,y:l,x2:h,y2:d}=t.getProps(["x","y","x2","y2"],r),c=h-o,u=d-l,f=nv(c)+nv(u),p=0===f?-1:((e-o)*c+(i-l)*u)/f;return p<0?(a=o,n=l):p>1?(a=h,n=d):(a=o+p*c,n=l+p*u),nv(e-a)+nv(i-n)<=s}(this,i,nv(r),s)||nO(this,i,s)}return function(t,{mouseX:e,mouseY:i},s,{hitSize:r,useFinalPosition:a}){return aO(n_(e,i,t.getProps(["x","y","x2","y2"],a),s),r)||nO(t,{mouseX:e,mouseY:i},a,s)}(this,{mouseX:t,mouseY:e},i,{hitSize:r,useFinalPosition:s})}getCenterPoint(t){return aN(this,t)}draw(t){let{x:e,y:i,x2:s,y2:r,cp:a,options:n}=this;if(t.save(),!a0(t,n))return t.restore();a1(t,n);let o=Math.sqrt(Math.pow(s-e,2)+Math.pow(r-i,2));if(n.curve&&a)return function(t,e,i,s){let{x:r,y:a,x2:n,y2:o,options:l}=e,{startOpts:h,endOpts:d,startAdjust:c,endAdjust:u}=nN(e),f={x:r,y:a},p={x:n,y:o},g=nD(f,i,p,0),m=nD(f,i,p,1)-tq,x=nM(f,i,p,c/s),b=nM(f,i,p,1-u/s),y=new Path2D;t.beginPath(),y.moveTo(x.x,x.y),y.quadraticCurveTo(i.x,i.y,b.x,b.y),t.shadowColor=l.borderShadowColor,t.stroke(y),e.path=y,e.ctx=t,nL(t,x,{angle:g,adjust:c},h),nL(t,b,{angle:m,adjust:u},d)}(t,this,a,o),t.restore();let{startOpts:l,endOpts:h,startAdjust:d,endAdjust:c}=nN(this),u=Math.atan2(r-i,s-e);t.translate(e,i),t.rotate(u),t.beginPath(),t.moveTo(0+d,0),t.lineTo(o-c,0),t.shadowColor=n.borderShadowColor,t.stroke(),nj(t,0,d,l),nj(t,o,-c,h),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){let i=function(t,e){let{scales:i,chartArea:s}=t,r=i[e.scaleID],a={x:s.left,y:s.top,x2:s.right,y2:s.bottom};return r?function(t,e,i){let s=a6(t,i.value,NaN),r=a6(t,i.endValue,s);t.isHorizontal()?(e.x=s,e.x2=r):(e.y=s,e.y2=r)}(r,a,e):function(t,e,i){for(let s of Object.keys(a8)){let r=t[a9(t,i,s)];if(r){let{min:t,max:a,start:n,end:o,startProp:l,endProp:h}=a8[s],d=a7(r,{min:i[t],max:i[a],start:r[n],end:r[o]});e[l]=d.start,e[h]=d.end}}}(i,a,e),a}(t,e),{x:s,y:r,x2:a,y2:n}=i,o=function({x:t,y:e,x2:i,y2:s},{top:r,right:a,bottom:n,left:o}){return!(t<o&&i<o||t>a&&i>a||e<r&&s<r||e>n&&s>n)}(i,t.chartArea),l=o?function(t,e,i){let{x:s,y:r}=nP(t,e,i),{x:a,y:n}=nP(e,t,i);return{x:s,y:r,x2:a,y2:n,width:Math.abs(a-s),height:Math.abs(n-r)}}({x:s,y:r},{x:a,y:n},t.chartArea):{x:s,y:r,x2:a,y2:n,width:Math.abs(a-s),height:Math.abs(n-r)};if(l.centerX=(a+s)/2,l.centerY=(n+r)/2,l.initProperties=aU(t,l,e),e.curve){let t={x:l.x,y:l.y},i={x:l.x2,y:l.y2};l.cp=function(t,e,i){let{x:s,y:r,x2:a,y2:n,centerX:o,centerY:l}=t,h=Math.atan2(n-r,a-s),d=aB(e.controlPoint,0);return aC({x:o+aF(i,d.x,!1),y:l+aF(i,d.y,!1)},{x:o,y:l},h)}(l,e,es(t,i))}let h=function(t,e,i){let s=i.borderWidth,r=e4(i.padding),a=a2(t.ctx,i);return function(t,e,i,s){let{width:r,height:a,padding:n}=i,{xAdjust:o,yAdjust:l}=e,h={x:t.x,y:t.y},d={x:t.x2,y:t.y2},c="auto"===e.rotation?function(t){let{x:e,y:i,x2:s,y2:r}=t,a=Math.atan2(r-i,s-e);return a>tq/2?a-tq:a<-(tq/2)?a+tq:a}(t):t7(e.rotation),u=function(t,e,i){let s=Math.cos(i),r=Math.sin(i);return{w:Math.abs(t*s)+Math.abs(e*r),h:Math.abs(t*r)+Math.abs(e*s)}}(r,a,c),f=function(t,e,i,s){let r,a=function(t,e){let{x:i,x2:s,y:r,y2:a}=t,n=Math.min(r,a)-e.top,o=Math.min(i,s)-e.left,l=e.bottom-Math.max(r,a),h=e.right-Math.max(i,s);return{x:Math.min(o,h),y:Math.min(n,l),dx:o<=h?1:-1,dy:n<=l?1:-1}}(t,s);return"start"===e.position?nE({w:t.x2-t.x,h:t.y2-t.y},i,e,a):"end"===e.position?1-nE({w:t.x-t.x2,h:t.y-t.y2},i,e,a):az(1,e.position)}(t,e,{labelSize:u,padding:n},s),p=t.cp?nM(h,t.cp,d,f):nx(h,d,f),g={size:u.w,min:s.left,max:s.right,padding:n.left},m={size:u.h,min:s.top,max:s.bottom,padding:n.top},x=nT(p.x,g)+o,b=nT(p.y,m)+l;return{x:x-r/2,y:b-a/2,x2:x+r/2,y2:b+a/2,centerX:x,centerY:b,pointX:p.x,pointY:p.y,width:r,height:a,rotation:et(c)}}(e,i,{width:a.width+r.width+s,height:a.height+r.height+s,padding:r},t.chartArea)}(t,l,e.label);return h._visible=o,l.elements=[{type:"label",optionScope:"label",properties:h,initProperties:l.initProperties}],l}}nC.id="lineAnnotation";let nS={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};function nP({x:t,y:e},i,{top:s,right:r,bottom:a,left:n}){return t<n&&(e=ny(n,{x:t,y:e},i),t=n),t>r&&(e=ny(r,{x:t,y:e},i),t=r),e<s&&(t=nb(s,{x:t,y:e},i),e=s),e>a&&(t=nb(a,{x:t,y:e},i),e=a),{x:t,y:e}}function nO(t,{mouseX:e,mouseY:i},s,r){let a=t.label;return a.options.display&&a.inRange(e,i,r,s)}function nE(t,e,i,s){let{labelSize:r,padding:a}=e,n=t.w*s.dx,o=t.h*s.dy;return aP(Math.max(n>0&&(r.w/2+a.left-s.x)/n,o>0&&(r.h/2+a.top-s.y)/o),0,.25)}function nT(t,e){let{size:i,min:s,max:r,padding:a}=e,n=i/2;return i>r-s?(r+s)/2:(s>=t-a-n&&(t=s+a+n),r<=t+a+n&&(t=r-a-n),t)}function nN(t){let e=t.options,i=e.arrowHeads&&e.arrowHeads.start,s=e.arrowHeads&&e.arrowHeads.end;return{startOpts:i,endOpts:s,startAdjust:nA(t,i),endAdjust:nA(t,s)}}function nA(t,e){if(!e||!e.display)return 0;let{length:i,width:s}=e,r=t.options.borderWidth/2;return Math.abs(nb(0,{x:i,y:s+r},{x:0,y:r}))}function nj(t,e,i,s){if(!s||!s.display)return;let{length:r,width:a,fill:n,backgroundColor:o,borderColor:l}=s,h=Math.abs(e-r)+i;t.beginPath(),a1(t,s),a0(t,s),t.moveTo(h,-a),t.lineTo(e+i,0),t.lineTo(h,a),!0===n?(t.fillStyle=o||l,t.closePath(),t.fill(),t.shadowColor="transparent"):t.shadowColor=s.borderShadowColor,t.stroke()}function nL(t,{x:e,y:i},{angle:s,adjust:r},a){a&&a.display&&(t.save(),t.translate(e,i),t.rotate(s),nj(t,0,-r,a),t.restore())}nC.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},nS),fill:!1,length:12,start:Object.assign({},nS),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,hitTolerance:0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},nm.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},nC.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}},nC.defaultRoutes={borderColor:"color"};class nI extends sY{inRange(t,e,i,s){let r=this.options.rotation,a=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==i&&"y"!==i)return function(t,e,i,s){let{width:r,height:a,centerX:n,centerY:o}=e,l=r/2,h=a/2;if(l<=0||h<=0)return!1;let d=t7(i||0),c=Math.cos(d),u=Math.sin(d);return Math.pow(c*(t.x-n)+u*(t.y-o),2)/Math.pow(l+s,2)+Math.pow(u*(t.x-n)-c*(t.y-o),2)/Math.pow(h+s,2)<=1.0001}({x:t,y:e},this.getProps(["width","height","centerX","centerY"],s),r,a);let{x:n,y:o,x2:l,y2:h}=this.getProps(["x","y","x2","y2"],s),d="y"===i?{start:o,end:h}:{start:n,end:l},c=aC({x:t,y:e},this.getCenterPoint(s),t7(-r));return c[i]>=d.start-a-.001&&c[i]<=d.end+a+.001}getCenterPoint(t){return aN(this,t)}draw(t){let{width:e,height:i,centerX:s,centerY:r,options:a}=this;t.save(),aQ(t,this.getCenterPoint(),a.rotation),a1(t,this.options),t.beginPath(),t.fillStyle=a.backgroundColor;let n=a0(t,a);t.ellipse(s,r,i/2,e/2,tq/2,0,2*tq),t.fill(),n&&(t.shadowColor=a.borderShadowColor,t.stroke()),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return ns(t,e)}}nI.id="ellipseAnnotation",nI.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,label:Object.assign({},np.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},nI.defaultRoutes={borderColor:"color",backgroundColor:"color"},nI.descriptors={label:{_fallback:!0}};class nR extends sY{inRange(t,e,i,s){let{x:r,y:a,x2:n,y2:o,width:l}=this.getProps(["x","y","x2","y2","width"],s),h=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==i&&"y"!==i){var d,c,u;return d={x:t,y:e},c=this.getCenterPoint(s),u=l/2,!!d&&!!c&&!(u<=0)&&Math.pow(d.x-c.x,2)+Math.pow(d.y-c.y,2)<=Math.pow(u+h,2)}return aO("y"===i?{start:a,end:o,value:e}:{start:r,end:n,value:t},h)}getCenterPoint(t){return aN(this,t)}draw(t){let e=this.options,i=e.borderWidth;if(e.radius<.1)return;t.save(),t.fillStyle=e.backgroundColor,a1(t,e);let s=a0(t,e);!function(t,e,i,s){let{radius:r,options:a}=e,n=a.pointStyle,o=a.rotation,l=(o||0)*tQ;if(aJ(n)){t.save(),t.translate(i,s),t.rotate(l),t.drawImage(n,-n.width/2,-n.height/2,n.width,n.height),t.restore();return}aG(r)||function(t,{x:e,y:i,radius:s,rotation:r,style:a,rad:n}){let o,l,h,d;switch(t.beginPath(),a){default:t.arc(e,i,s,0,tG),t.closePath();break;case"triangle":t.moveTo(e+Math.sin(n)*s,i-Math.cos(n)*s),n+=t2,t.lineTo(e+Math.sin(n)*s,i-Math.cos(n)*s),n+=t2,t.lineTo(e+Math.sin(n)*s,i-Math.cos(n)*s),t.closePath();break;case"rectRounded":d=.516*s,o=Math.cos(n+t1)*(h=s-d),l=Math.sin(n+t1)*h,t.arc(e-o,i-l,d,n-tq,n-t0),t.arc(e+l,i-o,d,n-t0,n),t.arc(e+o,i+l,d,n,n+t0),t.arc(e-l,i+o,d,n+t0,n+tq),t.closePath();break;case"rect":if(!r){h=Math.SQRT1_2*s,t.rect(e-h,i-h,2*h,2*h);break}n+=t1;case"rectRot":o=Math.cos(n)*s,l=Math.sin(n)*s,t.moveTo(e-o,i-l),t.lineTo(e+l,i-o),t.lineTo(e+o,i+l),t.lineTo(e-l,i+o),t.closePath();break;case"crossRot":n+=t1;case"cross":o=Math.cos(n)*s,l=Math.sin(n)*s,t.moveTo(e-o,i-l),t.lineTo(e+o,i+l),t.moveTo(e+l,i-o),t.lineTo(e-l,i+o);break;case"star":o=Math.cos(n)*s,l=Math.sin(n)*s,t.moveTo(e-o,i-l),t.lineTo(e+o,i+l),t.moveTo(e+l,i-o),t.lineTo(e-l,i+o),n+=t1,o=Math.cos(n)*s,l=Math.sin(n)*s,t.moveTo(e-o,i-l),t.lineTo(e+o,i+l),t.moveTo(e+l,i-o),t.lineTo(e-l,i+o);break;case"line":o=Math.cos(n)*s,l=Math.sin(n)*s,t.moveTo(e-o,i-l),t.lineTo(e+o,i+l);break;case"dash":t.moveTo(e,i),t.lineTo(e+Math.cos(n)*s,i+Math.sin(n)*s)}t.fill()}(t,{x:i,y:s,radius:r,rotation:o,style:n,rad:l})}(t,this,this.centerX,this.centerY),s&&!aJ(e.pointStyle)&&(t.shadowColor=e.borderShadowColor,t.stroke()),t.restore(),e.borderWidth=i}resolveElementProperties(t,e){let i=ni(t,e);return i.initProperties=aU(t,i,e),i}}nR.id="pointAnnotation",nR.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},nR.defaultRoutes={borderColor:"color",backgroundColor:"color"};class nz extends sY{inRange(t,e,i,s){if("x"!==i&&"y"!==i)return this.options.radius>=.1&&this.elements.length>1&&function(t,e,i,s){let r=!1,a=t[t.length-1].getProps(["bX","bY"],s);for(let n of t){let t=n.getProps(["bX","bY"],s);t.bY>i!=a.bY>i&&e<(a.bX-t.bX)*(i-t.bY)/(a.bY-t.bY)+t.bX&&(r=!r),a=t}return r}(this.elements,t,e,s);let r=aC({x:t,y:e},this.getCenterPoint(s),t7(-this.options.rotation)),a=this.elements.map(t=>"y"===i?t.bY:t.bX),n=Math.min(...a),o=Math.max(...a);return r[i]>=n&&r[i]<=o}getCenterPoint(t){return aN(this,t)}draw(t){let{elements:e,options:i}=this;t.save(),t.beginPath(),t.fillStyle=i.backgroundColor,a1(t,i);let s=a0(t,i),r=!0;for(let i of e)r?(t.moveTo(i.x,i.y),r=!1):t.lineTo(i.x,i.y);t.closePath(),t.fill(),s&&(t.shadowColor=i.borderShadowColor,t.stroke()),t.restore()}resolveElementProperties(t,e){let i=ni(t,e),{sides:s,rotation:r}=e,a=[],n=2*tq/s,o=r*tQ;for(let r=0;r<s;r++,o+=n){let s=function({centerX:t,centerY:e},{radius:i,borderWidth:s,hitTolerance:r},a){let n=(s+r)/2,o=Math.sin(a),l=Math.cos(a),h={x:t+o*i,y:e-l*i};return{type:"point",optionScope:"point",properties:{x:h.x,y:h.y,centerX:h.x,centerY:h.y,bX:t+o*(i+n),bY:e-l*(i+n)}}}(i,e,o);s.initProperties=aU(t,i,e),a.push(s)}return i.elements=a,i}}nz.id="polygonAnnotation",nz.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},nz.defaultRoutes={borderColor:"color",backgroundColor:"color"};let nF={box:np,doughnutLabel:ng,ellipse:nI,label:nm,line:nC,point:nR,polygon:nz};Object.keys(nF).forEach(t=>{eW.describe(`elements.${nF[t].id}`,{_fallback:"plugins.annotation.common"})});let nW={update:Object.assign},nB=no.concat(nd),nV=(t,e)=>tP(e)?nZ(t,e):t,nH=t=>"color"===t||"font"===t;function nY(t="line"){return nF[t]?t:(console.warn(`Unknown annotation type: '${t}', defaulting to 'line'`),"line")}function n$(t,e,i,s){let r=nF[nY(i)],a=t[e];return a&&a instanceof r||Object.assign(a=t[e]=new r,s),a}function nU(t){let e=nF[nY(t.type)],i={};for(let s of(i.id=t.id,i.type=t.type,i.drawTime=t.drawTime,Object.assign(i,nZ(t,e.defaults),nZ(t,e.defaultRoutes)),nB))i[s]=t[s];return i}function nZ(t,e){let i={};for(let s of Object.keys(e)){let r=e[s],a=t[s];nH(s)&&tS(a)?i[s]=a.map(t=>nV(t,r)):i[s]=nV(a,r)}return i}let nX=new Map,nq=t=>"doughnutLabel"!==t.type,nG=no.concat(nd);function nK(t,e,i){let{ctx:s,chartArea:r}=t,a=nX.get(t);for(let t of(i&&eZ(s,r),(function(t,e){let i=[];for(let s of t)if(s.options.drawTime===e&&i.push({element:s,main:!0}),s.elements&&s.elements.length)for(let t of s.elements)t.options.display&&t.options.drawTime===e&&i.push({element:t});return i})(a.visibleElements,e).sort((t,e)=>t.element.options.z-e.element.options.z))){var n=s,o=r,l=a,h=t;let e=h.element;h.main?(nc(l,e,"beforeDraw"),e.draw(n,o),nc(l,e,"afterDraw")):e.draw(n,o)}i&&eX(s)}var nJ=i(98690);let nQ={blue:{primary:"rgb(59, 130, 246)",background:"rgba(59, 130, 246, 0.5)"},green:{primary:"rgb(16, 185, 129)",background:"rgba(16, 185, 129, 0.5)"},red:{primary:"rgb(239, 68, 68)",background:"rgba(239, 68, 68, 0.5)"}};function n0(t,e=1){return`${t.toFixed(e)}%`}var n1=i(13668),n2=i(43649);function n5({data:t=[],isLoading:e=!1,error:i=null,title:s="Uso de API",apiCallsLimit:r,storageLimit:a}){let[n,o]=(0,p.useState)([]),l={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{mode:"index",intersect:!1,callbacks:{title:t=>`Fecha: ${t[0].label}`,label:t=>{let e=t.dataset.label||"";return e&&(e+=": "),null!==t.parsed.y&&(e.includes("Almacenamiento")?e+=(0,n1.z3)(1024*t.parsed.y*1024):e+=(0,n1.ZV)(t.parsed.y)),e},footer:t=>{let e=t[0].dataset.label||"";if(e.includes("Llamadas")&&r){let e=Math.min(t[0].parsed.y/r*100,100);return`${n0(e)} del l\xedmite (${(0,n1.ZV)(r)})`}if(e.includes("Almacenamiento")&&a){let e=a/1048576,i=Math.min(t[0].parsed.y/e*100,100);return`${n0(i)} del l\xedmite (${(0,n1.z3)(a)})`}return""}}}},scales:{y:{beginAtZero:!0,ticks:{font:{size:11},callback:function(t){return(0,n1.ZV)(t)}}}}};return(n.map(t=>t.date.slice(5)),n.map(t=>t.apiCalls),nQ.blue.primary,nQ.blue.background,n.map(t=>t.date.slice(5)),n.map(t=>t.storage/1048576),nQ.green.primary,nQ.green.background,{...l,plugins:{...l.plugins,title:{display:!0,text:"Llamadas a la API por d\xeda",font:{size:13,weight:"normal"},padding:{top:10,bottom:10}},annotation:r?{annotations:{limitLine:{type:"line",yMin:r,yMax:r,borderColor:nQ.red.primary,borderWidth:2,borderDash:[6,6],label:{display:!0,content:`L\xedmite: ${(0,n1.ZV)(r)}`,position:"end",backgroundColor:"rgba(239, 68, 68, 0.7)",font:{size:11}}}}}:void 0}},{...l,plugins:{...l.plugins,title:{display:!0,text:"Almacenamiento utilizado (MB)",font:{size:13,weight:"normal"},padding:{top:10,bottom:10}},annotation:a?{annotations:{limitLine:{type:"line",yMin:a/1048576,yMax:a/1048576,borderColor:nQ.red.primary,borderWidth:2,borderDash:[6,6],label:{display:!0,content:`L\xedmite: ${(0,n1.z3)(a)}`,position:"end",backgroundColor:"rgba(239, 68, 68, 0.7)",font:{size:11}}}}}:void 0}},e)?(0,f.jsxs)(E.Zp,{className:"transition-all duration-300 hover:shadow-md",children:[(0,f.jsxs)(E.aR,{children:[(0,f.jsx)(E.ZB,{children:(0,f.jsx)(T.E,{className:"h-6 w-1/3"})}),(0,f.jsx)(E.BT,{children:(0,f.jsx)(T.E,{className:"h-4 w-1/2"})})]}),(0,f.jsx)(E.Wu,{className:"h-80",children:(0,f.jsx)(T.E,{className:"h-full w-full rounded-md"})})]}):i?(0,f.jsxs)(E.Zp,{className:"transition-all duration-300 hover:shadow-md border-red-200 dark:border-red-800",children:[(0,f.jsxs)(E.aR,{children:[(0,f.jsxs)(E.ZB,{className:"text-destructive flex items-center gap-2",children:[(0,f.jsx)(n2.A,{className:"h-5 w-5"}),"Error al cargar datos"]}),(0,f.jsx)(E.BT,{className:"text-destructive/70",children:"No se pudieron cargar los datos de uso"})]}),(0,f.jsxs)(E.Wu,{className:"flex items-center justify-center py-8",children:[(0,f.jsx)("p",{className:"text-destructive mb-2",children:"Ocurri\xf3 un error al cargar los datos de uso."}),(0,f.jsx)("p",{className:"text-muted-foreground text-sm",children:"Intenta refrescar la p\xe1gina o contacta al soporte t\xe9cnico."})]})]}):(0,f.jsxs)(E.Zp,{className:"w-full",children:[(0,f.jsx)(E.aR,{children:(0,f.jsxs)("div",{className:"flex items-center justify-between",children:[(0,f.jsxs)("div",{children:[(0,f.jsxs)(E.ZB,{className:"flex items-center gap-2",children:[(0,f.jsx)(B.A,{className:"h-5 w-5"}),s]}),(0,f.jsx)(E.BT,{children:"Monitoreo de uso en el rango seleccionado"})]}),(0,f.jsxs)("div",{className:"flex items-center gap-2",children:[(0,f.jsx)(j.E,{variant:"info",children:"Per\xedodo: \xdaltimos 30 d\xedas"}),n.some(t=>t.apiCalls>0)&&(0,f.jsx)(j.E,{variant:"success",children:"Datos disponibles"})]})]})}),(0,f.jsx)(E.Wu,{children:(0,f.jsx)(nJ.tU,{defaultValue:"chart",className:"w-full",children:(0,f.jsxs)(nJ.j7,{className:"grid w-full grid-cols-2",children:[(0,f.jsx)(nJ.Xi,{value:"chart",className:"transition-all duration-200 data-[state=active]:bg-info/10 data-[state=active]:text-info",children:"Gr\xe1fico"}),(0,f.jsx)(nJ.Xi,{value:"table",className:"transition-all duration-200 data-[state=active]:bg-success/10 data-[state=active]:text-success",children:"Tabla"})]})})})]})}rp.register(at,as,rP,rC,rj,{id:"title",_element:rq,start(t,e,i){let s=new rq({ctx:t.ctx,options:i,chart:t});sS.configure(t,s,i),sS.addBox(t,s),t.titleBlock=s},stop(t){let e=t.titleBlock;sS.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){let s=t.titleBlock;sS.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},{id:"tooltip",_element:r8,positioners:rG,afterInit(t,e,i){i&&(t.tooltip=new r8({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){let e=t.tooltip;if(e&&e._willRender()){let i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){let i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:r3},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},{id:"legend",_element:rZ,start(t,e,i){let s=t.legend=new rZ({ctx:t.ctx,options:i,chart:t});sS.configure(t,s,i),sS.addBox(t,s)},stop(t){sS.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){let s=t.legend;sS.configure(t,s,i),s.options=i},afterUpdate(t){let e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){let s=e.datasetIndex,r=i.chart;r.isDatasetVisible(s)?(r.hide(s),e.hidden=!0):(r.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:r,color:a,useBorderRadius:n,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{let l=t.controller.getStyle(i?0:void 0),h=e4(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:a,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:s||l.pointStyle,rotation:l.rotation,textAlign:r||l.textAlign,borderRadius:n&&(o||l.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}},{id:"annotation",version:"3.1.0",beforeRegister(){!function(t,e,i,s=!0){let r=i.split("."),a=0;for(let e of"4.0".split(".")){let n=r[a++];if(parseInt(e,10)<parseInt(n,10))break;if(aS(n,e))if(!s)return!1;else throw Error(`${t} v${i} is not supported. v4.0 or newer is required.`)}}("chart.js","4.0",rp.version)},afterRegister(){rp.register(nF)},afterUnregister(){rp.unregister(nF)},beforeInit(t){nX.set(t,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(t,e,i){let s=nX.get(t).annotations=[],r=i.annotations;tP(r)?Object.keys(r).forEach(t=>{let e=r[t];tP(e)&&(e.id=t,s.push(e))}):tS(r)&&s.push(...r);var a=s.filter(nq),n=t.scales;for(let t of a){var o=t,l=n;for(let t of["scaleID","xScaleID","yScaleID"]){let e=a9(l,o,t);e&&!l[e]&&function(t,e){if("scaleID"===e)return!0;let i=e.charAt(0);for(let e of["Min","Max","Value"])if(tU(t[i+e]))return!0;return!1}(o,t)&&console.warn(`No scale found with id '${e}' for annotation '${o.id}'`)}}},afterDataLimits(t,e){let i=nX.get(t);!function(t,e,i){let s=function(t,e,i){let s=e.axis,r=e.id,a=s+"ScaleID",n={min:tT(e.min,Number.NEGATIVE_INFINITY),max:tT(e.max,Number.POSITIVE_INFINITY)};for(let o of i)o.scaleID===r?nf(o,e,["value","endValue"],n):a9(t,o,a)===r&&nf(o,e,[s+"Min",s+"Max",s+"Value"],n);return n}(t.scales,e,i),r=nu(e,s,"min","suggestedMin");(r=nu(e,s,"max","suggestedMax")||r)&&tZ(e.handleTickRangeOptions)&&e.handleTickRangeOptions()}(t,e.scale,i.annotations.filter(nq).filter(t=>t.display&&t.adjustScaleRange))},afterUpdate(t,e,i){let s=nX.get(t);s.listened=aZ(i,no,s.listeners),s.moveListened=!1,nn.forEach(t=>{tZ(i[t])&&(s.moveListened=!0)}),s.listened&&s.moveListened||s.annotations.forEach(t=>{!s.listened&&tZ(t.click)&&(s.listened=!0),s.moveListened||nn.forEach(e=>{tZ(t[e])&&(s.listened=!0,s.moveListened=!0)})}),function(t,e,i,s){var r,a,n,o,l,h,d,c;let u=(r=t,a=i.animations,"reset"===(n=s)||"none"===n||"resize"===n?nW:new iX(r,a)),f=e.annotations,p=function(t,e){let i=e.length,s=t.length;return s<i?t.splice(s,0,...Array(i-s)):s>i&&t.splice(i,s-i),t}(e.elements,f);for(let e=0;e<f.length;e++){let i=f[e],s=n$(p,e,i.type),r=i.setContext((o=t,l=s,h=p,d=i,l.$context||(l.$context=Object.assign(Object.create(o.getContext()),{element:l,get elements(){return h.filter(t=>t&&t.options)},id:d.id,type:"annotation"})))),a=s.resolveElementProperties(t,r);a.skip=isNaN((c=a).x)||isNaN(c.y),"elements"in a&&(function(t,e,i,s){let r=t.elements||(t.elements=[]);r.length=e.length;for(let t=0;t<e.length;t++){let a=e[t],n=a.properties,o=n$(r,t,a.type,a.initProperties);n.options=nU(i[a.optionScope].override(a)),s.update(o,n)}}(s,a.elements,r,u),delete a.elements),tU(s.x)||Object.assign(s,a),Object.assign(s,a.initProperties),a.options=nU(r),u.update(s,a)}}(t,s,i,e.mode),s.visibleElements=s.elements.filter(t=>!t.skip&&t.options.display);let r=s.visibleElements;s.hooked=aZ(i,nd,s.hooks),s.hooked||r.forEach(t=>{s.hooked||nd.forEach(e=>{tZ(t.options[e])&&(s.hooked=!0)})})},beforeDatasetsDraw(t,e,i){nK(t,"beforeDatasetsDraw",i.clip)},afterDatasetsDraw(t,e,i){nK(t,"afterDatasetsDraw",i.clip)},beforeDatasetDraw(t,e,i){nK(t,e.index,i.clip)},beforeDraw(t,e,i){nK(t,"beforeDraw",i.clip)},afterDraw(t,e,i){nK(t,"afterDraw",i.clip)},beforeEvent(t,e,i){(function(t,e,i){if(t.listened)switch(e.type){case"mousemove":case"mouseout":let s;var r=t,a=e,n=i;if(!r.moveListened)return;s="mousemove"===a.type?ak(r.visibleElements,a,n.interaction):[];let o=r.hovered;r.hovered=s;let l={state:r,event:a},h=nl(l,"leave",o,s);return nl(l,"enter",s,o)||h;case"click":let d;var c=t,u=e,f=i;let p=c.listeners;for(let t of ak(c.visibleElements,u,f.interaction))d=nh(t.options.click||p.click,t,u)||d;return d}})(nX.get(t),e.event,i)&&(e.changed=!0)},afterDestroy(t){nX.delete(t)},getAnnotations(t){let e=nX.get(t);return e?e.elements:[]},_getAnnotationElementsAtEventForMode:(t,e,i)=>ak(t,e,i),defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"},colors:{properties:["backgroundColor","borderColor"],type:"color"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:t=>!nG.includes(t)&&"init"!==t,annotations:{_allKeys:!1,_fallback:(t,e)=>`elements.${nF[nY(e.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:nH,_fallback:!0},_indexable:nH}},additionalOptionScopes:[""]});let n3=(0,i(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var n4=i(14952);Symbol.for("constructDateFrom");let n8={},n6={};function n9(t,e){try{let i=(n8[t]||=new Intl.DateTimeFormat("en-GB",{timeZone:t,hour:"numeric",timeZoneName:"longOffset"}).format)(e).split("GMT")[1]||"";if(i in n6)return n6[i];return ot(i,i.split(":"))}catch{if(t in n6)return n6[t];let e=t?.match(n7);if(e)return ot(t,e.slice(1));return NaN}}let n7=/([+-]\d\d):?(\d\d)?/;function ot(t,e){let i=+e[0],s=+(e[1]||0);return n6[t]=i>0?60*i+s:60*i-s}class oe extends Date{constructor(...t){super(),t.length>1&&"string"==typeof t[t.length-1]&&(this.timeZone=t.pop()),this.internal=new Date,isNaN(n9(this.timeZone,this))?this.setTime(NaN):t.length?"number"==typeof t[0]&&(1===t.length||2===t.length&&"number"!=typeof t[1])?this.setTime(t[0]):"string"==typeof t[0]?this.setTime(+new Date(t[0])):t[0]instanceof Date?this.setTime(+t[0]):(this.setTime(+new Date(...t)),or(this,NaN),os(this)):this.setTime(Date.now())}static tz(t,...e){return e.length?new oe(...e,t):new oe(Date.now(),t)}withTimeZone(t){return new oe(+this,t)}getTimezoneOffset(){return-n9(this.timeZone,this)}setTime(t){return Date.prototype.setTime.apply(this,arguments),os(this),+this}[Symbol.for("constructDateFrom")](t){return new oe(+new Date(t),this.timeZone)}}let oi=/^(get|set)(?!UTC)/;function os(t){t.internal.setTime(+t),t.internal.setUTCMinutes(t.internal.getUTCMinutes()-t.getTimezoneOffset())}function or(t){let e=n9(t.timeZone,t),i=new Date(+t);i.setUTCHours(i.getUTCHours()-1);let s=-new Date(+t).getTimezoneOffset(),r=s- -new Date(+i).getTimezoneOffset(),a=Date.prototype.getHours.apply(t)!==t.internal.getUTCHours();r&&a&&t.internal.setUTCMinutes(t.internal.getUTCMinutes()+r);let n=s-e;n&&Date.prototype.setUTCMinutes.call(t,Date.prototype.getUTCMinutes.call(t)+n);let o=n9(t.timeZone,t),l=-new Date(+t).getTimezoneOffset()-o-n;if(o!==e&&l){Date.prototype.setUTCMinutes.call(t,Date.prototype.getUTCMinutes.call(t)+l);let e=o-n9(t.timeZone,t);e&&(t.internal.setUTCMinutes(t.internal.getUTCMinutes()+e),Date.prototype.setUTCMinutes.call(t,Date.prototype.getUTCMinutes.call(t)+e))}}Object.getOwnPropertyNames(Date.prototype).forEach(t=>{if(!oi.test(t))return;let e=t.replace(oi,"$1UTC");oe.prototype[e]&&(t.startsWith("get")?oe.prototype[t]=function(){return this.internal[e]()}:(oe.prototype[t]=function(){var t;return Date.prototype[e].apply(this.internal,arguments),t=this,Date.prototype.setFullYear.call(t,t.internal.getUTCFullYear(),t.internal.getUTCMonth(),t.internal.getUTCDate()),Date.prototype.setHours.call(t,t.internal.getUTCHours(),t.internal.getUTCMinutes(),t.internal.getUTCSeconds(),t.internal.getUTCMilliseconds()),or(t),+this},oe.prototype[e]=function(){return Date.prototype[e].apply(this,arguments),os(this),+this}))});class oa extends oe{static tz(t,...e){return e.length?new oa(...e,t):new oa(Date.now(),t)}toISOString(){let[t,e,i]=this.tzComponents(),s=`${t}${e}:${i}`;return this.internal.toISOString().slice(0,-1)+s}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[t,e,i,s]=this.internal.toUTCString().split(" ");return`${t?.slice(0,-1)} ${i} ${e} ${s}`}toTimeString(){var t,e;let i=this.internal.toUTCString().split(" ")[4],[s,r,a]=this.tzComponents();return`${i} GMT${s}${r}${a} (${t=this.timeZone,e=this,new Intl.DateTimeFormat("en-GB",{timeZone:t,timeZoneName:"long"}).format(e).slice(12)})`}toLocaleString(t,e){return Date.prototype.toLocaleString.call(this,t,{...e,timeZone:e?.timeZone||this.timeZone})}toLocaleDateString(t,e){return Date.prototype.toLocaleDateString.call(this,t,{...e,timeZone:e?.timeZone||this.timeZone})}toLocaleTimeString(t,e){return Date.prototype.toLocaleTimeString.call(this,t,{...e,timeZone:e?.timeZone||this.timeZone})}tzComponents(){let t=this.getTimezoneOffset(),e=String(Math.floor(Math.abs(t)/60)).padStart(2,"0"),i=String(Math.abs(t)%60).padStart(2,"0");return[t>0?"-":"+",e,i]}withTimeZone(t){return new oa(+this,t)}[Symbol.for("constructDateFrom")](t){return new oa(+new Date(t),this.timeZone)}}!function(t){t.Root="root",t.Chevron="chevron",t.Day="day",t.DayButton="day_button",t.CaptionLabel="caption_label",t.Dropdowns="dropdowns",t.Dropdown="dropdown",t.DropdownRoot="dropdown_root",t.Footer="footer",t.MonthGrid="month_grid",t.MonthCaption="month_caption",t.MonthsDropdown="months_dropdown",t.Month="month",t.Months="months",t.Nav="nav",t.NextMonthButton="button_next",t.PreviousMonthButton="button_previous",t.Week="week",t.Weeks="weeks",t.Weekday="weekday",t.Weekdays="weekdays",t.WeekNumber="week_number",t.WeekNumberHeader="week_number_header",t.YearsDropdown="years_dropdown"}(r||(r={})),function(t){t.disabled="disabled",t.hidden="hidden",t.outside="outside",t.focused="focused",t.today="today"}(a||(a={})),function(t){t.range_end="range_end",t.range_middle="range_middle",t.range_start="range_start",t.selected="selected"}(n||(n={})),function(t){t.weeks_before_enter="weeks_before_enter",t.weeks_before_exit="weeks_before_exit",t.weeks_after_enter="weeks_after_enter",t.weeks_after_exit="weeks_after_exit",t.caption_after_enter="caption_after_enter",t.caption_after_exit="caption_after_exit",t.caption_before_enter="caption_before_enter",t.caption_before_exit="caption_before_exit"}(o||(o={}));var on=i(29175);function oo(t,e,i){let s=(0,m.a)(t,i?.in);if(isNaN(e))return(0,g.w)(i?.in||t,NaN);if(!e)return s;let r=s.getDate(),a=(0,g.w)(i?.in||t,s.getTime());return(a.setMonth(s.getMonth()+e+1,0),r>=a.getDate())?a:(s.setFullYear(a.getFullYear(),a.getMonth(),r),s)}var ol=i(48750),oh=i(29789),od=i(78872);function oc(t,e){let i=(0,od.q)(),s=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??i.weekStartsOn??i.locale?.options?.weekStartsOn??0,r=(0,m.a)(t,e?.in),a=r.getDay();return r.setDate(r.getDate()+((a<s?-7:0)+6-(a-s))),r.setHours(23,59,59,999),r}var ou=i(38832),of=i(46495);function op(t,e){return+(0,m.a)(t)>+(0,m.a)(e)}var og=i(91522),om=i(30319),ox=i(64916),ob=i(51877),oy=i(27272);function ov(t,e){let i=e.startOfMonth(t),s=i.getDay();return 1===s?i:0===s?e.addDays(i,-6):e.addDays(i,-1*(s-1))}class o_{constructor(t,e){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?oa.tz(this.options.timeZone):new this.Date,this.newDate=(t,e,i)=>this.overrides?.newDate?this.overrides.newDate(t,e,i):this.options.timeZone?new oa(t,e,i,this.options.timeZone):new Date(t,e,i),this.addDays=(t,e)=>this.overrides?.addDays?this.overrides.addDays(t,e):x(t,e),this.addMonths=(t,e)=>this.overrides?.addMonths?this.overrides.addMonths(t,e):oo(t,e),this.addWeeks=(t,e)=>this.overrides?.addWeeks?this.overrides.addWeeks(t,e):x(t,7*e,void 0),this.addYears=(t,e)=>this.overrides?.addYears?this.overrides.addYears(t,e):oo(t,12*e,void 0),this.differenceInCalendarDays=(t,e)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(t,e):(0,ol.m)(t,e),this.differenceInCalendarMonths=(t,e)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(t,e):function(t,e,i){let[s,r]=(0,oh.x)(void 0,t,e);return 12*(s.getFullYear()-r.getFullYear())+(s.getMonth()-r.getMonth())}(t,e),this.eachMonthOfInterval=t=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(t):function(t,e){let{start:i,end:s}=function(t,e){let[i,s]=(0,oh.x)(t,e.start,e.end);return{start:i,end:s}}(void 0,t),r=+i>+s,a=r?+i:+s,n=r?s:i;n.setHours(0,0,0,0),n.setDate(1);let o=(void 0)??1;if(!o)return[];o<0&&(o=-o,r=!r);let l=[];for(;+n<=a;)l.push((0,g.w)(i,n)),n.setMonth(n.getMonth()+o);return r?l.reverse():l}(t),this.endOfBroadcastWeek=t=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(t):function(t,e){let i=ov(t,e),s=function(t,e){let i=e.startOfMonth(t),s=i.getDay()>0?i.getDay():7,r=e.addDays(t,-s+1),a=e.addDays(r,34);return e.getMonth(t)===e.getMonth(a)?5:4}(t,e);return e.addDays(i,7*s-1)}(t,this),this.endOfISOWeek=t=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(t):oc(t,{...void 0,weekStartsOn:1}),this.endOfMonth=t=>this.overrides?.endOfMonth?this.overrides.endOfMonth(t):function(t,e){let i=(0,m.a)(t,void 0),s=i.getMonth();return i.setFullYear(i.getFullYear(),s+1,0),i.setHours(23,59,59,999),i}(t),this.endOfWeek=(t,e)=>this.overrides?.endOfWeek?this.overrides.endOfWeek(t,e):oc(t,this.options),this.endOfYear=t=>this.overrides?.endOfYear?this.overrides.endOfYear(t):function(t,e){let i=(0,m.a)(t,void 0),s=i.getFullYear();return i.setFullYear(s+1,0,0),i.setHours(23,59,59,999),i}(t),this.format=(t,e,i)=>{let s=this.overrides?.format?this.overrides.format(t,e,this.options):(0,b.GP)(t,e,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(s):s},this.getISOWeek=t=>this.overrides?.getISOWeek?this.overrides.getISOWeek(t):(0,ou.s)(t),this.getMonth=(t,e)=>{var i;return this.overrides?.getMonth?this.overrides.getMonth(t,this.options):(i=this.options,(0,m.a)(t,i?.in).getMonth())},this.getYear=(t,e)=>{var i;return this.overrides?.getYear?this.overrides.getYear(t,this.options):(i=this.options,(0,m.a)(t,i?.in).getFullYear())},this.getWeek=(t,e)=>this.overrides?.getWeek?this.overrides.getWeek(t,this.options):(0,of.N)(t,this.options),this.isAfter=(t,e)=>this.overrides?.isAfter?this.overrides.isAfter(t,e):op(t,e),this.isBefore=(t,e)=>this.overrides?.isBefore?this.overrides.isBefore(t,e):+(0,m.a)(t)<+(0,m.a)(e),this.isDate=t=>this.overrides?.isDate?this.overrides.isDate(t):(0,og.$)(t),this.isSameDay=(t,e)=>this.overrides?.isSameDay?this.overrides.isSameDay(t,e):function(t,e,i){let[s,r]=(0,oh.x)(void 0,t,e);return+(0,om.o)(s)==+(0,om.o)(r)}(t,e),this.isSameMonth=(t,e)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(t,e):function(t,e,i){let[s,r]=(0,oh.x)(void 0,t,e);return s.getFullYear()===r.getFullYear()&&s.getMonth()===r.getMonth()}(t,e),this.isSameYear=(t,e)=>this.overrides?.isSameYear?this.overrides.isSameYear(t,e):function(t,e,i){let[s,r]=(0,oh.x)(void 0,t,e);return s.getFullYear()===r.getFullYear()}(t,e),this.max=t=>this.overrides?.max?this.overrides.max(t):function(t,e){let i,s;return t.forEach(t=>{s||"object"!=typeof t||(s=g.w.bind(null,t));let e=(0,m.a)(t,s);(!i||i<e||isNaN(+e))&&(i=e)}),(0,g.w)(s,i||NaN)}(t),this.min=t=>this.overrides?.min?this.overrides.min(t):function(t,e){let i,s;return t.forEach(t=>{s||"object"!=typeof t||(s=g.w.bind(null,t));let e=(0,m.a)(t,s);(!i||i>e||isNaN(+e))&&(i=e)}),(0,g.w)(s,i||NaN)}(t),this.setMonth=(t,e)=>this.overrides?.setMonth?this.overrides.setMonth(t,e):function(t,e,i){let s=(0,m.a)(t,void 0),r=s.getFullYear(),a=s.getDate(),n=(0,g.w)(t,0);n.setFullYear(r,e,15),n.setHours(0,0,0,0);let o=function(t,e){let i=(0,m.a)(t,void 0),s=i.getFullYear(),r=i.getMonth(),a=(0,g.w)(i,0);return a.setFullYear(s,r+1,0),a.setHours(0,0,0,0),a.getDate()}(n);return s.setMonth(e,Math.min(a,o)),s}(t,e),this.setYear=(t,e)=>this.overrides?.setYear?this.overrides.setYear(t,e):function(t,e,i){let s=(0,m.a)(t,void 0);return isNaN(+s)?(0,g.w)(t,NaN):(s.setFullYear(e),s)}(t,e),this.startOfBroadcastWeek=(t,e)=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(t,this):ov(t,this),this.startOfDay=t=>this.overrides?.startOfDay?this.overrides.startOfDay(t):(0,om.o)(t),this.startOfISOWeek=t=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(t):(0,ox.b)(t),this.startOfMonth=t=>this.overrides?.startOfMonth?this.overrides.startOfMonth(t):function(t,e){let i=(0,m.a)(t,void 0);return i.setDate(1),i.setHours(0,0,0,0),i}(t),this.startOfWeek=(t,e)=>this.overrides?.startOfWeek?this.overrides.startOfWeek(t,this.options):(0,ob.k)(t,this.options),this.startOfYear=t=>this.overrides?.startOfYear?this.overrides.startOfYear(t):(0,oy.D)(t),this.options={locale:on.c,...t},this.overrides=e}getDigitMap(){let{numerals:t="latn"}=this.options,e=new Intl.NumberFormat("en-US",{numberingSystem:t}),i={};for(let t=0;t<10;t++)i[t.toString()]=e.format(t);return i}replaceDigits(t){let e=this.getDigitMap();return t.replace(/\d/g,t=>e[t]||t)}formatNumber(t){return this.replaceDigits(t.toString())}}let ow=new o_;function oM(t,e,i=!1,s=ow){let{from:r,to:a}=t,{differenceInCalendarDays:n,isSameDay:o}=s;return r&&a?(0>n(a,r)&&([r,a]=[a,r]),n(e,r)>=+!!i&&n(a,e)>=+!!i):!i&&a?o(a,e):!i&&!!r&&o(r,e)}function ok(t){return!!(t&&"object"==typeof t&&"before"in t&&"after"in t)}function oD(t){return!!(t&&"object"==typeof t&&"from"in t)}function oC(t){return!!(t&&"object"==typeof t&&"after"in t)}function oS(t){return!!(t&&"object"==typeof t&&"before"in t)}function oP(t){return!!(t&&"object"==typeof t&&"dayOfWeek"in t)}function oO(t,e){return Array.isArray(t)&&t.every(e.isDate)}function oE(t,e,i=ow){let s=Array.isArray(e)?e:[e],{isSameDay:r,differenceInCalendarDays:a,isAfter:n}=i;return s.some(e=>{if("boolean"==typeof e)return e;if(i.isDate(e))return r(t,e);if(oO(e,i))return e.includes(t);if(oD(e))return oM(e,t,!1,i);if(oP(e))return Array.isArray(e.dayOfWeek)?e.dayOfWeek.includes(t.getDay()):e.dayOfWeek===t.getDay();if(ok(e)){let i=a(e.before,t),s=a(e.after,t),r=i>0,o=s<0;return n(e.before,e.after)?o&&r:r||o}return oC(e)?a(t,e.after)>0:oS(e)?a(e.before,t)>0:"function"==typeof e&&e(t)})}function oT(t){return p.createElement("button",{...t})}function oN(t){return p.createElement("span",{...t})}function oA(t){let{size:e=24,orientation:i="left",className:s}=t;return p.createElement("svg",{className:s,width:e,height:e,viewBox:"0 0 24 24"},"up"===i&&p.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===i&&p.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===i&&p.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===i&&p.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function oj(t){let{day:e,modifiers:i,...s}=t;return p.createElement("td",{...s})}function oL(t){let{day:e,modifiers:i,...s}=t,r=p.useRef(null);return p.useEffect(()=>{i.focused&&r.current?.focus()},[i.focused]),p.createElement("button",{ref:r,...s})}function oI(t){let{options:e,className:i,components:s,classNames:a,...n}=t,o=[a[r.Dropdown],i].join(" "),l=e?.find(({value:t})=>t===n.value);return p.createElement("span",{"data-disabled":n.disabled,className:a[r.DropdownRoot]},p.createElement(s.Select,{className:o,...n},e?.map(({value:t,label:e,disabled:i})=>p.createElement(s.Option,{key:t,value:t,disabled:i},e))),p.createElement("span",{className:a[r.CaptionLabel],"aria-hidden":!0},l?.label,p.createElement(s.Chevron,{orientation:"down",size:18,className:a[r.Chevron]})))}function oR(t){return p.createElement("div",{...t})}function oz(t){return p.createElement("div",{...t})}function oF(t){let{calendarMonth:e,displayIndex:i,...s}=t;return p.createElement("div",{...s},t.children)}function oW(t){let{calendarMonth:e,displayIndex:i,...s}=t;return p.createElement("div",{...s})}function oB(t){return p.createElement("table",{...t})}function oV(t){return p.createElement("div",{...t})}let oH=(0,p.createContext)(void 0);function oY(){let t=(0,p.useContext)(oH);if(void 0===t)throw Error("useDayPicker() must be used within a custom component.");return t}function o$(t){let{components:e}=oY();return p.createElement(e.Dropdown,{...t})}function oU(t){let{onPreviousClick:e,onNextClick:i,previousMonth:s,nextMonth:a,...n}=t,{components:o,classNames:l,labels:{labelPrevious:h,labelNext:d}}=oY(),c=(0,p.useCallback)(t=>{a&&i?.(t)},[a,i]),u=(0,p.useCallback)(t=>{s&&e?.(t)},[s,e]);return p.createElement("nav",{...n},p.createElement(o.PreviousMonthButton,{type:"button",className:l[r.PreviousMonthButton],tabIndex:s?void 0:-1,"aria-disabled":!s||void 0,"aria-label":h(s),onClick:u},p.createElement(o.Chevron,{disabled:!s||void 0,className:l[r.Chevron],orientation:"left"})),p.createElement(o.NextMonthButton,{type:"button",className:l[r.NextMonthButton],tabIndex:a?void 0:-1,"aria-disabled":!a||void 0,"aria-label":d(a),onClick:c},p.createElement(o.Chevron,{disabled:!a||void 0,orientation:"right",className:l[r.Chevron]})))}function oZ(t){let{components:e}=oY();return p.createElement(e.Button,{...t})}function oX(t){return p.createElement("option",{...t})}function oq(t){let{components:e}=oY();return p.createElement(e.Button,{...t})}function oG(t){let{rootRef:e,...i}=t;return p.createElement("div",{...i,ref:e})}function oK(t){return p.createElement("select",{...t})}function oJ(t){let{week:e,...i}=t;return p.createElement("tr",{...i})}function oQ(t){return p.createElement("th",{...t})}function o0(t){return p.createElement("thead",{"aria-hidden":!0},p.createElement("tr",{...t}))}function o1(t){let{week:e,...i}=t;return p.createElement("th",{...i})}function o2(t){return p.createElement("th",{...t})}function o5(t){return p.createElement("tbody",{...t})}function o3(t){let{components:e}=oY();return p.createElement(e.Dropdown,{...t})}function o4(t,e,i){return(i??new o_(e)).format(t,"LLLL y")}let o8=o4;function o6(t,e,i){return(i??new o_(e)).format(t,"d")}function o9(t,e=ow){return e.format(t,"LLLL")}function o7(t,e=ow){return t<10?e.formatNumber(`0${t.toLocaleString()}`):e.formatNumber(`${t.toLocaleString()}`)}function lt(){return""}function le(t,e,i){return(i??new o_(e)).format(t,"cccccc")}function li(t,e=ow){return e.format(t,"yyyy")}let ls=li;function lr(t,e,i){return(i??new o_(e)).format(t,"LLLL y")}let la=lr;function ln(t,e,i,s){let r=(s??new o_(i)).format(t,"PPPP");return e?.today&&(r=`Today, ${r}`),r}function lo(t,e,i,s){let r=(s??new o_(i)).format(t,"PPPP");return e.today&&(r=`Today, ${r}`),e.selected&&(r=`${r}, selected`),r}let ll=lo;function lh(){return""}function ld(t){return"Choose the Month"}function lc(t){return"Go to the Next Month"}function lu(t){return"Go to the Previous Month"}function lf(t,e,i){return(i??new o_(e)).format(t,"cccc")}function lp(t,e){return`Week ${t}`}function lg(t){return"Week Number"}function lm(t){return"Choose the Year"}let lx=t=>t instanceof HTMLElement?t:null,lb=t=>[...t.querySelectorAll("[data-animated-month]")??[]],ly=t=>lx(t.querySelector("[data-animated-month]")),lv=t=>lx(t.querySelector("[data-animated-caption]")),l_=t=>lx(t.querySelector("[data-animated-weeks]")),lw=t=>lx(t.querySelector("[data-animated-nav]")),lM=t=>lx(t.querySelector("[data-animated-weekdays]"));function lk(t,e){let{month:i,defaultMonth:s,today:r=e.today(),numberOfMonths:a=1,endMonth:n,startMonth:o}=t,l=i||s||r,{differenceInCalendarMonths:h,addMonths:d,startOfMonth:c}=e;return n&&0>h(n,l)&&(l=d(n,-1*(a-1))),o&&0>h(l,o)&&(l=o),c(l)}class lD{constructor(t,e,i=ow){this.date=t,this.displayMonth=e,this.outside=!!(e&&!i.isSameMonth(t,e)),this.dateLib=i}isEqualTo(t){return this.dateLib.isSameDay(t.date,this.date)&&this.dateLib.isSameMonth(t.displayMonth,this.displayMonth)}}class lC{constructor(t,e){this.days=e,this.weekNumber=t}}class lS{constructor(t,e){this.date=t,this.weeks=e}}function lP(t,e){let[i,s]=(0,p.useState)(t);return[void 0===e?i:e,s]}function lO(t){return!t[a.disabled]&&!t[a.hidden]&&!t[a.outside]}function lE(t,e,i=ow){return oM(t,e.from,!1,i)||oM(t,e.to,!1,i)||oM(e,t.from,!1,i)||oM(e,t.to,!1,i)}function lT(t){let e=t;e.timeZone&&((e={...t}).today&&(e.today=new oa(e.today,e.timeZone)),e.month&&(e.month=new oa(e.month,e.timeZone)),e.defaultMonth&&(e.defaultMonth=new oa(e.defaultMonth,e.timeZone)),e.startMonth&&(e.startMonth=new oa(e.startMonth,e.timeZone)),e.endMonth&&(e.endMonth=new oa(e.endMonth,e.timeZone)),"single"===e.mode&&e.selected?e.selected=new oa(e.selected,e.timeZone):"multiple"===e.mode&&e.selected?e.selected=e.selected?.map(t=>new oa(t,e.timeZone)):"range"===e.mode&&e.selected&&(e.selected={from:e.selected.from?new oa(e.selected.from,e.timeZone):void 0,to:e.selected.to?new oa(e.selected.to,e.timeZone):void 0}));let{components:i,formatters:s,labels:h,dateLib:f,locale:g,classNames:m}=(0,p.useMemo)(()=>{var t,i;let s={...on.c,...e.locale};return{dateLib:new o_({locale:s,weekStartsOn:e.broadcastCalendar?1:e.weekStartsOn,firstWeekContainsDate:e.firstWeekContainsDate,useAdditionalWeekYearTokens:e.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:e.useAdditionalDayOfYearTokens,timeZone:e.timeZone,numerals:e.numerals},e.dateLib),components:(t=e.components,{...d,...t}),formatters:(i=e.formatters,i?.formatMonthCaption&&!i.formatCaption&&(i.formatCaption=i.formatMonthCaption),i?.formatYearCaption&&!i.formatYearDropdown&&(i.formatYearDropdown=i.formatYearCaption),{...c,...i}),labels:{...u,...e.labels},locale:s,classNames:{...function(){let t={};for(let e in r)t[r[e]]=`rdp-${r[e]}`;for(let e in a)t[a[e]]=`rdp-${a[e]}`;for(let e in n)t[n[e]]=`rdp-${n[e]}`;for(let e in o)t[o[e]]=`rdp-${o[e]}`;return t}(),...e.classNames}}},[e.locale,e.broadcastCalendar,e.weekStartsOn,e.firstWeekContainsDate,e.useAdditionalWeekYearTokens,e.useAdditionalDayOfYearTokens,e.timeZone,e.numerals,e.dateLib,e.components,e.formatters,e.labels,e.classNames]),{captionLayout:x,mode:b,navLayout:y,numberOfMonths:v=1,onDayBlur:_,onDayClick:w,onDayFocus:M,onDayKeyDown:k,onDayMouseEnter:D,onDayMouseLeave:C,onNextClick:S,onPrevClick:P,showWeekNumber:O,styles:E}=e,{formatCaption:T,formatDay:N,formatMonthDropdown:A,formatWeekNumber:j,formatWeekNumberHeader:L,formatWeekdayName:I,formatYearDropdown:R}=s,z=function(t,e){let[i,s]=function(t,e){let{startMonth:i,endMonth:s}=t,{startOfYear:r,startOfDay:a,startOfMonth:n,endOfMonth:o,addYears:l,endOfYear:h,newDate:d,today:c}=e,{fromYear:u,toYear:f,fromMonth:p,toMonth:g}=t;!i&&p&&(i=p),!i&&u&&(i=e.newDate(u,0,1)),!s&&g&&(s=g),!s&&f&&(s=d(f,11,31));let m="dropdown"===t.captionLayout||"dropdown-years"===t.captionLayout;return i?i=n(i):u?i=d(u,0,1):!i&&m&&(i=r(l(t.today??c(),-100))),s?s=o(s):f?s=d(f,11,31):!s&&m&&(s=h(t.today??c())),[i?a(i):i,s?a(s):s]}(t,e),{startOfMonth:r,endOfMonth:a}=e,n=lk(t,e),[o,l]=lP(n,t.month?n:void 0);(0,p.useEffect)(()=>{l(lk(t,e))},[t.timeZone]);let h=function(t,e,i,s){let{numberOfMonths:r=1}=i,a=[];for(let i=0;i<r;i++){let r=s.addMonths(t,i);if(e&&r>e)break;a.push(r)}return a}(o,s,t,e),d=function(t,e,i,s){let r=t[0],a=t[t.length-1],{ISOWeek:n,fixedWeeks:o,broadcastCalendar:l}=i??{},{addDays:h,differenceInCalendarDays:d,differenceInCalendarMonths:c,endOfBroadcastWeek:u,endOfISOWeek:f,endOfMonth:p,endOfWeek:g,isAfter:m,startOfBroadcastWeek:x,startOfISOWeek:b,startOfWeek:y}=s,v=l?x(r,s):n?b(r):y(r),_=d(l?u(a):n?f(p(a)):g(p(a)),v),w=c(a,r)+1,M=[];for(let t=0;t<=_;t++){let i=h(v,t);if(e&&m(i,e))break;M.push(i)}let k=(l?35:42)*w;if(o&&M.length<k){let t=k-M.length;for(let e=0;e<t;e++){let t=h(M[M.length-1],1);M.push(t)}}return M}(h,t.endMonth?a(t.endMonth):void 0,t,e),c=function(t,e,i,s){let{addDays:r,endOfBroadcastWeek:a,endOfISOWeek:n,endOfMonth:o,endOfWeek:l,getISOWeek:h,getWeek:d,startOfBroadcastWeek:c,startOfISOWeek:u,startOfWeek:f}=s,p=t.reduce((t,p)=>{let g=i.broadcastCalendar?c(p,s):i.ISOWeek?u(p):f(p),m=i.broadcastCalendar?a(p):i.ISOWeek?n(o(p)):l(o(p)),x=e.filter(t=>t>=g&&t<=m),b=i.broadcastCalendar?35:42;if(i.fixedWeeks&&x.length<b){let t=e.filter(t=>{let e=b-x.length;return t>m&&t<=r(m,e)});x.push(...t)}let y=x.reduce((t,e)=>{let r=i.ISOWeek?h(e):d(e),a=t.find(t=>t.weekNumber===r),n=new lD(e,p,s);return a?a.days.push(n):t.push(new lC(r,[n])),t},[]),v=new lS(p,y);return t.push(v),t},[]);return i.reverseMonths?p.reverse():p}(h,d,t,e),u=c.reduce((t,e)=>[...t,...e.weeks],[]),f=function(t){let e=[];return t.reduce((t,i)=>[...t,...i.weeks.reduce((t,e)=>[...t,...e.days],e)],e)}(c),g=function(t,e,i,s){if(i.disableNavigation)return;let{pagedNavigation:r,numberOfMonths:a}=i,{startOfMonth:n,addMonths:o,differenceInCalendarMonths:l}=s,h=n(t);if(!e||!(0>=l(h,e)))return o(h,-(r?a??1:1))}(o,i,t,e),m=function(t,e,i,s){if(i.disableNavigation)return;let{pagedNavigation:r,numberOfMonths:a=1}=i,{startOfMonth:n,addMonths:o,differenceInCalendarMonths:l}=s,h=n(t);if(!e||!(l(e,t)<a))return o(h,r?a:1)}(o,s,t,e),{disableNavigation:x,onMonthChange:b}=t,y=t=>u.some(e=>e.days.some(e=>e.isEqualTo(t))),v=t=>{if(x)return;let e=r(t);i&&e<r(i)&&(e=r(i)),s&&e>r(s)&&(e=r(s)),l(e),b?.(e)};return{months:c,weeks:u,days:f,navStart:i,navEnd:s,previousMonth:g,nextMonth:m,goToMonth:v,goToDay:t=>{y(t)||v(t.date)}}}(e,f),{days:F,months:W,navStart:B,navEnd:V,previousMonth:H,nextMonth:Y,goToMonth:$}=z,U=function(t,e,i){let{disabled:s,hidden:r,modifiers:n,showOutsideDays:o,broadcastCalendar:l,today:h}=e,{isSameDay:d,isSameMonth:c,startOfMonth:u,isBefore:f,endOfMonth:p,isAfter:g}=i,m=e.startMonth&&u(e.startMonth),x=e.endMonth&&p(e.endMonth),b={[a.focused]:[],[a.outside]:[],[a.disabled]:[],[a.hidden]:[],[a.today]:[]},y={};for(let e of t){let{date:t,displayMonth:a}=e,u=!!(a&&!c(t,a)),p=!!(m&&f(t,m)),v=!!(x&&g(t,x)),_=!!(s&&oE(t,s,i)),w=!!(r&&oE(t,r,i))||p||v||!l&&!o&&u||l&&!1===o&&u,M=d(t,h??i.today());u&&b.outside.push(e),_&&b.disabled.push(e),w&&b.hidden.push(e),M&&b.today.push(e),n&&Object.keys(n).forEach(s=>{let r=n?.[s];r&&oE(t,r,i)&&(y[s]?y[s].push(e):y[s]=[e])})}return t=>{let e={[a.focused]:!1,[a.disabled]:!1,[a.hidden]:!1,[a.outside]:!1,[a.today]:!1},i={};for(let i in b){let s=b[i];e[i]=s.some(e=>e===t)}for(let e in y)i[e]=y[e].some(e=>e===t);return{...e,...i}}}(F,e,f),{isSelected:Z,select:X,selected:q}=function(t,e){let i=function(t,e){let{selected:i,required:s,onSelect:r}=t,[a,n]=lP(i,r?i:void 0),o=r?i:a,{isSameDay:l}=e;return{selected:o,select:(t,e,i)=>{let a=t;return!s&&o&&o&&l(t,o)&&(a=void 0),r||n(a),r?.(a,t,e,i),a},isSelected:t=>!!o&&l(o,t)}}(t,e),s=function(t,e){let{selected:i,required:s,onSelect:r}=t,[a,n]=lP(i,r?i:void 0),o=r?i:a,{isSameDay:l}=e,h=t=>o?.some(e=>l(e,t))??!1,{min:d,max:c}=t;return{selected:o,select:(t,e,i)=>{let a=[...o??[]];if(h(t)){if(o?.length===d||s&&o?.length===1)return;a=o?.filter(e=>!l(e,t))}else a=o?.length===c?[t]:[...a,t];return r||n(a),r?.(a,t,e,i),a},isSelected:h}}(t,e),r=function(t,e){let{disabled:i,excludeDisabled:s,selected:r,required:a,onSelect:n}=t,[o,l]=lP(r,n?r:void 0),h=n?r:o;return{selected:h,select:(r,o,d)=>{let{min:c,max:u}=t,f=r?function(t,e,i=0,s=0,r=!1,a=ow){let n,{from:o,to:l}=e||{},{isSameDay:h,isAfter:d,isBefore:c}=a;if(o||l){if(o&&!l)n=h(o,t)?r?{from:o,to:void 0}:void 0:c(t,o)?{from:t,to:o}:{from:o,to:t};else if(o&&l)if(h(o,t)&&h(l,t))n=r?{from:o,to:l}:void 0;else if(h(o,t))n={from:o,to:i>0?void 0:t};else if(h(l,t))n={from:t,to:i>0?void 0:t};else if(c(t,o))n={from:t,to:l};else if(d(t,o))n={from:o,to:t};else if(d(t,l))n={from:o,to:t};else throw Error("Invalid range")}else n={from:t,to:i>0?void 0:t};if(n?.from&&n?.to){let e=a.differenceInCalendarDays(n.to,n.from);s>0&&e>s?n={from:t,to:void 0}:i>1&&e<i&&(n={from:t,to:void 0})}return n}(r,h,c,u,a,e):void 0;return s&&i&&f?.from&&f.to&&function(t,e,i=ow){let s=Array.isArray(e)?e:[e];if(s.filter(t=>"function"!=typeof t).some(e=>"boolean"==typeof e?e:i.isDate(e)?oM(t,e,!1,i):oO(e,i)?e.some(e=>oM(t,e,!1,i)):oD(e)?!!e.from&&!!e.to&&lE(t,{from:e.from,to:e.to},i):oP(e)?function(t,e,i=ow){let s=Array.isArray(e)?e:[e],r=t.from,a=Math.min(i.differenceInCalendarDays(t.to,t.from),6);for(let t=0;t<=a;t++){if(s.includes(r.getDay()))return!0;r=i.addDays(r,1)}return!1}(t,e.dayOfWeek,i):ok(e)?i.isAfter(e.before,e.after)?lE(t,{from:i.addDays(e.after,1),to:i.addDays(e.before,-1)},i):oE(t.from,e,i)||oE(t.to,e,i):!!(oC(e)||oS(e))&&(oE(t.from,e,i)||oE(t.to,e,i))))return!0;let r=s.filter(t=>"function"==typeof t);if(r.length){let e=t.from,s=i.differenceInCalendarDays(t.to,t.from);for(let t=0;t<=s;t++){if(r.some(t=>t(e)))return!0;e=i.addDays(e,1)}}return!1}({from:f.from,to:f.to},i,e)&&(f.from=r,f.to=void 0),n||l(f),n?.(f,r,o,d),f},isSelected:t=>h&&oM(h,t,!1,e)}}(t,e);switch(t.mode){case"single":return i;case"multiple":return s;case"range":return r;default:return}}(e,f)??{},{blur:G,focused:K,isFocusTarget:J,moveFocus:Q,setFocused:tt}=function(t,e,i,s,r){let{autoFocus:n}=t,[o,h]=(0,p.useState)(),d=function(t,e,i,s){let r,n=-1;for(let o of t){let t=e(o);lO(t)&&(t[a.focused]&&n<l.FocusedModifier?(r=o,n=l.FocusedModifier):s?.isEqualTo(o)&&n<l.LastFocused?(r=o,n=l.LastFocused):i(o.date)&&n<l.Selected?(r=o,n=l.Selected):t[a.today]&&n<l.Today&&(r=o,n=l.Today))}return r||(r=t.find(t=>lO(e(t)))),r}(e.days,i,s||(()=>!1),o),[c,u]=(0,p.useState)(n?d:void 0);return{isFocusTarget:t=>!!d?.isEqualTo(t),setFocused:u,focused:c,blur:()=>{h(c),u(void 0)},moveFocus:(i,s)=>{if(!c)return;let a=function t(e,i,s,r,a,n,o,l=0){if(l>365)return;let h=function(t,e,i,s,r,a,n){let{ISOWeek:o,broadcastCalendar:l}=a,{addDays:h,addMonths:d,addWeeks:c,addYears:u,endOfBroadcastWeek:f,endOfISOWeek:p,endOfWeek:g,max:m,min:x,startOfBroadcastWeek:b,startOfISOWeek:y,startOfWeek:v}=n,_=({day:h,week:c,month:d,year:u,startOfWeek:t=>l?b(t,n):o?y(t):v(t),endOfWeek:t=>l?f(t):o?p(t):g(t)})[t](i,"after"===e?1:-1);return"before"===e&&s?_=m([s,_]):"after"===e&&r&&(_=x([r,_])),_}(e,i,s.date,r,a,n,o),d=!!(n.disabled&&oE(h,n.disabled,o)),c=!!(n.hidden&&oE(h,n.hidden,o)),u=new lD(h,h,o);return d||c?t(e,i,u,r,a,n,o,l+1):u}(i,s,c,e.navStart,e.navEnd,t,r);a&&(e.goToDay(a),u(a))}}}(e,z,U,Z??(()=>!1),f),{labelDayButton:te,labelGridcell:ti,labelGrid:ts,labelMonthDropdown:tr,labelNav:ta,labelPrevious:tn,labelNext:to,labelWeekday:tl,labelWeekNumber:th,labelWeekNumberHeader:td,labelYearDropdown:tc}=h,tu=(0,p.useMemo)(()=>(function(t,e,i){let s=t.today(),r=e?t.startOfISOWeek(s):t.startOfWeek(s),a=[];for(let e=0;e<7;e++){let i=t.addDays(r,e);a.push(i)}return a})(f,e.ISOWeek),[f,e.ISOWeek]),tf=void 0!==b||void 0!==w,tp=(0,p.useCallback)(()=>{H&&($(H),P?.(H))},[H,$,P]),tg=(0,p.useCallback)(()=>{Y&&($(Y),S?.(Y))},[$,Y,S]),tm=(0,p.useCallback)((t,e)=>i=>{i.preventDefault(),i.stopPropagation(),tt(t),X?.(t.date,e,i),w?.(t.date,e,i)},[X,w,tt]),tx=(0,p.useCallback)((t,e)=>i=>{tt(t),M?.(t.date,e,i)},[M,tt]),tb=(0,p.useCallback)((t,e)=>i=>{G(),_?.(t.date,e,i)},[G,_]),ty=(0,p.useCallback)((t,i)=>s=>{let r={ArrowLeft:["day","rtl"===e.dir?"after":"before"],ArrowRight:["day","rtl"===e.dir?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[s.shiftKey?"year":"month","before"],PageDown:[s.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(r[s.key]){s.preventDefault(),s.stopPropagation();let[t,e]=r[s.key];Q(t,e)}k?.(t.date,i,s)},[Q,k,e.dir]),tv=(0,p.useCallback)((t,e)=>i=>{D?.(t.date,e,i)},[D]),t_=(0,p.useCallback)((t,e)=>i=>{C?.(t.date,e,i)},[C]),tw=(0,p.useCallback)(t=>e=>{let i=Number(e.target.value);$(f.setMonth(f.startOfMonth(t),i))},[f,$]),tM=(0,p.useCallback)(t=>e=>{let i=Number(e.target.value);$(f.setYear(f.startOfMonth(t),i))},[f,$]),{className:tk,style:tD}=(0,p.useMemo)(()=>({className:[m[r.Root],e.className].filter(Boolean).join(" "),style:{...E?.[r.Root],...e.style}}),[m,e.className,e.style,E]),tC=function(t){let e={"data-mode":t.mode??void 0,"data-required":"required"in t?t.required:void 0,"data-multiple-months":t.numberOfMonths&&t.numberOfMonths>1||void 0,"data-week-numbers":t.showWeekNumber||void 0,"data-broadcast-calendar":t.broadcastCalendar||void 0,"data-nav-layout":t.navLayout||void 0};return Object.entries(t).forEach(([t,i])=>{t.startsWith("data-")&&(e[t]=i)}),e}(e),tS=(0,p.useRef)(null);!function(t,e,{classNames:i,months:s,focused:r,dateLib:a}){let n=(0,p.useRef)(null),l=(0,p.useRef)(s),h=(0,p.useRef)(!1);(0,p.useLayoutEffect)(()=>{let d=l.current;if(l.current=s,!e||!t.current||!(t.current instanceof HTMLElement)||0===s.length||0===d.length||s.length!==d.length)return;let c=a.isSameMonth(s[0].date,d[0].date),u=a.isAfter(s[0].date,d[0].date),f=u?i[o.caption_after_enter]:i[o.caption_before_enter],p=u?i[o.weeks_after_enter]:i[o.weeks_before_enter],g=n.current,m=t.current.cloneNode(!0);if(m instanceof HTMLElement?(lb(m).forEach(t=>{if(!(t instanceof HTMLElement))return;let e=ly(t);e&&t.contains(e)&&t.removeChild(e);let i=lv(t);i&&i.classList.remove(f);let s=l_(t);s&&s.classList.remove(p)}),n.current=m):n.current=null,h.current||c||r)return;let x=g instanceof HTMLElement?lb(g):[],b=lb(t.current);if(b&&b.every(t=>t instanceof HTMLElement)&&x&&x.every(t=>t instanceof HTMLElement)){h.current=!0;let e=[];t.current.style.isolation="isolate";let s=lw(t.current);s&&(s.style.zIndex="1"),b.forEach((r,a)=>{let n=x[a];if(!n)return;r.style.position="relative",r.style.overflow="hidden";let l=lv(r);l&&l.classList.add(f);let d=l_(r);d&&d.classList.add(p);let c=()=>{h.current=!1,t.current&&(t.current.style.isolation=""),s&&(s.style.zIndex=""),l&&l.classList.remove(f),d&&d.classList.remove(p),r.style.position="",r.style.overflow="",r.contains(n)&&r.removeChild(n)};e.push(c),n.style.pointerEvents="none",n.style.position="absolute",n.style.overflow="hidden",n.setAttribute("aria-hidden","true");let g=lM(n);g&&(g.style.opacity="0");let m=lv(n);m&&(m.classList.add(u?i[o.caption_before_exit]:i[o.caption_after_exit]),m.addEventListener("animationend",c));let b=l_(n);b&&b.classList.add(u?i[o.weeks_before_exit]:i[o.weeks_after_exit]),r.insertBefore(n,r.firstChild)})}})}(tS,!!e.animate,{classNames:m,months:W,focused:K,dateLib:f});let tP={dayPickerProps:e,selected:q,select:X,isSelected:Z,months:W,nextMonth:Y,previousMonth:H,goToMonth:$,getModifiers:U,components:i,classNames:m,styles:E,labels:h,formatters:s};return p.createElement(oH.Provider,{value:tP},p.createElement(i.Root,{rootRef:e.animate?tS:void 0,className:tk,style:tD,dir:e.dir,id:e.id,lang:e.lang,nonce:e.nonce,title:e.title,role:e.role,"aria-label":e["aria-label"],...tC},p.createElement(i.Months,{className:m[r.Months],style:E?.[r.Months]},!e.hideNavigation&&!y&&p.createElement(i.Nav,{"data-animated-nav":e.animate?"true":void 0,className:m[r.Nav],style:E?.[r.Nav],"aria-label":ta(),onPreviousClick:tp,onNextClick:tg,previousMonth:H,nextMonth:Y}),W.map((t,o)=>{let l=function(t,e,i,s,r){let{startOfMonth:a,startOfYear:n,endOfYear:o,eachMonthOfInterval:l,getMonth:h}=r;return l({start:n(t),end:o(t)}).map(t=>{let n=s.formatMonthDropdown(t,r);return{value:h(t),label:n,disabled:e&&t<a(e)||i&&t>a(i)||!1}})}(t.date,B,V,s,f),h=function(t,e,i,s){if(!t||!e)return;let{startOfYear:r,endOfYear:a,addYears:n,getYear:o,isBefore:l,isSameYear:h}=s,d=r(t),c=a(e),u=[],f=d;for(;l(f,c)||h(f,c);)u.push(f),f=n(f,1);return u.map(t=>{let e=i.formatYearDropdown(t,s);return{value:o(t),label:e,disabled:!1}})}(B,V,s,f);return p.createElement(i.Month,{"data-animated-month":e.animate?"true":void 0,className:m[r.Month],style:E?.[r.Month],key:o,displayIndex:o,calendarMonth:t},"around"===y&&!e.hideNavigation&&0===o&&p.createElement(i.PreviousMonthButton,{type:"button",className:m[r.PreviousMonthButton],tabIndex:H?void 0:-1,"aria-disabled":!H||void 0,"aria-label":tn(H),onClick:tp,"data-animated-button":e.animate?"true":void 0},p.createElement(i.Chevron,{disabled:!H||void 0,className:m[r.Chevron],orientation:"rtl"===e.dir?"right":"left"})),p.createElement(i.MonthCaption,{"data-animated-caption":e.animate?"true":void 0,className:m[r.MonthCaption],style:E?.[r.MonthCaption],calendarMonth:t,displayIndex:o},x?.startsWith("dropdown")?p.createElement(i.DropdownNav,{className:m[r.Dropdowns],style:E?.[r.Dropdowns]},"dropdown"===x||"dropdown-months"===x?p.createElement(i.MonthsDropdown,{className:m[r.MonthsDropdown],"aria-label":tr(),classNames:m,components:i,disabled:!!e.disableNavigation,onChange:tw(t.date),options:l,style:E?.[r.Dropdown],value:f.getMonth(t.date)}):p.createElement("span",null,A(t.date,f)),"dropdown"===x||"dropdown-years"===x?p.createElement(i.YearsDropdown,{className:m[r.YearsDropdown],"aria-label":tc(f.options),classNames:m,components:i,disabled:!!e.disableNavigation,onChange:tM(t.date),options:h,style:E?.[r.Dropdown],value:f.getYear(t.date)}):p.createElement("span",null,R(t.date,f)),p.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},T(t.date,f.options,f))):p.createElement(i.CaptionLabel,{className:m[r.CaptionLabel],role:"status","aria-live":"polite"},T(t.date,f.options,f))),"around"===y&&!e.hideNavigation&&o===v-1&&p.createElement(i.NextMonthButton,{type:"button",className:m[r.NextMonthButton],tabIndex:Y?void 0:-1,"aria-disabled":!Y||void 0,"aria-label":to(Y),onClick:tg,"data-animated-button":e.animate?"true":void 0},p.createElement(i.Chevron,{disabled:!Y||void 0,className:m[r.Chevron],orientation:"rtl"===e.dir?"left":"right"})),o===v-1&&"after"===y&&!e.hideNavigation&&p.createElement(i.Nav,{"data-animated-nav":e.animate?"true":void 0,className:m[r.Nav],style:E?.[r.Nav],"aria-label":ta(),onPreviousClick:tp,onNextClick:tg,previousMonth:H,nextMonth:Y}),p.createElement(i.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===b||"range"===b,"aria-label":ts(t.date,f.options,f)||void 0,className:m[r.MonthGrid],style:E?.[r.MonthGrid]},!e.hideWeekdays&&p.createElement(i.Weekdays,{"data-animated-weekdays":e.animate?"true":void 0,className:m[r.Weekdays],style:E?.[r.Weekdays]},O&&p.createElement(i.WeekNumberHeader,{"aria-label":td(f.options),className:m[r.WeekNumberHeader],style:E?.[r.WeekNumberHeader],scope:"col"},L()),tu.map((t,e)=>p.createElement(i.Weekday,{"aria-label":tl(t,f.options,f),className:m[r.Weekday],key:e,style:E?.[r.Weekday],scope:"col"},I(t,f.options,f)))),p.createElement(i.Weeks,{"data-animated-weeks":e.animate?"true":void 0,className:m[r.Weeks],style:E?.[r.Weeks]},t.weeks.map((t,s)=>p.createElement(i.Week,{className:m[r.Week],key:t.weekNumber,style:E?.[r.Week],week:t},O&&p.createElement(i.WeekNumber,{week:t,style:E?.[r.WeekNumber],"aria-label":th(t.weekNumber,{locale:g}),className:m[r.WeekNumber],scope:"row",role:"rowheader"},j(t.weekNumber,f)),t.days.map(t=>{let{date:s}=t,o=U(t);if(o[a.focused]=!o.hidden&&!!K?.isEqualTo(t),o[n.selected]=Z?.(s)||o.selected,oD(q)){let{from:t,to:e}=q;o[n.range_start]=!!(t&&e&&f.isSameDay(s,t)),o[n.range_end]=!!(t&&e&&f.isSameDay(s,e)),o[n.range_middle]=oM(q,s,!0,f)}let l=function(t,e={},i={}){let s={...e?.[r.Day]};return Object.entries(t).filter(([,t])=>!0===t).forEach(([t])=>{s={...s,...i?.[t]}}),s}(o,E,e.modifiersStyles),h=function(t,e,i={}){return Object.entries(t).filter(([,t])=>!0===t).reduce((t,[s])=>(i[s]?t.push(i[s]):e[a[s]]?t.push(e[a[s]]):e[n[s]]&&t.push(e[n[s]]),t),[e[r.Day]])}(o,m,e.modifiersClassNames),d=tf||o.hidden?void 0:ti(s,o,f.options,f);return p.createElement(i.Day,{key:`${f.format(s,"yyyy-MM-dd")}_${f.format(t.displayMonth,"yyyy-MM")}`,day:t,modifiers:o,className:h.join(" "),style:l,role:"gridcell","aria-selected":o.selected||void 0,"aria-label":d,"data-day":f.format(s,"yyyy-MM-dd"),"data-month":t.outside?f.format(s,"yyyy-MM"):void 0,"data-selected":o.selected||void 0,"data-disabled":o.disabled||void 0,"data-hidden":o.hidden||void 0,"data-outside":t.outside||void 0,"data-focused":o.focused||void 0,"data-today":o.today||void 0},!o.hidden&&tf?p.createElement(i.DayButton,{className:m[r.DayButton],style:E?.[r.DayButton],type:"button",day:t,modifiers:o,disabled:o.disabled||void 0,tabIndex:J(t)?0:-1,"aria-label":te(s,o,f.options,f),onClick:tm(t,o),onBlur:tb(t,o),onFocus:tx(t,o),onKeyDown:ty(t,o),onMouseEnter:tv(t,o),onMouseLeave:t_(t,o)},N(s,f.options,f)):!o.hidden&&N(t.date,f.options,f))}))))))})),e.footer&&p.createElement(i.Footer,{className:m[r.Footer],style:E?.[r.Footer],role:"status","aria-live":"polite"},e.footer)))}!function(t){t[t.Today=0]="Today",t[t.Selected=1]="Selected",t[t.LastFocused=2]="LastFocused",t[t.FocusedModifier=3]="FocusedModifier"}(l||(l={}));var lN=i(4780);function lA({className:t,classNames:e,showOutsideDays:i=!0,...s}){return(0,f.jsx)(lT,{showOutsideDays:i,className:(0,lN.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,lN.cn)((0,N.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,lN.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===s.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,lN.cn)((0,N.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...e},components:{Chevron:({orientation:t,...e})=>{let i="left"===t?n3:n4.A;return(0,f.jsx)(i,{className:"size-4",...e})}},...s})}var lj=i(70569),lL=i(98599),lI=i(11273),lR=i(51215),lz=i(8730),lF=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let i=(0,lz.TL)(`Primitive.${e}`),s=p.forwardRef((t,s)=>{let{asChild:r,...a}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,f.jsx)(r?i:e,{...a,ref:s})});return s.displayName=`Primitive.${e}`,{...t,[e]:s}},{}),lW=i(13495),lB=i(16309),lV="dismissableLayer.update",lH=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),lY=p.forwardRef((t,e)=>{let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:s,onPointerDownOutside:r,onFocusOutside:a,onInteractOutside:n,onDismiss:o,...l}=t,d=p.useContext(lH),[c,u]=p.useState(null),g=c?.ownerDocument??globalThis?.document,[,m]=p.useState({}),x=(0,lL.s)(e,t=>u(t)),b=Array.from(d.layers),[y]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),v=b.indexOf(y),_=c?b.indexOf(c):-1,w=d.layersWithOutsidePointerEventsDisabled.size>0,M=_>=v,k=function(t,e=globalThis?.document){let i=(0,lW.c)(t),s=p.useRef(!1),r=p.useRef(()=>{});return p.useEffect(()=>{let t=t=>{if(t.target&&!s.current){let s=function(){lU("dismissableLayer.pointerDownOutside",i,a,{discrete:!0})},a={originalEvent:t};"touch"===t.pointerType?(e.removeEventListener("click",r.current),r.current=s,e.addEventListener("click",r.current,{once:!0})):s()}else e.removeEventListener("click",r.current);s.current=!1},a=window.setTimeout(()=>{e.addEventListener("pointerdown",t)},0);return()=>{window.clearTimeout(a),e.removeEventListener("pointerdown",t),e.removeEventListener("click",r.current)}},[e,i]),{onPointerDownCapture:()=>s.current=!0}}(t=>{let e=t.target,i=[...d.branches].some(t=>t.contains(e));M&&!i&&(r?.(t),n?.(t),t.defaultPrevented||o?.())},g),D=function(t,e=globalThis?.document){let i=(0,lW.c)(t),s=p.useRef(!1);return p.useEffect(()=>{let t=t=>{t.target&&!s.current&&lU("dismissableLayer.focusOutside",i,{originalEvent:t},{discrete:!1})};return e.addEventListener("focusin",t),()=>e.removeEventListener("focusin",t)},[e,i]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}(t=>{let e=t.target;![...d.branches].some(t=>t.contains(e))&&(a?.(t),n?.(t),t.defaultPrevented||o?.())},g);return(0,lB.U)(t=>{_===d.layers.size-1&&(s?.(t),!t.defaultPrevented&&o&&(t.preventDefault(),o()))},g),p.useEffect(()=>{if(c)return i&&(0===d.layersWithOutsidePointerEventsDisabled.size&&(h=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(c)),d.layers.add(c),l$(),()=>{i&&1===d.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=h)}},[c,g,i,d]),p.useEffect(()=>()=>{c&&(d.layers.delete(c),d.layersWithOutsidePointerEventsDisabled.delete(c),l$())},[c,d]),p.useEffect(()=>{let t=()=>m({});return document.addEventListener(lV,t),()=>document.removeEventListener(lV,t)},[]),(0,f.jsx)(lF.div,{...l,ref:x,style:{pointerEvents:w?M?"auto":"none":void 0,...t.style},onFocusCapture:(0,lj.m)(t.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,lj.m)(t.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,lj.m)(t.onPointerDownCapture,k.onPointerDownCapture)})});function l$(){let t=new CustomEvent(lV);document.dispatchEvent(t)}function lU(t,e,i,{discrete:s}){let r=i.originalEvent.target,a=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:i});if(e&&r.addEventListener(t,e,{once:!0}),s)r&&lR.flushSync(()=>r.dispatchEvent(a));else r.dispatchEvent(a)}lY.displayName="DismissableLayer",p.forwardRef((t,e)=>{let i=p.useContext(lH),s=p.useRef(null),r=(0,lL.s)(e,s);return p.useEffect(()=>{let t=s.current;if(t)return i.branches.add(t),()=>{i.branches.delete(t)}},[i.branches]),(0,f.jsx)(lF.div,{...t,ref:r})}).displayName="DismissableLayerBranch";var lZ=i(1359),lX="focusScope.autoFocusOnMount",lq="focusScope.autoFocusOnUnmount",lG={bubbles:!1,cancelable:!0},lK=p.forwardRef((t,e)=>{let{loop:i=!1,trapped:s=!1,onMountAutoFocus:r,onUnmountAutoFocus:a,...n}=t,[o,l]=p.useState(null),h=(0,lW.c)(r),d=(0,lW.c)(a),c=p.useRef(null),u=(0,lL.s)(e,t=>l(t)),g=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(s){let t=function(t){if(g.paused||!o)return;let e=t.target;o.contains(e)?c.current=e:l0(c.current,{select:!0})},e=function(t){if(g.paused||!o)return;let e=t.relatedTarget;null!==e&&(o.contains(e)||l0(c.current,{select:!0}))};document.addEventListener("focusin",t),document.addEventListener("focusout",e);let i=new MutationObserver(function(t){if(document.activeElement===document.body)for(let e of t)e.removedNodes.length>0&&l0(o)});return o&&i.observe(o,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",t),document.removeEventListener("focusout",e),i.disconnect()}}},[s,o,g.paused]),p.useEffect(()=>{if(o){l1.add(g);let t=document.activeElement;if(!o.contains(t)){let e=new CustomEvent(lX,lG);o.addEventListener(lX,h),o.dispatchEvent(e),e.defaultPrevented||(function(t,{select:e=!1}={}){let i=document.activeElement;for(let s of t)if(l0(s,{select:e}),document.activeElement!==i)return}(lJ(o).filter(t=>"A"!==t.tagName),{select:!0}),document.activeElement===t&&l0(o))}return()=>{o.removeEventListener(lX,h),setTimeout(()=>{let e=new CustomEvent(lq,lG);o.addEventListener(lq,d),o.dispatchEvent(e),e.defaultPrevented||l0(t??document.body,{select:!0}),o.removeEventListener(lq,d),l1.remove(g)},0)}}},[o,h,d,g]);let m=p.useCallback(t=>{if(!i&&!s||g.paused)return;let e="Tab"===t.key&&!t.altKey&&!t.ctrlKey&&!t.metaKey,r=document.activeElement;if(e&&r){let e=t.currentTarget,[s,a]=function(t){let e=lJ(t);return[lQ(e,t),lQ(e.reverse(),t)]}(e);s&&a?t.shiftKey||r!==a?t.shiftKey&&r===s&&(t.preventDefault(),i&&l0(a,{select:!0})):(t.preventDefault(),i&&l0(s,{select:!0})):r===e&&t.preventDefault()}},[i,s,g.paused]);return(0,f.jsx)(lF.div,{tabIndex:-1,...n,ref:u,onKeyDown:m})});function lJ(t){let e=[],i=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:t=>{let e="INPUT"===t.tagName&&"hidden"===t.type;return t.disabled||t.hidden||e?NodeFilter.FILTER_SKIP:t.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;i.nextNode();)e.push(i.currentNode);return e}function lQ(t,e){for(let i of t)if(!function(t,{upTo:e}){if("hidden"===getComputedStyle(t).visibility)return!0;for(;t&&(void 0===e||t!==e);){if("none"===getComputedStyle(t).display)return!0;t=t.parentElement}return!1}(i,{upTo:e}))return i}function l0(t,{select:e=!1}={}){if(t&&t.focus){var i;let s=document.activeElement;t.focus({preventScroll:!0}),t!==s&&(i=t)instanceof HTMLInputElement&&"select"in i&&e&&t.select()}}lK.displayName="FocusScope";var l1=function(){let t=[];return{add(e){let i=t[0];e!==i&&i?.pause(),(t=l2(t,e)).unshift(e)},remove(e){t=l2(t,e),t[0]?.resume()}}}();function l2(t,e){let i=[...t],s=i.indexOf(e);return -1!==s&&i.splice(s,1),i}var l5=i(96963),l3=i(73393),l4=i(66156),l8=p.forwardRef((t,e)=>{let{container:i,...s}=t,[r,a]=p.useState(!1);(0,l4.N)(()=>a(!0),[]);let n=i||r&&globalThis?.document?.body;return n?lR.createPortal((0,f.jsx)(lF.div,{...s,ref:e}),n):null});l8.displayName="Portal";var l6=i(46059),l9=i(65551),l7=i(63376),ht=i(42247),he="Popover",[hi,hs]=(0,lI.A)(he,[l3.Bk]),hr=(0,l3.Bk)(),[ha,hn]=hi(he),ho=t=>{let{__scopePopover:e,children:i,open:s,defaultOpen:r,onOpenChange:a,modal:n=!1}=t,o=hr(e),l=p.useRef(null),[h,d]=p.useState(!1),[c,u]=(0,l9.i)({prop:s,defaultProp:r??!1,onChange:a,caller:he});return(0,f.jsx)(l3.bL,{...o,children:(0,f.jsx)(ha,{scope:e,contentId:(0,l5.B)(),triggerRef:l,open:c,onOpenChange:u,onOpenToggle:p.useCallback(()=>u(t=>!t),[u]),hasCustomAnchor:h,onCustomAnchorAdd:p.useCallback(()=>d(!0),[]),onCustomAnchorRemove:p.useCallback(()=>d(!1),[]),modal:n,children:i})})};ho.displayName=he;var hl="PopoverAnchor";p.forwardRef((t,e)=>{let{__scopePopover:i,...s}=t,r=hn(hl,i),a=hr(i),{onCustomAnchorAdd:n,onCustomAnchorRemove:o}=r;return p.useEffect(()=>(n(),()=>o()),[n,o]),(0,f.jsx)(l3.Mz,{...a,...s,ref:e})}).displayName=hl;var hh="PopoverTrigger",hd=p.forwardRef((t,e)=>{let{__scopePopover:i,...s}=t,r=hn(hh,i),a=hr(i),n=(0,lL.s)(e,r.triggerRef),o=(0,f.jsx)(lF.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":hw(r.open),...s,ref:n,onClick:(0,lj.m)(t.onClick,r.onOpenToggle)});return r.hasCustomAnchor?o:(0,f.jsx)(l3.Mz,{asChild:!0,...a,children:o})});hd.displayName=hh;var hc="PopoverPortal",[hu,hf]=hi(hc,{forceMount:void 0}),hp=t=>{let{__scopePopover:e,forceMount:i,children:s,container:r}=t,a=hn(hc,e);return(0,f.jsx)(hu,{scope:e,forceMount:i,children:(0,f.jsx)(l6.C,{present:i||a.open,children:(0,f.jsx)(l8,{asChild:!0,container:r,children:s})})})};hp.displayName=hc;var hg="PopoverContent",hm=p.forwardRef((t,e)=>{let i=hf(hg,t.__scopePopover),{forceMount:s=i.forceMount,...r}=t,a=hn(hg,t.__scopePopover);return(0,f.jsx)(l6.C,{present:s||a.open,children:a.modal?(0,f.jsx)(hb,{...r,ref:e}):(0,f.jsx)(hy,{...r,ref:e})})});hm.displayName=hg;var hx=(0,lz.TL)("PopoverContent.RemoveScroll"),hb=p.forwardRef((t,e)=>{let i=hn(hg,t.__scopePopover),s=p.useRef(null),r=(0,lL.s)(e,s),a=p.useRef(!1);return p.useEffect(()=>{let t=s.current;if(t)return(0,l7.Eq)(t)},[]),(0,f.jsx)(ht.A,{as:hx,allowPinchZoom:!0,children:(0,f.jsx)(hv,{...t,ref:r,trapFocus:i.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,lj.m)(t.onCloseAutoFocus,t=>{t.preventDefault(),a.current||i.triggerRef.current?.focus()}),onPointerDownOutside:(0,lj.m)(t.onPointerDownOutside,t=>{let e=t.detail.originalEvent,i=0===e.button&&!0===e.ctrlKey;a.current=2===e.button||i},{checkForDefaultPrevented:!1}),onFocusOutside:(0,lj.m)(t.onFocusOutside,t=>t.preventDefault(),{checkForDefaultPrevented:!1})})})}),hy=p.forwardRef((t,e)=>{let i=hn(hg,t.__scopePopover),s=p.useRef(!1),r=p.useRef(!1);return(0,f.jsx)(hv,{...t,ref:e,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{t.onCloseAutoFocus?.(e),e.defaultPrevented||(s.current||i.triggerRef.current?.focus(),e.preventDefault()),s.current=!1,r.current=!1},onInteractOutside:e=>{t.onInteractOutside?.(e),e.defaultPrevented||(s.current=!0,"pointerdown"===e.detail.originalEvent.type&&(r.current=!0));let a=e.target;i.triggerRef.current?.contains(a)&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&r.current&&e.preventDefault()}})}),hv=p.forwardRef((t,e)=>{let{__scopePopover:i,trapFocus:s,onOpenAutoFocus:r,onCloseAutoFocus:a,disableOutsidePointerEvents:n,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:h,onInteractOutside:d,...c}=t,u=hn(hg,i),p=hr(i);return(0,lZ.Oh)(),(0,f.jsx)(lK,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:r,onUnmountAutoFocus:a,children:(0,f.jsx)(lY,{asChild:!0,disableOutsidePointerEvents:n,onInteractOutside:d,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:h,onDismiss:()=>u.onOpenChange(!1),children:(0,f.jsx)(l3.UC,{"data-state":hw(u.open),role:"dialog",id:u.contentId,...p,...c,ref:e,style:{...c.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),h_="PopoverClose";function hw(t){return t?"open":"closed"}function hM({...t}){return(0,f.jsx)(ho,{"data-slot":"popover",...t})}function hk({...t}){return(0,f.jsx)(hd,{"data-slot":"popover-trigger",...t})}function hD({className:t,align:e="center",sideOffset:i=4,...s}){return(0,f.jsx)(hp,{children:(0,f.jsx)(hm,{"data-slot":"popover-content",align:e,sideOffset:i,className:(0,lN.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...s})})}p.forwardRef((t,e)=>{let{__scopePopover:i,...s}=t,r=hn(h_,i);return(0,f.jsx)(lF.button,{type:"button",...s,ref:e,onClick:(0,lj.m)(t.onClick,()=>r.onOpenChange(!1))})}).displayName=h_,p.forwardRef((t,e)=>{let{__scopePopover:i,...s}=t,r=hr(i);return(0,f.jsx)(l3.i3,{...r,...s,ref:e})}).displayName="PopoverArrow";var hC=i(40228),hS=i(41585);function hP({onChange:t,className:e}){let i=new Date,[s,r]=(0,p.useState)(30),[a,n]=(0,p.useState)(!1),[o,l]=(0,p.useState)({from:x(i,-30,void 0),to:i}),h=e=>{let s={from:x(i,-e,void 0),to:i};l(s),r(e),t(s)};return(0,f.jsxs)("div",{className:(0,lN.cn)("flex flex-wrap gap-2",e),children:[[{label:"\xdaltimos 7 d\xedas",days:7},{label:"\xdaltimos 14 d\xedas",days:14},{label:"\xdaltimos 30 d\xedas",days:30}].map(t=>(0,f.jsx)(N.Button,{variant:s===t.days?"default":"outline",size:"sm",onClick:()=>h(t.days),className:s===t.days?"bg-blue-600 hover:bg-blue-700":"",children:t.label},t.days)),(0,f.jsxs)(hM,{open:a,onOpenChange:n,children:[(0,f.jsx)(hk,{asChild:!0,children:(0,f.jsxs)(N.Button,{variant:0===s?"default":"outline",size:"sm",className:(0,lN.cn)("flex items-center gap-1",0===s?"bg-blue-600 hover:bg-blue-700":""),children:[(0,f.jsx)(hC.A,{className:"h-4 w-4"}),0===s?`${(0,b.GP)(o.from,"dd/MM/yy")} - ${(0,b.GP)(o.to,"dd/MM/yy")}`:"Personalizado"]})}),(0,f.jsx)(hD,{className:"w-auto p-0",align:"end",children:(0,f.jsx)(lA,{initialFocus:!0,mode:"range",defaultMonth:o.from,selected:o,onSelect:e=>{if(e?.from&&e?.to){let i={from:e.from,to:e.to};l(i),r(0),t(i),n(!1)}},numberOfMonths:2,locale:hS.es,disabled:t=>op(t,i)})})]})]})}var hO=i(76242);function hE(){(0,O.As)();let[t,e]=(0,p.useState)(!1),[i,s]=(0,p.useState)([]),[r,a]=(0,p.useState)({from:x(new Date,-30,void 0),to:new Date}),{accountData:n,error:o,isLoading:l}=(0,O.T4)(),{usageData:h,error:d,isLoading:c,mutate:u}=(0,O.TB)(),{data:g,error:m,isLoading:y,mutate:_}=(0,O.Xn)(r.from,r.to),w=async()=>{e(!0);try{await Promise.all([u(),_()]),I.o.success("Datos de uso actualizados")}catch(t){(0,R.h)(t,"Error al actualizar los datos de uso")}finally{e(!1)}};if((()=>{if(i.length)return i.reduce((t,e)=>t+e.apiCalls,0),i[i.length-1]?.storage})(),c||l||y)return(0,f.jsxs)("div",{className:"container mx-auto py-8",children:[(0,f.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,f.jsx)("h1",{className:"text-display",children:"Uso de API"}),(0,f.jsx)(T.E,{className:"h-9 w-24"})]}),(0,f.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[1,2,3].map(t=>(0,f.jsxs)(E.Zp,{children:[(0,f.jsxs)(E.aR,{className:"pb-2",children:[(0,f.jsx)(T.E,{className:"h-5 w-24"}),(0,f.jsx)(T.E,{className:"h-4 w-16"})]}),(0,f.jsxs)(E.Wu,{children:[(0,f.jsx)(T.E,{className:"h-8 w-16 mx-auto mb-2"}),(0,f.jsx)(T.E,{className:"h-2 w-full"})]})]},t))}),(0,f.jsxs)(E.Zp,{children:[(0,f.jsxs)(E.aR,{children:[(0,f.jsx)(T.E,{className:"h-6 w-32"}),(0,f.jsx)(T.E,{className:"h-4 w-48"})]}),(0,f.jsx)(E.Wu,{children:(0,f.jsx)(T.E,{className:"h-64 w-full"})})]})]});let M=()=>!!(h&&(h.apiCalls?.used||h.storage?.usedBytes));return d||o||m?(0,f.jsxs)("div",{className:"container mx-auto py-8",children:[(0,f.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,f.jsx)("h1",{className:"text-display",children:"Uso de API"}),(0,f.jsxs)(N.Button,{onClick:w,disabled:t,variant:"outline",size:"sm",className:"h-9",children:[(0,f.jsx)(z.A,{className:`w-4 h-4 mr-2 ${t?"animate-spin":""}`}),"Actualizar"]})]}),(0,f.jsxs)(L.Fc,{variant:"destructive",className:"mb-6",children:[(0,f.jsx)(F.A,{className:"h-4 w-4"}),(0,f.jsx)(L.XL,{children:"Error al cargar datos"}),(0,f.jsx)(L.TN,{children:("string"==typeof d?d:d?.message)||("string"==typeof o?o:o?.message)||("string"==typeof m?m:m?.message)||"Ocurri\xf3 un error al cargar los datos de uso."})]})]}):(0,f.jsxs)("div",{className:"container mx-auto py-8",children:[(0,f.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h1",{className:"text-display mb-2",children:"Uso de API"}),(0,f.jsx)("p",{className:"text-muted-foreground",children:"Monitorea el uso de tu API y almacenamiento en tiempo real"})]}),(0,f.jsx)("div",{className:"flex items-center space-x-2",children:(0,f.jsxs)(N.Button,{onClick:w,disabled:t,variant:"outline",size:"sm",className:"h-9",children:[(0,f.jsx)(z.A,{className:`w-4 h-4 mr-2 ${t?"animate-spin":""}`}),"Actualizar"]})})]}),(0,f.jsx)("div",{className:"mb-6",children:(0,f.jsx)(hP,{onChange:t=>{a(t)},className:"w-auto"})}),(0,f.jsxs)("div",{className:"mb-6 flex items-center text-sm text-muted-foreground",children:[(0,f.jsx)(U.mm,{icon:W.A,size:"sm",context:"muted",className:"mr-1"}),(0,f.jsxs)("span",{children:["\xdaltima actualizaci\xf3n: ","Hace unos momentos"]})]}),(0,f.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,f.jsxs)(E.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-blue-100 dark:hover:border-blue-900",children:[(0,f.jsxs)(E.aR,{className:"pb-2",children:[(0,f.jsxs)(E.ZB,{className:"flex items-center text-lg",children:[(0,f.jsx)(U.mm,{icon:B.A,size:"md",context:"primary",className:"mr-2"}),"Llamadas a la API"]}),(0,f.jsxs)(E.BT,{children:["Total en el periodo: ",r.from?(0,b.GP)(r.from,"dd/MM/yy"):"inicio"," - ",r.to?(0,b.GP)(r.to,"dd/MM/yy"):"fin"]})]}),(0,f.jsxs)(E.Wu,{children:[(0,f.jsx)("div",{className:"text-metric text-center py-2 text-primary",children:(0,n1.ZV)(h?.apiCalls?.used||0)}),(0,f.jsxs)("div",{className:"mt-2",children:[(0,f.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,f.jsx)("span",{children:"Uso actual"}),(0,f.jsx)("span",{className:"font-medium",children:(0,n1.ZV)(h?.apiCalls?.used||0)})]}),(0,f.jsx)(A.k,{value:Math.min((h?.apiCalls?.used||0)/(h?.apiCalls?.limit||1e4)*100,100),className:"h-1.5 rayuela-progress-glow"}),(0,f.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mt-2",children:[(0,f.jsxs)("div",{className:"flex items-center",children:[(0,f.jsx)(U.mm,{icon:W.A,size:"xs",context:"muted",className:"mr-1"}),(0,f.jsx)("span",{children:"Pr\xf3ximo reset:"})]}),(0,f.jsx)("span",{className:"font-medium",children:h?.apiCalls?.resetDate?(0,b.GP)(v(h.apiCalls.resetDate),"dd/MM/yy HH:mm"):"No disponible"})]})]})]})]}),(0,f.jsxs)(E.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-green-100 dark:hover:border-green-900",children:[(0,f.jsxs)(E.aR,{className:"pb-2",children:[(0,f.jsxs)(E.ZB,{className:"flex items-center text-lg",children:[(0,f.jsx)(U.mm,{icon:V.A,size:"md",context:"primary",className:"mr-2"}),"Almacenamiento",(0,f.jsx)(hO.Bc,{children:(0,f.jsxs)(hO.m_,{children:[(0,f.jsx)(hO.k$,{asChild:!0,children:(0,f.jsx)(U.mm,{icon:H.A,size:"sm",context:"muted",className:"ml-1 cursor-help"})}),(0,f.jsx)(hO.ZI,{children:(0,f.jsx)("p",{className:"text-xs",children:"Datos almacenados en tu cuenta"})})]})})]}),(0,f.jsxs)(E.BT,{children:["Espacio utilizado al ",r.to?(0,b.GP)(r.to,"dd/MM/yy"):"final del periodo"]})]}),(0,f.jsxs)(E.Wu,{children:[(0,f.jsx)("div",{className:"text-metric text-center py-2 text-success",children:(0,n1.z3)(h?.storage?.usedBytes||0)}),(0,f.jsxs)("div",{className:"mt-2",children:[(0,f.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,f.jsx)("span",{children:"Uso actual"}),(0,f.jsxs)("div",{className:"flex items-center",children:[(0,f.jsx)("span",{className:"font-medium",children:(0,n1.z3)(h?.storage?.usedBytes||0)}),(0,f.jsxs)("span",{className:"ml-1",children:["/ ",h?.storage?.limitBytes?(0,n1.z3)(h.storage.limitBytes):"∞"]})]})]}),(0,f.jsx)(A.k,{value:h?.storage?.percentage||0,className:"h-1.5 rayuela-progress-glow"}),(0,f.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mt-2",children:[(0,f.jsxs)("div",{className:"flex items-center",children:[(0,f.jsx)(U.mm,{icon:W.A,size:"xs",context:"muted",className:"mr-1"}),(0,f.jsx)("span",{children:"\xdaltima medici\xf3n:"})]}),(0,f.jsx)("span",{className:"font-medium",children:h?.storage?.lastMeasured?(0,b.GP)(v(h.storage.lastMeasured),"dd/MM/yy HH:mm"):"No disponible"})]})]})]})]}),(0,f.jsxs)(E.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-purple-100 dark:hover:border-purple-900",children:[(0,f.jsxs)(E.aR,{className:"pb-2",children:[(0,f.jsxs)(E.ZB,{className:"flex items-center text-lg",children:[(0,f.jsx)(U.mm,{icon:Y.A,size:"md",context:"primary",className:"mr-2"}),"Estado de la Cuenta"]}),(0,f.jsx)(E.BT,{children:"Informaci\xf3n general de tu cuenta"})]}),(0,f.jsxs)(E.Wu,{children:[(0,f.jsxs)("div",{className:"flex flex-col space-y-3 py-2",children:[(0,f.jsxs)("div",{className:"flex justify-between items-center",children:[(0,f.jsxs)("div",{className:"flex items-center text-sm",children:[(0,f.jsx)(U.mm,{icon:Y.A,size:"sm",context:"primary",className:"mr-1.5"}),(0,f.jsx)("span",{className:"text-muted-foreground",children:"Plan actual:"})]}),(0,f.jsx)(j.E,{variant:"default",className:"text-xs",children:n?.plan||"B\xe1sico"})]}),(0,f.jsxs)("div",{className:"flex justify-between items-center",children:[(0,f.jsxs)("div",{className:"flex items-center text-sm",children:[(0,f.jsx)(U.mm,{icon:V.A,size:"sm",context:"primary",className:"mr-1.5"}),(0,f.jsx)("span",{className:"text-muted-foreground",children:"Datos disponibles:"})]}),(0,f.jsx)("span",{className:"font-medium text-sm",children:M()?"S\xed":"No"})]})]}),!M()&&(0,f.jsxs)("div",{className:"flex flex-col items-center justify-center py-4 text-center",children:[(0,f.jsx)(U.mm,{icon:Y.A,size:"xl",context:"muted",className:"mb-2"}),(0,f.jsx)("p",{className:"text-muted-foreground font-medium text-sm",children:"No hay datos disponibles"}),(0,f.jsx)("p",{className:"text-xs text-muted-foreground text-center max-w-md",children:"Los datos de uso se mostrar\xe1n aqu\xed una vez que comiences a usar la API."})]})]})]})]}),(0,f.jsxs)(E.Zp,{className:"mb-8",children:[(0,f.jsxs)(E.aR,{children:[(0,f.jsxs)(E.ZB,{className:"flex items-center",children:[(0,f.jsx)(U.mm,{icon:$.A,size:"md",context:"primary",className:"mr-2"}),"Historial de Uso"]}),(0,f.jsx)(E.BT,{children:"Tendencias de uso de la API durante el periodo seleccionado"})]}),(0,f.jsxs)(E.Wu,{children:[(0,f.jsx)("div",{className:"mb-4 p-3 bg-info-light rounded-lg border border-info/20",children:(0,f.jsxs)("p",{className:"text-sm flex items-start",children:[(0,f.jsx)(U.mm,{icon:Y.A,size:"md",context:"info",className:"mr-2 shrink-0 mt-0.5"}),(0,f.jsx)("span",{children:"El gr\xe1fico muestra las tendencias de uso de tu API durante el periodo seleccionado. Los datos se actualizan autom\xe1ticamente y reflejan las llamadas realizadas y el almacenamiento utilizado."})]})}),(0,f.jsx)(n5,{data:i,isLoading:y,error:m})]})]}),(0,f.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,f.jsx)(Z.s,{priceId:"default_price_id",planName:"Pro"}),(0,f.jsx)(X.q,{})]})]})}var hT=i(95282),hN=i.n(hT);function hA(){return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsxs)(hN(),{children:[(0,f.jsx)("title",{children:"Panel de Uso | Rayuela API"}),(0,f.jsx)("meta",{name:"description",content:"Monitorea el uso de tu API, visualiza estad\xedsticas y administra tus l\xedmites de consumo."})]}),(0,f.jsx)(hE,{})]})}},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:t=>{"use strict";t.exports=require("assert")},13668:(t,e,i)=>{"use strict";i.d(e,{KC:()=>p,Yq:()=>g,ZV:()=>u,a3:()=>m,cR:()=>f,z3:()=>c});var s=i(60687);i(43210);var r=i(96834),a=i(5336),n=i(78122),o=i(48730),l=i(35071),h=i(97840),d=i(43649);function c(t,e=2){if(0===t)return"0 Bytes";let i=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,i)).toFixed(e<0?0:e))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][i]}function u(t){return new Intl.NumberFormat().format(t)}function f(t){let e={className:"h-4 w-4"};switch(t?.toLowerCase()){case"completed":case"success":case"finished":return(0,s.jsx)(a.A,{...e,className:"h-4 w-4 text-green-500"});case"running":case"processing":case"in_progress":return(0,s.jsx)(n.A,{...e,className:"h-4 w-4 text-blue-500 animate-spin"});case"pending":case"queued":case"waiting":return(0,s.jsx)(o.A,{...e,className:"h-4 w-4 text-yellow-500"});case"failed":case"error":case"cancelled":return(0,s.jsx)(l.A,{...e,className:"h-4 w-4 text-red-500"});case"starting":case"initializing":return(0,s.jsx)(h.A,{...e,className:"h-4 w-4 text-blue-400"});case"warning":return(0,s.jsx)(d.A,{...e,className:"h-4 w-4 text-amber-500"});default:return(0,s.jsx)(o.A,{...e,className:"h-4 w-4 text-gray-400"})}}function p(t){switch(t?.toLowerCase()){case"completed":case"success":case"finished":return(0,s.jsx)(r.E,{variant:"success",className:"text-xs",children:"Completado"});case"running":case"processing":case"in_progress":return(0,s.jsx)(r.E,{variant:"info",className:"text-xs",children:"En progreso"});case"pending":case"queued":case"waiting":return(0,s.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Pendiente"});case"failed":case"error":case"cancelled":return(0,s.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"Fallido"});case"starting":case"initializing":return(0,s.jsx)(r.E,{variant:"secondary",className:"text-xs",children:"Iniciando"});case"warning":return(0,s.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Advertencia"});default:return(0,s.jsx)(r.E,{variant:"outline",className:"text-xs",children:"Desconocido"})}}function g(t,e=!0){if(!t)return"No disponible";try{let i=new Date(t),s={year:"numeric",month:"long",day:"numeric",...e&&{hour:"2-digit",minute:"2-digit"}};return new Intl.DateTimeFormat("es-ES",s).format(i)}catch(t){return console.error("Error al formatear fecha:",t),"Formato de fecha inv\xe1lido"}}function m(t){let e=Math.floor(t/1e3),i=Math.floor(e/60),s=Math.floor(i/60),r=Math.floor(s/24);return r>0?`${r}d ${s%24}h ${i%60}m`:s>0?`${s}h ${i%60}m ${e%60}s`:i>0?`${i}m ${e%60}s`:`${e}s`}},14952:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:t=>{"use strict";t.exports=require("os")},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:t=>{"use strict";t.exports=require("path")},35071:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42074:(t,e,i)=>{Promise.resolve().then(i.bind(i,70020))},43125:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},48730:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65668:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},70020:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\usage\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\usage\\page.tsx","default")},74075:t=>{"use strict";t.exports=require("zlib")},76242:(t,e,i)=>{"use strict";i.d(e,{Bc:()=>n,ZI:()=>h,k$:()=>l,m_:()=>o});var s=i(60687);i(43210);var r=i(46442),a=i(4780);function n({delayDuration:t=0,...e}){return(0,s.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...e})}function o({...t}){return(0,s.jsx)(n,{children:(0,s.jsx)(r.bL,{"data-slot":"tooltip",...t})})}function l({...t}){return(0,s.jsx)(r.l9,{"data-slot":"tooltip-trigger",...t})}function h({className:t,sideOffset:e=0,children:i,...n}){return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:e,className:(0,a.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...n,children:[i,(0,s.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},78639:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>h});var s=i(65239),r=i(48088),a=i(88170),n=i.n(a),o=i(30893),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let h={children:["",{children:["(dashboard)",{children:["usage",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,70020)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\usage\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\usage\\page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/usage/page",pathname:"/usage",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},79551:t=>{"use strict";t.exports=require("url")},81630:t=>{"use strict";t.exports=require("http")},82242:(t,e,i)=>{Promise.resolve().then(i.bind(i,5045))},83997:t=>{"use strict";t.exports=require("tty")},94652:(t,e,i)=>{"use strict";i.d(e,{q:()=>c});var s=i(60687),r=i(43210),a=i(29523),n=i(44957),o=i(62185),l=i(52581),h=i(43125),d=i(25334);function c({children:t,className:e,variant:i="outline",...c}){let{token:u,apiKey:f}=(0,n.A)(),[p,g]=(0,r.useState)(!1),m=async()=>{if(!u||!f)return void l.o.error("Debes iniciar sesi\xf3n para realizar esta acci\xf3n");g(!0);try{let t=await (0,o.oE)();if(t.url)window.location.href=t.url;else throw Error("No se recibi\xf3 una URL de redirecci\xf3n")}catch(t){console.error("Error al crear sesi\xf3n del Portal de Facturaci\xf3n:",t),l.o.error(t.message||"Error al acceder al portal de facturaci\xf3n"),g(!1)}};return(0,s.jsx)(a.Button,{onClick:m,disabled:p,className:e,variant:i,...c,children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Redirigiendo..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),t||"Gestionar Facturaci\xf3n"]})})}},94735:t=>{"use strict";t.exports=require("events")},95282:(t,e)=>{"use strict";function i(){return null}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return i}}),("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},96882:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97840:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},97930:(t,e,i)=>{"use strict";i.d(e,{s:()=>c});var s=i(60687),r=i(43210),a=i(29523),n=i(44957),o=i(62185),l=i(52581),h=i(43125),d=i(85778);function c({priceId:t,planName:e,actionType:i="subscribe",children:c,className:u,variant:f="default",...p}){let{token:g,apiKey:m}=(0,n.A)(),[x,b]=(0,r.useState)(!1),y=async()=>{if(!g||!m)return void l.o.error("Debes iniciar sesi\xf3n para realizar esta acci\xf3n");b(!0);try{if("contact"===i){window.location.href="/contact-sales";return}let e=await (0,o.fw)(t);if(e.url)window.location.href=e.url;else throw Error("No se recibi\xf3 una URL de redirecci\xf3n")}catch(t){console.error("Error al crear sesi\xf3n de checkout:",t),l.o.error(t.message||`Error al procesar la suscripci\xf3n al plan ${e}`),b(!1)}};return(0,s.jsx)(a.Button,{onClick:y,disabled:x,className:u,variant:f,...p,children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Procesando..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),c||("upgrade"===i?`Actualizar a ${e}`:"downgrade"===i?`Cambiar a ${e}`:"contact"===i?"Contactar con Ventas":`Suscribirse a ${e}`)]})})}}};var e=require("../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[4447,2713,5814,5423,1576,7400,5077,5552,2807,5320,3525,3658],()=>i(78639));module.exports=s})();