"use strict";exports.id=3658,exports.ids=[3658],exports.modules={59327:(e,r,t)=>{t.d(r,{h:()=>c});var o=t(52581),a=t(51060);class n extends Error{constructor(e,r,t,o){super(e),this.status=r,this.errorCode=t,this.details=o,this.name="ApiError"}static isApiError(e){return e instanceof n}static fromResponse(e){return new n(e.message,e.status_code,e.error_code,e.details)}}let i=a.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>e),i.interceptors.response.use(e=>e,e=>{if(e.response){let r=e.response.data;throw n.fromResponse(r)}if(e.request)throw new n("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new n(e.message,0,"REQUEST_ERROR",null)});var s=t(85814),l=t.n(s),d=t(43210),u=t.n(d);function c(e,r="Ha ocurrido un error"){return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof n)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void o.o.error(u().createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",u().createElement(l(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void o.o.error(u().createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",u().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void o.o.error(u().createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",u().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void o.o.error(u().createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",u().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void o.o.error(u().createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",u().createElement(l(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void o.o.error(u().createElement("div",{},"Error de validacion: "+e.message+". ",u().createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void o.o.error(u().createElement("div",{},"Datos insuficientes para generar recomendaciones. ",u().createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void o.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(o.o.error(e.message||r),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void o.o.error(e.message||r):void(o.o.error(r),console.groupEnd())}},98690:(e,r,t)=>{t.d(r,{tU:()=>Q,av:()=>Z,j7:()=>Y,Xi:()=>W});var o=t(60687),a=t(43210),n=t(70569),i=t(11273),s=t(9510),l=t(98599),d=t(96963);t(51215);var u=t(8730),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,u.TL)(`Primitive.${r}`),n=a.forwardRef((e,a)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?t:r,{...i,ref:a})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{}),f=t(13495),m=t(65551),p=t(43),v="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[h,E,w]=(0,s.N)(g),[y,I]=(0,i.A)(g,[w]),[R,x]=y(g),A=a.forwardRef((e,r)=>(0,o.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(N,{...e,ref:r})})}));A.displayName=g;var N=a.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:i,loop:s=!1,dir:d,currentTabStopId:u,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:w,onEntryFocus:y,preventScrollOnEntryFocus:I=!1,...x}=e,A=a.useRef(null),N=(0,l.s)(r,A),C=(0,p.jH)(d),[T,j]=(0,m.i)({prop:u,defaultProp:h??null,onChange:w,caller:g}),[F,_]=a.useState(!1),L=(0,f.c)(y),P=E(t),S=a.useRef(!1),[k,K]=a.useState(0);return a.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(v,L),()=>e.removeEventListener(v,L)},[L]),(0,o.jsx)(R,{scope:t,orientation:i,dir:C,loop:s,currentTabStopId:T,onItemFocus:a.useCallback(e=>j(e),[j]),onItemShiftTab:a.useCallback(()=>_(!0),[]),onFocusableItemAdd:a.useCallback(()=>K(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>K(e=>e-1),[]),children:(0,o.jsx)(c.div,{tabIndex:F||0===k?-1:0,"data-orientation":i,...x,ref:N,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let r=!S.current;if(e.target===e.currentTarget&&r&&!F){let r=new CustomEvent(v,b);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=P().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),I)}}S.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>_(!1))})})}),C="RovingFocusGroupItem",T=a.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:i=!0,active:s=!1,tabStopId:l,children:u,...f}=e,m=(0,d.B)(),p=l||m,v=x(C,t),b=v.currentTabStopId===p,g=E(t),{onFocusableItemAdd:w,onFocusableItemRemove:y}=v;return a.useEffect(()=>{if(i)return w(),()=>y()},[i,w,y]),(0,o.jsx)(h.ItemSlot,{scope:t,id:p,focusable:i,active:s,children:(0,o.jsx)(c.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...f,ref:r,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var o;let a=(o=e.key,"rtl"!==t?o:"ArrowLeft"===o?"ArrowRight":"ArrowRight"===o?"ArrowLeft":o);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(a)))return j[a]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let o=t.indexOf(e.currentTarget);t=v.loop?function(e,r){return e.map((t,o)=>e[(r+o)%e.length])}(t,o+1):t.slice(o+1)}setTimeout(()=>D(t))}}),children:"function"==typeof u?u({isCurrentTabStop:b}):u})})});T.displayName=C;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e,r=!1){let t=document.activeElement;for(let o of e)if(o===t||(o.focus({preventScroll:r}),document.activeElement!==t))return}var F=t(46059),_=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,u.TL)(`Primitive.${r}`),n=a.forwardRef((e,a)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?t:r,{...i,ref:a})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{}),L="Tabs",[P,S]=(0,i.A)(L,[I]),k=I(),[K,U]=P(L),M=a.forwardRef((e,r)=>{let{__scopeTabs:t,value:a,onValueChange:n,defaultValue:i,orientation:s="horizontal",dir:l,activationMode:u="automatic",...c}=e,f=(0,p.jH)(l),[v,b]=(0,m.i)({prop:a,onChange:n,defaultProp:i??"",caller:L});return(0,o.jsx)(K,{scope:t,baseId:(0,d.B)(),value:v,onValueChange:b,orientation:s,dir:f,activationMode:u,children:(0,o.jsx)(_.div,{dir:f,"data-orientation":s,...c,ref:r})})});M.displayName=L;var z="TabsList",H=a.forwardRef((e,r)=>{let{__scopeTabs:t,loop:a=!0,...n}=e,i=U(z,t),s=k(t);return(0,o.jsx)(A,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:a,children:(0,o.jsx)(_.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:r})})});H.displayName=z;var O="TabsTrigger",V=a.forwardRef((e,r)=>{let{__scopeTabs:t,value:a,disabled:i=!1,...s}=e,l=U(O,t),d=k(t),u=G(l.baseId,a),c=q(l.baseId,a),f=a===l.value;return(0,o.jsx)(T,{asChild:!0,...d,focusable:!i,active:f,children:(0,o.jsx)(_.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":c,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...s,ref:r,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;f||i||!e||l.onValueChange(a)})})})});V.displayName=O;var $="TabsContent",B=a.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,forceMount:i,children:s,...l}=e,d=U($,t),u=G(d.baseId,n),c=q(d.baseId,n),f=n===d.value,m=a.useRef(f);return a.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,o.jsx)(F.C,{present:i||f,children:({present:t})=>(0,o.jsx)(_.div,{"data-state":f?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!t,id:c,tabIndex:0,...l,ref:r,style:{...e.style,animationDuration:m.current?"0s":void 0},children:t&&s})})});function G(e,r){return`${e}-trigger-${r}`}function q(e,r){return`${e}-content-${r}`}B.displayName=$;var X=t(4780);let Q=M,Y=a.forwardRef(({className:e,...r},t)=>(0,o.jsx)(H,{ref:t,className:(0,X.cn)("inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...r}));Y.displayName=H.displayName;let W=a.forwardRef(({className:e,...r},t)=>(0,o.jsx)(V,{ref:t,className:(0,X.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...r}));W.displayName=V.displayName;let Z=a.forwardRef(({className:e,...r},t)=>(0,o.jsx)(B,{ref:t,className:(0,X.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...r}));Z.displayName=B.displayName}};