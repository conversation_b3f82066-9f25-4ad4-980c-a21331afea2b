# Build stage
FROM python:3.12-slim AS builder

WORKDIR /app

# Install build dependencies including PostgreSQL dev libs
RUN apt-get update && apt-get install -y --no-install-recommends \
	build-essential \
	gcc \
	g++ \
	gfortran \
	libgomp1 \
	libopenblas-dev \
	libpq-dev \
	pkg-config \
	&& rm -rf /var/lib/apt/lists/*

# Copy requirements and add gunicorn and psycopg2 for migrations
COPY requirements.txt .
RUN echo "gunicorn==21.2.0" >> requirements.txt && \
    echo "psycopg2-binary==2.9.9" >> requirements.txt

# Build wheels for all dependencies
RUN pip wheel --no-cache-dir --no-deps --wheel-dir /app/wheels -r requirements.txt

# Final stage - optimized for production
FROM python:3.12-slim

# Install runtime dependencies including PostgreSQL client libs
RUN apt-get update && apt-get install -y --no-install-recommends \
	libgomp1 \
	libopenblas0 \
	libpq5 \
	curl \
	&& apt-get clean \
	&& rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -u 1000 appuser

WORKDIR /app

# Copy wheels and requirements from builder stage
COPY --from=builder /app/wheels /wheels
COPY --from=builder /app/requirements.txt .

# Install ALL dependencies from wheels
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir /wheels/* && \
    pip install --no-cache-dir asgiref==3.8.1 && \
    pip install --no-cache-dir itsdangerous && \
    pip install --no-cache-dir aiohttp && \
    rm -rf /wheels /root/.cache/pip/*

# Critical dependency verification during build
RUN python -c "import fastapi, uvicorn, sqlalchemy, asyncpg, pydantic, dotenv, redis, celery; print('✅ All critical imports successful during build!')"

# Copy only necessary application code
COPY ./src ./src
COPY ./alembic ./alembic
COPY ./scripts ./scripts
COPY alembic.ini .
COPY main.py .
COPY gunicorn_conf.py .
COPY health_server.py .
COPY start.sh .
COPY test_deps.py .
COPY deployment_check.py .
COPY check_deployment.py .
COPY fix_migration_version.py .
COPY start_beat_with_health.sh .
COPY start_worker_with_health.sh .

# Make scripts executable and change ownership of files
RUN chmod +x start_beat_with_health.sh && \
    chmod +x start_worker_with_health.sh && \
    chmod +x start.sh && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Final dependency test as non-root user
RUN python test_deps.py

# Environment variables for production
ENV ENV=production \
	PYTHONUNBUFFERED=1 \
	PYTHONDONTWRITEBYTECODE=1 \
	API_HOST=0.0.0.0

# Cloud Run will set PORT environment variable
# Default to 8080 for local development
ENV PORT=8080

# Expose application port
EXPOSE ${PORT}

# Health check
HEALTHCHECK CMD curl --fail http://localhost:${PORT}/health || exit 1

# Use gunicorn with uvicorn workers for production
CMD ["gunicorn", "main:app", "-c", "gunicorn_conf.py"]