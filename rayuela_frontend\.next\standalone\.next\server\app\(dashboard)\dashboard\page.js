(()=>{var e={};e.id=6337,e.ids=[6337],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4998:(e,a,s)=>{"use strict";s.d(a,{default:()=>V});var t=s(60687),r=s(43210),i=s(62244);s(62185);var n=s(29523),o=s(91821),l=s(44493),d=s(85726),c=s(19959),m=s(82080),x=s(53411),p=s(11860),u=s(85778),h=s(85814),g=s.n(h),j=s(14719),f=s(93613),y=s(62688);let b=(0,y.A)("server-crash",[["path",{d:"M6 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-2",key:"4b9dqc"}],["path",{d:"M6 14H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-2",key:"22nnkd"}],["path",{d:"M6 6h.01",key:"1utrut"}],["path",{d:"M6 18h.01",key:"uhywen"}],["path",{d:"m13 6-4 6h6l-4 6",key:"14hqih"}]]);var v=s(76242);function N({className:e=""}){let[a,s]=(0,r.useState)("loading"),[i,n]=(0,r.useState)(null),o=(()=>{switch(a){case"operational":return{label:"API Operativa",icon:(0,t.jsx)(j.A,{className:"h-4 w-4 text-green-500"}),description:"Todos los sistemas funcionando correctamente",className:"text-green-600 dark:text-green-400"};case"degraded":return{label:"Rendimiento Degradado",icon:(0,t.jsx)(f.A,{className:"h-4 w-4 text-amber-500"}),description:"Algunos servicios pueden experimentar lentitud",className:"text-amber-600 dark:text-amber-400"};case"outage":return{label:"Servicio Interrumpido",icon:(0,t.jsx)(b,{className:"h-4 w-4 text-red-500"}),description:"Estamos experimentando problemas t\xe9cnicos",className:"text-red-600 dark:text-red-400"};default:return{label:"Verificando estado...",icon:(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-400 animate-pulse"}),description:"Obteniendo informaci\xf3n del estado de la API",className:"text-gray-600 dark:text-gray-400"}}})(),l=i?i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):"";return(0,t.jsx)(v.Bc,{children:(0,t.jsxs)(v.m_,{children:[(0,t.jsx)(v.k$,{asChild:!0,children:(0,t.jsxs)("div",{className:`flex items-center gap-1.5 ${e}`,children:[o.icon,(0,t.jsx)("span",{className:`text-xs font-medium ${o.className}`,children:o.label})]})}),(0,t.jsx)(v.ZI,{side:"bottom",children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("p",{className:"font-medium",children:o.label}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o.description}),i&&(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["\xdaltima verificaci\xf3n: ",l]})]})})]})})}var k=s(56896);let w=(0,y.A)("code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);var A=s(13964),_=s(70615),P=s(61611);let E=(0,y.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var C=s(78122),I=s(5336),S=s(25334);let D=(0,y.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var T=s(96834),R=s(37276),z=s(16825);let q=({title:e,language:a,code:s,apiKey:i})=>{let[o,l]=(0,r.useState)(!1),d=i?s.replace(/YOUR_API_KEY/g,i):s;return(0,t.jsxs)("div",{className:"mt-3 ml-6 border rounded-md bg-gray-50 dark:bg-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(w,{className:"h-3 w-3 mr-1 text-gray-500"}),(0,t.jsx)("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:e}),(0,t.jsx)(T.E,{variant:"outline",className:"ml-2 text-[10px] px-1 py-0 h-4",children:a})]}),(0,t.jsx)(n.Button,{variant:"ghost",size:"sm",onClick:()=>{navigator.clipboard.writeText(d).then(()=>{l(!0),setTimeout(()=>l(!1),2e3)}).catch(e=>{console.error("Error al copiar:",e)})},className:"h-6 px-2 text-xs",children:o?(0,t.jsx)(A.A,{className:"h-3 w-3"}):(0,t.jsx)(_.A,{className:"h-3 w-3"})})]}),(0,t.jsx)("pre",{className:"p-3 text-xs overflow-x-auto",children:(0,t.jsx)("code",{className:"text-gray-800 dark:text-gray-200",children:d})})]})},$=({item:e,onToggle:a,onRefresh:s,apiKey:r,codeSnippets:i,colorScheme:n})=>{let o={blue:{bg:"bg-info-light/50",checkbox:"bg-info border-info",badge:"bg-info-light",link:"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"},purple:{bg:"bg-purple-50/50 dark:bg-purple-900/10",checkbox:"bg-purple-600 border-purple-600",badge:"bg-purple-50 dark:bg-purple-900/30",link:"text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"},amber:{bg:"bg-warning-light/50",checkbox:"bg-warning border-warning",badge:"bg-warning-light",link:"text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300"}}[n];return(0,t.jsxs)("li",{className:`flex items-start p-2 rounded-md transition-colors ${e.completed?o.bg:"hover:bg-gray-50 dark:hover:bg-gray-800/50"}`,children:[(0,t.jsx)("div",{className:"flex h-5 items-center mr-3",children:(0,t.jsx)(k.S,{id:e.id,checked:e.completed,onCheckedChange:()=>a(e.id),className:e.completed?o.checkbox:""})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[e.icon,(0,t.jsx)("label",{htmlFor:e.id,className:`ml-2 text-sm font-medium ${e.completed?"text-gray-500":"text-gray-900 dark:text-gray-100"}`,children:e.label}),e.autoDetect&&(0,t.jsx)(v.Bc,{children:(0,t.jsxs)(v.m_,{children:[(0,t.jsx)(v.k$,{asChild:!0,children:(0,t.jsx)(T.E,{variant:"outline",className:`ml-2 text-[10px] px-1 py-0 h-4 ${o.badge}`,children:"Auto"})}),(0,t.jsx)(v.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:"Este paso se verifica autom\xe1ticamente"})})]})}),e.completed&&(0,t.jsx)(I.A,{className:"h-3.5 w-3.5 ml-2 text-success"})]}),e.docsLink&&(0,t.jsx)(v.Bc,{children:(0,t.jsxs)(v.m_,{children:[(0,t.jsx)(v.k$,{asChild:!0,children:(0,t.jsxs)("a",{href:e.docsLink,className:`text-xs ${o.link} flex items-center`,target:"_blank",rel:"noopener noreferrer",children:[(0,t.jsx)(m.A,{className:"h-3 w-3 mr-1"}),e.docsTitle||"Documentaci\xf3n"]})}),(0,t.jsx)(v.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:e.tooltipContent||"Ver documentaci\xf3n detallada"})})]})})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-0.5 ml-6",children:e.description}),(0,t.jsxs)("div",{className:"mt-2 ml-6 flex items-center",children:[e.isExternal?(0,t.jsxs)("a",{href:e.link,target:"_blank",rel:"noopener noreferrer",className:`text-xs ${o.link} font-medium flex items-center`,children:[(0,t.jsx)(S.A,{className:"h-3 w-3 mr-1"}),"Ver gu\xeda paso a paso"]}):(0,t.jsxs)(g(),{href:e.link,className:`text-xs ${o.link} font-medium flex items-center`,children:[(0,t.jsx)(D,{className:"h-3 w-3 mr-1"}),"Ir a ",e.label]}),e.autoDetect&&!e.completed&&(0,t.jsxs)("button",{onClick:s,className:"ml-3 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 flex items-center",title:"Verificar progreso",children:[(0,t.jsx)(C.A,{className:"h-3 w-3 mr-1"}),"Verificar"]})]}),"send_catalog_data"===e.id&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(q,{title:"Cargar productos (cURL)",language:"bash",code:i.catalog.curl,apiKey:r}),(0,t.jsx)(q,{title:"Cargar productos (Python)",language:"python",code:i.catalog.python,apiKey:r}),(0,t.jsx)(q,{title:"Cargar productos (JavaScript)",language:"javascript",code:i.catalog.javascript,apiKey:r})]}),"send_interaction_data"===e.id&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(q,{title:"Cargar interacciones (cURL)",language:"bash",code:i.interactions.curl,apiKey:r}),(0,t.jsx)(q,{title:"Cargar interacciones (Python)",language:"python",code:i.interactions.python,apiKey:r}),(0,t.jsx)(q,{title:"Cargar interacciones (JavaScript)",language:"javascript",code:i.interactions.javascript,apiKey:r})]}),"first_recommendation"===e.id&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(q,{title:"Obtener recomendaciones (cURL)",language:"bash",code:i.recommendations.curl,apiKey:r}),(0,t.jsx)(q,{title:"Obtener recomendaciones (Python)",language:"python",code:i.recommendations.python,apiKey:r}),(0,t.jsx)(q,{title:"Obtener recomendaciones (JavaScript)",language:"javascript",code:i.recommendations.javascript,apiKey:r})]})]})]})},K=({className:e})=>(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:e,children:[(0,t.jsx)("line",{x1:"10",y1:"6",x2:"21",y2:"6"}),(0,t.jsx)("line",{x1:"10",y1:"12",x2:"21",y2:"12"}),(0,t.jsx)("line",{x1:"10",y1:"18",x2:"21",y2:"18"}),(0,t.jsx)("polyline",{points:"3 6 4 7 6 5"}),(0,t.jsx)("polyline",{points:"3 12 4 13 6 11"}),(0,t.jsx)("polyline",{points:"3 18 4 19 6 17"})]}),O=()=>{let{apiKey:e}=(0,i.As)(),a="http://localhost:8001",s={catalog:{curl:`curl -X POST "${a}/ingestion/batch" \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "products": [
      {
        "external_id": "P001",
        "name": "Smartphone XYZ",
        "category": "Electronics",
        "price": 599.99,
        "description": "Un smartphone de \xfaltima generaci\xf3n",
        "image_url": "https://ejemplo.com/images/p001.jpg"
      }
    ]
  }'`,python:`import requests

url = "${a}/ingestion/batch"
headers = {
    "X-API-Key": "YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "products": [
        {
            "external_id": "P001",
            "name": "Smartphone XYZ",
            "category": "Electronics",
            "price": 599.99,
            "description": "Un smartphone de \xfaltima generaci\xf3n",
            "image_url": "https://ejemplo.com/images/p001.jpg"
        }
    ]
}

response = requests.post(url, headers=headers, json=data)
print(response.json())`,javascript:`const response = await fetch('${a}/ingestion/batch', {
  method: 'POST',
  headers: {
    'X-API-Key': 'YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    products: [
      {
        external_id: 'P001',
        name: 'Smartphone XYZ',
        category: 'Electronics',
        price: 599.99,
        description: 'Un smartphone de \xfaltima generaci\xf3n',
        image_url: 'https://ejemplo.com/images/p001.jpg'
      }
    ]
  })
});

const data = await response.json();
console.log(data);`},interactions:{curl:`curl -X POST "${a}/ingestion/batch" \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "interactions": [
      {
        "user_external_id": "U001",
        "product_external_id": "P001",
        "interaction_type": "view",
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ]
  }'`,python:`import requests
from datetime import datetime

url = "${a}/ingestion/batch"
headers = {
    "X-API-Key": "YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "interactions": [
        {
            "user_external_id": "U001",
            "product_external_id": "P001",
            "interaction_type": "view",
            "timestamp": datetime.now().isoformat() + "Z"
        }
    ]
}

response = requests.post(url, headers=headers, json=data)
print(response.json())`,javascript:`const response = await fetch('${a}/ingestion/batch', {
  method: 'POST',
  headers: {
    'X-API-Key': 'YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    interactions: [
      {
        user_external_id: 'U001',
        product_external_id: 'P001',
        interaction_type: 'view',
        timestamp: new Date().toISOString()
      }
    ]
  })
});

const data = await response.json();
console.log(data);`},recommendations:{curl:`curl -X POST "${a}/recommendations/personalized/query" \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "user_id": 123,
    "limit": 5,
    "strategy": "balanced",
    "model_type": "standard"
  }'`,python:`import requests

url = "${a}/recommendations/personalized/query"
headers = {
    "X-API-Key": "YOUR_API_KEY",
    "Content-Type": "application/json"
}
data = {
    "user_id": 123,
    "limit": 5,
    "strategy": "balanced",
    "model_type": "standard"
}

response = requests.post(url, headers=headers, json=data)
recommendations = response.json()
print(recommendations)`,javascript:`const response = await fetch('${a}/recommendations/personalized/query', {
  method: 'POST',
  headers: {
    'X-API-Key': 'YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    user_id: 123,
    limit: 5,
    strategy: 'balanced',
    model_type: 'standard'
  })
});

const recommendations = await response.json();
console.log(recommendations);`}},[o,d]=(0,r.useState)(!0),[x,u]=(0,r.useState)(!1),[h,g]=(0,r.useState)([{id:"generate_key",label:"He Guardado mi API Key",description:"Confirma que has guardado tu API Key de forma segura. \uD83E\uDDEA En el Developer Sandbox puedes resetear todo y generar nuevas claves cuando necesites.",icon:(0,t.jsx)(c.A,{className:"h-4 w-4 text-info"}),link:"/api-keys",docsLink:"https://docs.rayuela.ai/authentication",docsTitle:"Gu\xeda de Autenticaci\xf3n",completed:!1,autoDetect:!1,tooltipContent:"En el Developer Sandbox puedes experimentar libremente sin preocuparte por costos. Guarda tu API Key pero recuerda que puedes resetear todo cuando necesites empezar fresh.",category:"setup"},{id:"read_docs",label:"Consultar Documentaci\xf3n",description:"Lee la gu\xeda de inicio r\xe1pido para aprender a usar la API",icon:(0,t.jsx)(m.A,{className:"h-4 w-4 text-success"}),link:"https://docs.rayuela.ai/quickstart",docsLink:"https://docs.rayuela.ai/api-reference",docsTitle:"Referencia de API",isExternal:!0,completed:!1,tooltipContent:"Familiar\xedzate con los conceptos b\xe1sicos y la estructura de la API",category:"setup"},{id:"send_catalog_data",label:"Ingresar Datos de Productos",description:"Usa el endpoint /ingestion/batch para cargar tus productos. C\xf3digo listo para copiar incluido abajo.",icon:(0,t.jsx)(P.A,{className:"h-4 w-4 text-purple-500"}),link:"https://docs.rayuela.ai/guides/data_ingestion_guide",docsLink:"https://docs.rayuela.ai/guides/data_ingestion_guide",docsTitle:"Gu\xeda de Ingesta de Datos",isExternal:!0,completed:!1,autoDetect:!0,tooltipContent:"Carga tus productos usando el endpoint de ingesta por lotes. Los ejemplos de c\xf3digo est\xe1n listos para usar con tu API Key.",category:"data"},{id:"send_interaction_data",label:"Ingresar Datos de Interacciones",description:"Usa el endpoint /ingestion/batch para cargar interacciones usuario-producto. C\xf3digo listo para copiar incluido abajo.",icon:(0,t.jsx)(P.A,{className:"h-4 w-4 text-indigo-500"}),link:"https://docs.rayuela.ai/guides/data_ingestion_guide#interacciones-interactions",docsLink:"https://docs.rayuela.ai/guides/data_ingestion_guide",docsTitle:"Gu\xeda de Ingesta de Datos",isExternal:!0,completed:!1,autoDetect:!0,tooltipContent:"Las interacciones (vistas, compras, clicks) son fundamentales para generar recomendaciones personalizadas. Los ejemplos de c\xf3digo est\xe1n listos para usar.",category:"data"},{id:"train_model",label:"Entrenar Primer Modelo",description:"Inicia el entrenamiento de tu modelo de recomendaci\xf3n",icon:(0,t.jsx)(E,{className:"h-4 w-4 text-orange-500"}),link:"https://docs.rayuela.ai/quickstart#entrenamiento-de-modelo",docsLink:"https://docs.rayuela.ai/models",docsTitle:"Gu\xeda de Entrenamiento",isExternal:!0,completed:!1,autoDetect:!0,tooltipContent:"Una vez cargados tus datos, entrena tu primer modelo para comenzar a generar recomendaciones personalizadas.",category:"model"},{id:"first_recommendation",label:"Obtener Recomendaciones",description:"Usa el endpoint /recommendations/personalized/query para obtener recomendaciones. C\xf3digo listo para copiar incluido abajo.",icon:(0,t.jsx)(E,{className:"h-4 w-4 text-warning"}),link:"https://docs.rayuela.ai/quickstart#obtenci\xf3n-de-recomendaciones",docsLink:"https://docs.rayuela.ai/recommendations",docsTitle:"Gu\xeda de Recomendaciones",isExternal:!0,completed:!1,autoDetect:!0,tooltipContent:"Aprende a solicitar recomendaciones personalizadas usando el endpoint de consulta. Los ejemplos de c\xf3digo est\xe1n listos para usar con tu API Key.",category:"model"}]);(0,r.useEffect)(()=>{if(j&&N)try{let e=N();if(e&&Object.keys(e).length>0)g(a=>a.map(a=>({...a,completed:!!e[a.id]}))),e.dismissed&&u(!0);else{let e=localStorage.getItem("gettingStartedChecklist");if(e){let a=JSON.parse(e);g(e=>e.map(e=>({...e,completed:!!a[e.id]})))}let a="true"===localStorage.getItem("dismissedChecklist");u(a)}}catch(a){console.error("Error loading checklist state:",a);let e=localStorage.getItem("gettingStartedChecklist");if(e)try{let a=JSON.parse(e);g(e=>e.map(e=>({...e,completed:!!a[e.id]})))}catch(e){console.error("Error parsing saved checklist from localStorage:",e)}}},[]);let{accountData:j,error:f,isLoading:y,refresh:b,getChecklistStatus:N,updateChecklistStatus:k}=(0,i.T4)({revalidateOnFocus:!0,dedupingInterval:5e3,refreshInterval:15e3});(0,i.Qf)({revalidateOnFocus:!0,dedupingInterval:5e3,refreshInterval:15e3}),(0,r.useEffect)(()=>{j&&N&&(async()=>{try{let e=await N();e&&"object"==typeof e&&g(a=>a.map(a=>({...a,completed:!!e[a.id]})));let a="true"===localStorage.getItem("dismissedChecklist");u(a)}catch(a){console.error("Error loading checklist state:",a);let e=localStorage.getItem("gettingStartedChecklist");if(e)try{let a=JSON.parse(e);g(e=>e.map(e=>({...e,completed:a[e.id]||!1})))}catch(e){console.error("Error parsing saved checklist from localStorage:",e)}}})()},[j,N]);let{usageData:w,error:A,isLoading:_,mutate:S}=(0,i.TB)();(0,r.useEffect)(()=>{(j||w)&&q()},[j,w]);let D=async()=>{try{await Promise.all([b(),S()])}catch(e){console.error("Error al actualizar los datos:",e)}},q=async()=>{if(!j||!w)return;let{updatedItems:e,isNewApiKey:a,hasApiKey:s,hasSentCatalogData:t,hasSentInteractionData:r,hasTrainedModel:i,hasMadeApiCalls:n}=(0,z.Vd)(j,w,h,null),o="true"===localStorage.getItem("seenPostModalHighlight");g(e);let l=(0,z.qC)(e,s,o,t,r,i,n);if(k)try{await k(l)}catch(e){console.error("Error updating checklist status in backend:",e)}localStorage.setItem("gettingStartedChecklist",JSON.stringify(l)),(o||a)&&!localStorage.getItem("dismissedChecklist")&&(localStorage.removeItem("dismissedChecklist"),u(!1)),d(!1)};(0,r.useEffect)(()=>{j&&w?q():y||_||d(!1)},[j,w,y,_]);let O=async e=>{let a=h.map(a=>a.id===e?{...a,completed:!a.completed}:a);g(a);let s={};if(a.forEach(e=>{s[e.id]=e.completed}),k)try{await k(s)}catch(e){console.error("Error updating checklist status in backend:",e)}localStorage.setItem("gettingStartedChecklist",JSON.stringify(s))},L=async()=>{if(u(!0),localStorage.setItem("dismissedChecklist","true"),k)try{let e=N?.()||{};await k({...e,dismissed:!0})}catch(e){console.error("Error updating dismissed status in backend:",e)}};if(x)return null;let B=h.filter(e=>e.completed).length,M=h.length,U=Math.round(B/M*100),Y="true"===localStorage.getItem("seenPostModalHighlight"),Z=(0,z.Io)(Y,!1);return Z&&localStorage.setItem("checklistHighlighted","true"),(0,t.jsxs)(l.Zp,{className:`mb-6 border-2 ${Z?"border-indigo-400 dark:border-indigo-500 shadow-lg animate-pulse":"border-indigo-200 dark:border-indigo-800/40 shadow-md hover:shadow-lg"} transition-all duration-300 ${B<M?"ring-2 ring-indigo-100 dark:ring-indigo-900/20":""}`,children:[(0,t.jsx)(l.aR,{className:`pb-2 ${Z?"bg-indigo-50 dark:bg-indigo-900/20":B<M?"bg-gradient-to-r from-indigo-50 to-transparent dark:from-indigo-900/10 dark:to-transparent":""}`,children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(l.ZB,{className:"text-lg flex items-center",children:[(0,t.jsx)("span",{className:`${Z?"bg-indigo-200 dark:bg-indigo-800":"bg-indigo-100 dark:bg-indigo-900/50"} p-1.5 rounded-md mr-2 transition-colors`,children:(0,t.jsx)(K,{className:"h-5 w-5 text-indigo-600 dark:text-indigo-400"})}),Z?"\xa1Comienza Aqu\xed!":"Primeros Pasos"]}),(0,t.jsxs)(l.BT,{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:Z?"Sigue estos pasos para comenzar a usar tu nueva API Key":"Completa estas tareas para comenzar a usar Rayuela"}),(0,t.jsx)(v.Bc,{children:(0,t.jsxs)(v.m_,{children:[(0,t.jsx)(v.k$,{asChild:!0,children:(0,t.jsx)(n.Button,{variant:"ghost",size:"icon",className:"h-6 w-6 text-indigo-500 hover:text-indigo-700 hover:bg-indigo-50 -mr-2",onClick:D,disabled:y||_,children:(0,t.jsx)(C.A,{className:`h-3.5 w-3.5 ${y||_?"animate-spin":""}`})})}),(0,t.jsx)(v.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:"Actualizar progreso"})})]})})]})]}),(0,t.jsxs)("div",{className:"flex gap-1",children:[!o&&j&&w&&(0,t.jsx)(v.Bc,{children:(0,t.jsxs)(v.m_,{children:[(0,t.jsx)(v.k$,{asChild:!0,children:(0,t.jsxs)(T.E,{variant:"outline",className:"text-[10px] px-1.5 py-0 h-5 bg-gray-50 dark:bg-gray-800 text-gray-500",children:["Actualizado:"," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})}),(0,t.jsx)(v.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:"\xdaltima verificaci\xf3n de progreso"})})]})}),(0,t.jsx)(n.Button,{variant:"ghost",size:"icon",className:"text-gray-400 hover:text-gray-500",onClick:L,children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Progreso"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[B,"/",M," completados"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden",children:(0,t.jsx)("div",{className:`h-2.5 rounded-full transition-all duration-500 ease-in-out ${100===U?"bg-gradient-to-r from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600":U>50?"bg-gradient-to-r from-indigo-500 to-blue-500 dark:from-indigo-600 dark:to-blue-600":"bg-gradient-to-r from-indigo-400 to-indigo-500 dark:from-indigo-500 dark:to-indigo-600"}`,style:{width:`${U}%`}})}),100===U&&(0,t.jsx)("div",{className:"flex justify-center mt-2 text-green-600 dark:text-green-400 text-xs font-medium",children:(0,t.jsx)(R.hf,{icon:(0,t.jsx)(I.A,{className:"h-4 w-4"}),size:"sm",children:"\xa1Configuraci\xf3n completada!"})}),(y||_)&&(0,t.jsx)("div",{className:"flex justify-center py-2 mb-4 mt-2",children:(0,t.jsx)(R.hf,{icon:(0,t.jsx)(C.A,{className:"h-4 w-4 animate-spin text-indigo-500"}),children:(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"Verificando progreso..."})})}),(f||A)&&(0,t.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border-l-2 border-red-500 pl-3 py-2 mb-4 mt-2",children:(0,t.jsxs)("p",{className:"text-xs text-red-600 dark:text-red-400",children:["Error al verificar el progreso.",(0,t.jsx)("button",{onClick:D,className:"ml-2 underline hover:text-red-700 dark:hover:text-red-300",children:"Reintentar"})]})}),(0,t.jsxs)("div",{className:"space-y-6 mt-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2",children:(0,t.jsx)(R.hf,{icon:(0,t.jsx)("span",{className:"bg-blue-100 dark:bg-blue-900/50 p-1 rounded-md",children:(0,t.jsx)(c.A,{className:"h-3.5 w-3.5 text-blue-600 dark:text-blue-400"})}),children:"Configuraci\xf3n Inicial"})}),(0,t.jsx)("ul",{className:"space-y-3",children:h.filter(e=>"setup"===e.category).map(a=>(0,t.jsx)($,{item:a,onToggle:O,onRefresh:D,apiKey:e||void 0,codeSnippets:s,colorScheme:"blue"},a.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[(0,t.jsx)("span",{className:"bg-purple-100 dark:bg-purple-900/50 p-1 rounded-md mr-2",children:(0,t.jsx)(P.A,{className:"h-3.5 w-3.5 text-purple-600 dark:text-purple-400"})}),"Carga de Datos"]}),(0,t.jsx)("ul",{className:"space-y-3",children:h.filter(e=>"data"===e.category).map(a=>(0,t.jsx)($,{item:a,onToggle:O,onRefresh:D,apiKey:e||void 0,codeSnippets:s,colorScheme:"purple"},a.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[(0,t.jsx)("span",{className:"bg-amber-100 dark:bg-amber-900/50 p-1 rounded-md mr-2",children:(0,t.jsx)(E,{className:"h-3.5 w-3.5 text-amber-600 dark:text-amber-400"})}),"Modelo y Recomendaciones"]}),(0,t.jsx)("ul",{className:"space-y-3",children:h.filter(e=>"model"===e.category).map(a=>(0,t.jsx)($,{item:a,onToggle:O,onRefresh:D,apiKey:e||void 0,codeSnippets:s,colorScheme:"amber"},a.id))})]})]})]})})]})};var L=s(13972);let B=(0,y.A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);var M=s(43649),U=s(88233),Y=s(52581),Z=s(44957);function F({currentPlan:e}){let[a,s]=(0,r.useState)(!1),{token:i}=(0,Z.A)();if("FREE"!==e)return null;let d=async()=>{s(!0);try{if((await fetch("/api/v1/sandbox/reset",{method:"POST",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).ok)Y.o.success("\uD83E\uDDEA Datos de sandbox limpiados exitosamente"),window.location.reload();else throw Error("Error al limpiar el sandbox")}catch(e){console.error("Error resetting sandbox:",e),Y.o.error("Error al limpiar los datos de sandbox")}finally{s(!1)}};return(0,t.jsxs)(l.Zp,{className:"border-orange-200 dark:border-orange-800",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2 text-orange-800 dark:text-orange-200",children:[(0,t.jsx)(B,{className:"h-5 w-5"}),"Developer Sandbox Tools"]}),(0,t.jsx)(l.BT,{children:"Herramientas especiales para desarrollo y experimentaci\xf3n"})]}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(M.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"Entorno de Desarrollo"}),(0,t.jsxs)(o.TN,{children:["Est\xe1s en el plan ",(0,t.jsx)("strong",{children:"Developer Sandbox"}),". Puedes experimentar libremente sin preocuparte por costos o afectar datos de producci\xf3n."]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"font-medium",children:"Reset de Datos Experimentales"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Limpia todos tus datos de prueba (productos, usuarios, interacciones, modelos) para empezar fresh con nuevos experimentos."}),(0,t.jsxs)(L.Lt,{children:[(0,t.jsx)(L.tv,{asChild:!0,children:(0,t.jsxs)(n.Button,{variant:"outline",className:"w-full border-orange-300 text-orange-700 hover:bg-orange-50 dark:border-orange-700 dark:text-orange-300 dark:hover:bg-orange-900/20",disabled:a,children:[(0,t.jsx)(U.A,{className:"mr-2 h-4 w-4"}),a?"Limpiando...":"Limpiar Datos de Sandbox"]})}),(0,t.jsxs)(L.EO,{children:[(0,t.jsxs)(L.wd,{children:[(0,t.jsx)(L.r7,{children:"\xbfLimpiar todos los datos de sandbox?"}),(0,t.jsxs)(L.$v,{className:"space-y-2",children:[(0,t.jsx)("p",{children:"Esta acci\xf3n eliminar\xe1 permanentemente:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside text-sm space-y-1 ml-4",children:[(0,t.jsx)("li",{children:"Todos los productos de prueba"}),(0,t.jsx)("li",{children:"Todos los usuarios de prueba"}),(0,t.jsx)("li",{children:"Todas las interacciones de prueba"}),(0,t.jsx)("li",{children:"Todos los modelos entrenados"}),(0,t.jsx)("li",{children:"Historial de jobs de entrenamiento e ingesta"})]}),(0,t.jsx)("p",{className:"font-medium text-orange-600 dark:text-orange-400",children:"⚠️ Esta acci\xf3n no se puede deshacer."}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"\uD83D\uDD12 Tus API Keys y configuraci\xf3n de cuenta se mantendr\xe1n intactas."})]})]}),(0,t.jsxs)(L.ck,{children:[(0,t.jsx)(L.Zr,{children:"Cancelar"}),(0,t.jsx)(L.Rx,{onClick:d,className:"bg-orange-600 hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800",disabled:a,children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(C.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Limpiando..."]}):"S\xed, limpiar sandbox"})]})]})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,t.jsxs)("p",{children:["\uD83D\uDCA1 ",(0,t.jsx)("strong",{children:"Tip:"})," Limpia tus datos regularmente para probar diferentes escenarios"]}),(0,t.jsxs)("p",{children:["\uD83D\uDE80 ",(0,t.jsx)("strong",{children:"Workflow recomendado:"})," Reset → Cargar datos test → Entrenar → Evaluar"]})]})]})]})]})}var H=s(13668);function V(){let{token:e,apiKey:a}=(0,i.As)(),[s,h]=(0,r.useState)(!1),[j,f]=(0,r.useState)(!0),[y,b]=(0,r.useState)(!1),{accountData:v,error:k,isLoading:w}=(0,i.T4)(),{usageData:A,error:_,isLoading:P}=(0,i.TB)(),E=w||P;return(0,t.jsxs)("div",{className:"rayuela-fade-in",children:[(0,t.jsx)("div",{className:"rayuela-scale-in rayuela-stagger-1",children:(0,t.jsx)(O,{})}),(0,t.jsx)("div",{className:"rayuela-scale-in rayuela-stagger-2",children:(0,t.jsx)(F,{currentPlan:v?.subscription?.plan})}),s&&!y&&!j&&(0,t.jsx)("div",{className:"rayuela-slide-up rayuela-stagger-2",children:(0,t.jsx)(o.Fc,{className:"mb-6",variant:"info",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(o.XL,{className:"flex items-center",children:"\xa1Bienvenido a Rayuela!"}),(0,t.jsxs)(o.TN,{className:"mt-2",children:["Para comenzar a utilizar nuestra API, te recomendamos seguir estos pasos:",(0,t.jsxs)("ol",{className:"list-none mt-3 space-y-3",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm",children:"1"}),(0,t.jsx)(g(),{href:"/api-keys",className:"text-primary hover:text-primary/80 font-medium underline underline-offset-2",children:"Gestiona tus API Keys"}),(0,t.jsx)("span",{className:"ml-2",children:"para generar o ver tus claves de acceso"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm",children:"2"}),(0,t.jsx)("a",{href:"https://docs.rayuela.ai/quickstart",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary/80 font-medium underline underline-offset-2",children:"Consulta nuestra gu\xeda de inicio r\xe1pido"}),(0,t.jsx)("span",{className:"ml-2",children:"para aprender a integrar la API"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm",children:"3"}),(0,t.jsx)(g(),{href:"/usage",className:"text-primary hover:text-primary/80 font-medium underline underline-offset-2",children:"Monitorea tu uso"}),(0,t.jsx)("span",{className:"ml-2",children:"para ver estad\xedsticas y l\xedmites de tu cuenta"})]})]})]}),(0,t.jsxs)("div",{className:"mt-5 flex flex-wrap gap-3",children:[(0,t.jsx)(n.Button,{asChild:!0,size:"sm",children:(0,t.jsxs)(g(),{href:"/api-keys",className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Gestionar API Keys"]})}),(0,t.jsx)(n.Button,{asChild:!0,variant:"outline",size:"sm",children:(0,t.jsxs)("a",{href:"https://docs.rayuela.ai/quickstart",target:"_blank",rel:"noopener noreferrer",className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Ver Gu\xeda de Inicio"]})}),(0,t.jsx)(n.Button,{asChild:!0,variant:"outline",size:"sm",children:(0,t.jsxs)(g(),{href:"/usage",className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Ver Uso"]})})]})]}),(0,t.jsx)(n.Button,{variant:"ghost",size:"icon",className:"text-muted-foreground hover:bg-muted hover:text-foreground",onClick:()=>{b(!0),localStorage.setItem("dismissedOnboarding","true")},children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})]})})}),(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-heading-large text-foreground",children:"Dashboard Overview"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2",children:"Welcome to your dashboard. Here you'll find an overview of your API usage, billing information, and manage your API keys."})]}),(0,t.jsx)(N,{className:"hidden md:flex"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-6",children:[(0,t.jsxs)(l.Zp,{className:"col-span-1 md:col-span-2 lg:col-span-2 border-2 border-blue-50 dark:border-blue-900/30 shadow-sm hover:shadow-md transition-all duration-300",children:[(0,t.jsxs)(l.aR,{className:"pb-2",children:[(0,t.jsxs)(l.ZB,{className:"flex items-center text-subheading",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 mr-2 text-info"}),"Resumen de Uso"]}),(0,t.jsx)(l.BT,{children:"Vista r\xe1pida de tu consumo actual"})]}),(0,t.jsx)(l.Wu,{children:E?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.E,{className:"h-6 w-full"}),(0,t.jsx)(d.E,{className:"h-4 w-3/4"})]}):_?(0,t.jsx)("p",{className:"text-red-500 text-sm",children:"Error al cargar datos de uso"}):A&&v?(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"p-3 bg-info-light rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-blue-700 dark:text-blue-300 font-medium",children:"Llamadas API"}),(0,t.jsx)("div",{className:"text-metric mt-1 text-blue-800 dark:text-blue-200",children:(0,H.ZV)(A.apiCalls?.used||0)}),(0,t.jsx)("div",{className:"text-xs text-blue-600/70 dark:text-blue-400/70 mt-1",children:"Uso actual"})]}),(0,t.jsxs)("div",{className:"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-green-700 dark:text-green-300 font-medium",children:"Almacenamiento"}),(0,t.jsx)("div",{className:"text-metric mt-1 text-green-800 dark:text-green-200",children:(0,H.z3)(A.storage?.usedBytes||0)}),(0,t.jsx)("div",{className:"text-xs text-green-600/70 dark:text-green-400/70 mt-1",children:"Uso actual"})]})]}):(0,t.jsx)("p",{className:"text-gray-500",children:"No hay datos de uso disponibles"})}),(0,t.jsx)(l.wL,{children:(0,t.jsx)(n.Button,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,t.jsxs)(g(),{href:"/usage",className:"flex items-center justify-center",children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Ver Panel de Uso Completo"]})})})]}),(0,t.jsxs)(l.Zp,{className:"border-2 border-orange-50 dark:border-orange-900/30 shadow-sm hover:shadow-md transition-all duration-300",children:[(0,t.jsxs)(l.aR,{className:"pb-2",children:[(0,t.jsxs)(l.ZB,{className:"flex items-center text-subheading",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 mr-2 text-warning"}),"API Key Actual"]}),(0,t.jsx)(l.BT,{children:"Informaci\xf3n de tu clave activa"})]}),(0,t.jsx)(l.Wu,{children:E?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.E,{className:"h-4 w-full"}),(0,t.jsx)(d.E,{className:"h-3 w-2/3"})]}):k?(0,t.jsx)("p",{className:"text-red-500 text-sm",children:"Error al cargar datos de la cuenta"}):a?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"text-sm font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded",children:[a.slice(0,4),"...",a.slice(-4)]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"API Key activa"})]}):(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay API Key disponible"})})]}),(0,t.jsxs)(l.Zp,{className:"border-2 border-green-50 dark:border-green-900/30 shadow-sm hover:shadow-md transition-all duration-300",children:[(0,t.jsxs)(l.aR,{className:"pb-2",children:[(0,t.jsxs)(l.ZB,{className:"flex items-center text-subheading",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 mr-2 text-success"}),"Estado de Cuenta"]}),(0,t.jsx)(l.BT,{children:"Estado actual de tu suscripci\xf3n"})]}),(0,t.jsx)(l.Wu,{children:E?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.E,{className:"h-6 w-full"}),(0,t.jsx)(d.E,{className:"h-4 w-3/4"})]}):k?(0,t.jsx)("p",{className:"text-red-500 text-sm",children:"Error al cargar datos de suscripci\xf3n"}):v?.subscription?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`w-2 h-2 rounded-full mr-2 ${v.subscription.isActive?"bg-green-500":"bg-red-500"}`}),(0,t.jsx)("span",{className:"text-sm font-medium",children:v.subscription.plan||"Free"})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[v.subscription.isActive?"Activa":"Inactiva",v.subscription.expiresAt&&(0,t.jsxs)("span",{children:[" • Expira ",(0,H.Yq)(v.subscription.expiresAt)]})]})]}):(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Plan Free"})]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Plan b\xe1sico activo"})]})})]})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13668:(e,a,s)=>{"use strict";s.d(a,{KC:()=>u,Yq:()=>h,ZV:()=>x,a3:()=>g,cR:()=>p,z3:()=>m});var t=s(60687);s(43210);var r=s(96834),i=s(5336),n=s(78122),o=s(48730),l=s(35071),d=s(97840),c=s(43649);function m(e,a=2){if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(a<0?0:a))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][s]}function x(e){return new Intl.NumberFormat().format(e)}function p(e){let a={className:"h-4 w-4"};switch(e?.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(i.A,{...a,className:"h-4 w-4 text-green-500"});case"running":case"processing":case"in_progress":return(0,t.jsx)(n.A,{...a,className:"h-4 w-4 text-blue-500 animate-spin"});case"pending":case"queued":case"waiting":return(0,t.jsx)(o.A,{...a,className:"h-4 w-4 text-yellow-500"});case"failed":case"error":case"cancelled":return(0,t.jsx)(l.A,{...a,className:"h-4 w-4 text-red-500"});case"starting":case"initializing":return(0,t.jsx)(d.A,{...a,className:"h-4 w-4 text-blue-400"});case"warning":return(0,t.jsx)(c.A,{...a,className:"h-4 w-4 text-amber-500"});default:return(0,t.jsx)(o.A,{...a,className:"h-4 w-4 text-gray-400"})}}function u(e){switch(e?.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(r.E,{variant:"success",className:"text-xs",children:"Completado"});case"running":case"processing":case"in_progress":return(0,t.jsx)(r.E,{variant:"info",className:"text-xs",children:"En progreso"});case"pending":case"queued":case"waiting":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Pendiente"});case"failed":case"error":case"cancelled":return(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"Fallido"});case"starting":case"initializing":return(0,t.jsx)(r.E,{variant:"secondary",className:"text-xs",children:"Iniciando"});case"warning":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Advertencia"});default:return(0,t.jsx)(r.E,{variant:"outline",className:"text-xs",children:"Desconocido"})}}function h(e,a=!0){if(!e)return"No disponible";try{let s=new Date(e),t={year:"numeric",month:"long",day:"numeric",...a&&{hour:"2-digit",minute:"2-digit"}};return new Intl.DateTimeFormat("es-ES",t).format(s)}catch(e){return console.error("Error al formatear fecha:",e),"Formato de fecha inv\xe1lido"}}function g(e){let a=Math.floor(e/1e3),s=Math.floor(a/60),t=Math.floor(s/60),r=Math.floor(t/24);return r>0?`${r}d ${t%24}h ${s%60}m`:t>0?`${t}h ${s%60}m ${a%60}s`:s>0?`${s}m ${a%60}s`:`${a}s`}},13972:(e,a,s)=>{"use strict";s.d(a,{Lt:()=>T,Rx:()=>B,Zr:()=>M,EO:()=>q,$v:()=>L,ck:()=>K,wd:()=>$,r7:()=>O,tv:()=>R});var t=s(60687),r=s(43210),i=s(11273),n=s(98599),o=s(4590),l=s(70569),d=Symbol("radix.slottable"),c="AlertDialog",[m,x]=(0,i.A)(c,[o.Hs]),p=(0,o.Hs)(),u=e=>{let{__scopeAlertDialog:a,...s}=e,r=p(a);return(0,t.jsx)(o.bL,{...r,...s,modal:!0})};u.displayName=c;var h=r.forwardRef((e,a)=>{let{__scopeAlertDialog:s,...r}=e,i=p(s);return(0,t.jsx)(o.l9,{...i,...r,ref:a})});h.displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:a,...s}=e,r=p(a);return(0,t.jsx)(o.ZL,{...r,...s})};g.displayName="AlertDialogPortal";var j=r.forwardRef((e,a)=>{let{__scopeAlertDialog:s,...r}=e,i=p(s);return(0,t.jsx)(o.hJ,{...i,...r,ref:a})});j.displayName="AlertDialogOverlay";var f="AlertDialogContent",[y,b]=m(f),v=function(e){let a=({children:e})=>(0,t.jsx)(t.Fragment,{children:e});return a.displayName=`${e}.Slottable`,a.__radixId=d,a}("AlertDialogContent"),N=r.forwardRef((e,a)=>{let{__scopeAlertDialog:s,children:i,...d}=e,c=p(s),m=r.useRef(null),x=(0,n.s)(a,m),u=r.useRef(null);return(0,t.jsx)(o.G$,{contentName:f,titleName:k,docsSlug:"alert-dialog",children:(0,t.jsx)(y,{scope:s,cancelRef:u,children:(0,t.jsxs)(o.UC,{role:"alertdialog",...c,...d,ref:x,onOpenAutoFocus:(0,l.m)(d.onOpenAutoFocus,e=>{e.preventDefault(),u.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,t.jsx)(v,{children:i}),(0,t.jsx)(I,{contentRef:m})]})})})});N.displayName=f;var k="AlertDialogTitle",w=r.forwardRef((e,a)=>{let{__scopeAlertDialog:s,...r}=e,i=p(s);return(0,t.jsx)(o.hE,{...i,...r,ref:a})});w.displayName=k;var A="AlertDialogDescription",_=r.forwardRef((e,a)=>{let{__scopeAlertDialog:s,...r}=e,i=p(s);return(0,t.jsx)(o.VY,{...i,...r,ref:a})});_.displayName=A;var P=r.forwardRef((e,a)=>{let{__scopeAlertDialog:s,...r}=e,i=p(s);return(0,t.jsx)(o.bm,{...i,...r,ref:a})});P.displayName="AlertDialogAction";var E="AlertDialogCancel",C=r.forwardRef((e,a)=>{let{__scopeAlertDialog:s,...r}=e,{cancelRef:i}=b(E,s),l=p(s),d=(0,n.s)(a,i);return(0,t.jsx)(o.bm,{...l,...r,ref:d})});C.displayName=E;var I=({contentRef:e})=>{let a=`\`${f}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${f}\` by passing a \`${A}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${f}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return r.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(a)},[a,e]),null},S=s(4780),D=s(29523);let T=u,R=h,z=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(j,{className:(0,S.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:s}));z.displayName=j.displayName;let q=r.forwardRef(({className:e,...a},s)=>(0,t.jsxs)(g,{children:[(0,t.jsx)(z,{}),(0,t.jsx)(N,{ref:s,className:(0,S.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]}));q.displayName=N.displayName;let $=({className:e,...a})=>(0,t.jsx)("div",{className:(0,S.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...a});$.displayName="AlertDialogHeader";let K=({className:e,...a})=>(0,t.jsx)("div",{className:(0,S.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});K.displayName="AlertDialogFooter";let O=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(w,{ref:s,className:(0,S.cn)("text-lg font-semibold",e),...a}));O.displayName=w.displayName;let L=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(_,{ref:s,className:(0,S.cn)("text-sm text-muted-foreground",e),...a}));L.displayName=_.displayName;let B=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(P,{ref:s,className:(0,S.cn)((0,D.r)(),e),...a}));B.displayName=P.displayName;let M=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(C,{ref:s,className:(0,S.cn)((0,D.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...a}));M.displayName=C.displayName},14719:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23259:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t.default});var t=s(28464)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28464:(e,a,s)=>{"use strict";s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\page.tsx","default")},28698:(e,a,s)=>{Promise.resolve().then(s.bind(s,28464))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},48730:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68866:(e,a,s)=>{Promise.resolve().then(s.bind(s,4998))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},97840:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},98803:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=s(65239),r=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(a,l);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,23259)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var a=require("../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[4447,2713,5814,1576,5077,5552,2807,5320,3525,9835],()=>s(98803));module.exports=t})();