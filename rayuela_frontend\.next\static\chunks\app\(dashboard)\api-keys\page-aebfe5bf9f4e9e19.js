(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2032],{10:(e,a,s)=>{"use strict";s.d(a,{$v:()=>f,EO:()=>u,Lt:()=>c,Rx:()=>v,Zr:()=>j,ck:()=>h,r7:()=>p,tv:()=>o,wd:()=>x});var r=s(5155),t=s(2115),n=s(2278),i=s(9434),l=s(285);let c=n.bL,o=n.l9,d=n.ZL,m=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(n.hJ,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...t,ref:a})});m.displayName=n.hJ.displayName;let u=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsxs)(d,{children:[(0,r.jsx)(m,{}),(0,r.jsx)(n.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...t})]})});u.displayName=n.UC.displayName;let x=e=>{let{className:a,...s}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...s})};x.displayName="AlertDialogHeader";let h=e=>{let{className:a,...s}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};h.displayName="AlertDialogFooter";let p=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(n.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold",s),...t})});p.displayName=n.hE.displayName;let f=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(n.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",s),...t})});f.displayName=n.VY.displayName;let v=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(n.rc,{ref:a,className:(0,i.cn)((0,l.r)(),s),...t})});v.displayName=n.rc.displayName;let j=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(n.ZD,{ref:a,className:(0,i.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",s),...t})});j.displayName=n.ZD.displayName},4556:(e,a,s)=>{"use strict";s.d(a,{Q:()=>i});var r=s(6072),t=s(5731),n=s(2115);function i(){var e,a,s;let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[l,c]=(0,n.useState)(!1),[o,d]=(0,n.useState)(!1),[m,u]=(0,n.useState)(!1),[x,h]=(0,n.useState)(!1),[p,f]=(0,n.useState)(null),{data:v,error:j,isLoading:y,isValidating:g,mutate:N}=(0,r.Ay)("api-keys",async()=>await (0,t.PX)(),{revalidateOnFocus:null==(e=i.revalidateOnFocus)||e,refreshInterval:i.refreshInterval,dedupingInterval:null!=(a=i.dedupingInterval)?a:6e4,errorRetryCount:null!=(s=i.errorRetryCount)?s:3,onError:e=>{console.error("Error fetching API keys:",e)}}),A=(null==v?void 0:v.api_keys)&&v.api_keys.length>0?v.api_keys.find(e=>e.is_active)||v.api_keys[0]:null,E=async e=>{c(!0),f(null);try{let a={name:e.name||"",permissions:[]},s=await (0,t.Iq)(a);return await N(),s}catch(a){let e=a instanceof t.hD?a:new t.hD("Error al crear API Key",500);throw f(e),e}finally{c(!1)}},I=async(e,a)=>{d(!0),f(null);try{let s={name:a.name||void 0,permissions:[]},r=await (0,t.XW)(e.toString(),s);return await N(),r}catch(a){let e=a instanceof t.hD?a:new t.hD("Error al actualizar API Key",500);throw f(e),e}finally{d(!1)}},b=async e=>{u(!0),f(null);try{return await (0,t.mA)(e),await N(),!0}catch(a){let e=a instanceof t.hD?a:new t.hD("Error al revocar API Key",500);throw f(e),e}finally{u(!1)}},w=async()=>{h(!0),f(null);try{let e=await (0,t.Iq)({name:"API Key ".concat(new Date().toLocaleDateString("es-ES"))});return await N(),e}catch(e){return f(e instanceof t.hD?e:new t.hD("Error al regenerar API Key",500)),null}finally{h(!1)}};return{data:null!=v?v:null,primaryKey:null!=A?A:null,error:null!=j?j:null,isLoading:y,isValidating:g,mutate:N,dataUpdatedAt:0,createApiKey:E,updateApiKey:I,revokeApiKey:b,regenerateApiKey:w,isCreating:l,isUpdating:o,isRevoking:m,isRegenerating:x,operationError:p,getFormattedApiKey:e=>{let a=e||A;return(null==a?void 0:a.prefix)&&(null==a?void 0:a.last_chars)?"".concat(a.prefix,"••••••••").concat(a.last_chars):null}}}},6718:(e,a,s)=>{Promise.resolve().then(s.bind(s,6986))},6986:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>M});var r=s(5155),t=s(2115),n=s(285),i=s(6695),l=s(8856),c=s(5127),o=s(6126),d=s(5365),m=s(3999),u=s(4556),x=s(6671),h=s(7588),p=s(3008),f=s(3439),v=s(5339),j=s(9803),y=s(4616),g=s(6932),N=s(7924),A=s(4357),E=s(2713),I=s(3717),b=s(2525),w=s(1284),C=s(5525),P=s(6785),K=s(7580),_=s(6874),z=s.n(_),k=s(2193),R=s(7271),S=s(8534),D=s(10),L=s(4165),T=s(2523),F=s(5057),B=s(9409);function M(){var e,a,s;let{token:_,user:M,isLoading:O}=(0,m.A)(),[q,U]=(0,t.useState)(!1),[G,H]=(0,t.useState)(null),[X,Z]=(0,t.useState)(!1),[V,J]=(0,t.useState)(!1),[Q,Y]=(0,t.useState)(null),[W,$]=(0,t.useState)(""),[ee,ea]=(0,t.useState)(""),[es,er]=(0,t.useState)(""),[et,en]=(0,t.useState)("all"),{data:ei,error:el,isLoading:ec,createApiKey:eo,updateApiKey:ed,revokeApiKey:em,isCreating:eu,isUpdating:ex,isRevoking:eh,getFormattedApiKey:ep}=(0,u.Q)({revalidateOnFocus:!0,refreshInterval:3e4,dedupingInterval:5e3,errorRetryCount:3}),ef=(0,t.useMemo)(()=>(null==ei?void 0:ei.api_keys)?ei.api_keys.filter(e=>{let a="all"===et||"active"===et&&e.is_active||"revoked"===et&&!e.is_active,s=""===es||e.name&&e.name.toLowerCase().includes(es.toLowerCase())||e.id.toString().includes(es);return a&&s}):[],[null==ei?void 0:ei.api_keys,et,es]),ev=e=>e.prefix&&e.last_chars?"".concat(e.prefix).concat("••••••••••••••••").concat(e.last_chars):"ray_••••••••••••••••••••",ej=e=>ep(e),ey=async()=>{try{let e=await eo({name:W.trim()||"API Key ".concat(new Date().toLocaleDateString("es-ES"))});(null==e?void 0:e.api_key)&&(H(e.api_key),U(!0),Z(!1),$(""),x.o.success("API Key creada con \xe9xito"))}catch(e){(0,h.h)(e,"Error al crear la API Key")}},eg=async()=>{if(Q)try{await ed(Q.id,{name:ee.trim()})&&(J(!1),Y(null),ea(""),x.o.success("API Key actualizada con \xe9xito"))}catch(e){(0,h.h)(e,"Error al actualizar la API Key")}},eN=async(e,a)=>{try{await em(e),x.o.success("API Key ".concat(a?'"'.concat(a,'"'):""," revocada con \xe9xito"))}catch(e){(0,h.h)(e,"Error al revocar la API Key")}},eA=e=>{let a=ej(e);a&&(navigator.clipboard.writeText(a),x.o.success("API Key copiada al portapapeles"))},eE=e=>{Y(e),ea(e.name||""),J(!0)},eI=()=>{er(""),en("all")};if(el)return(0,r.jsx)("div",{className:"container mx-auto py-8",children:(0,r.jsxs)(d.Fc,{variant:"destructive",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)(d.XL,{children:"Error al cargar las API Keys"}),(0,r.jsxs)(d.TN,{children:[el.message,(0,r.jsx)("br",{}),"Intenta refrescar la p\xe1gina o contacta a soporte."]})]})});if(!_||!M)return(0,r.jsx)("div",{className:"container mx-auto py-8",children:(0,r.jsxs)(d.Fc,{variant:"warning",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)(d.XL,{children:"Sesi\xf3n no verificada"}),(0,r.jsxs)(d.TN,{children:["No se pudo verificar tu sesi\xf3n. Por favor, intenta ",(0,r.jsx)(z(),{href:"/login",className:"underline",children:"iniciar sesi\xf3n"})," de nuevo."]})]})});if(O||ec)return(0,r.jsx)("div",{className:"container mx-auto py-8 space-y-8",children:(0,r.jsxs)("div",{className:"bg-card/30 border border-border/50 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"API Keys"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2",children:"Gestiona m\xfaltiples claves para tus proyectos y entornos"})]}),(0,r.jsx)(l.E,{className:"h-10 w-32"})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(l.E,{className:"h-6 w-6"}),(0,r.jsx)(l.E,{className:"h-6 w-48"})]}),(0,r.jsx)(i.BT,{children:(0,r.jsx)(l.E,{className:"h-4 w-64"})})]}),(0,r.jsx)(i.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.E,{className:"h-10 w-full"}),(0,r.jsx)(l.E,{className:"h-10 w-full"}),(0,r.jsx)(l.E,{className:"h-10 w-full"})]})})]})]})});let eb=(null==ei||null==(e=ei.api_keys)?void 0:e.length)||0,ew=(null==ei||null==(s=ei.api_keys)||null==(a=s.filter(e=>e.is_active))?void 0:a.length)||0,eC=eb-ew;return(0,r.jsxs)(R.hI,{title:"API Keys",description:"Gestiona m\xfaltiples claves de API para acceder a los servicios de Rayuela",actions:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex gap-6 text-sm text-muted-foreground mr-4",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[(0,r.jsx)(S.mm,{icon:j.A,size:"sm",context:"success"}),ew," activas"]}),eC>0&&(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[(0,r.jsx)(S.mm,{icon:v.A,size:"sm",context:"error"}),eC," revocadas"]}),(0,r.jsxs)("span",{className:"font-medium",children:["Total: ",eb]})]}),(0,r.jsxs)(L.lG,{open:X,onOpenChange:Z,children:[(0,r.jsx)(L.zM,{asChild:!0,children:(0,r.jsxs)(n.Button,{className:"flex items-center gap-2",children:[(0,r.jsx)(S.mm,{icon:y.A,size:"sm",context:"neutral"}),"Nueva API Key"]})}),(0,r.jsxs)(L.Cf,{children:[(0,r.jsxs)(L.c7,{children:[(0,r.jsx)(L.L3,{children:"Crear Nueva API Key"}),(0,r.jsx)(L.rr,{children:"Crea una nueva API Key para acceder a los servicios de Rayuela. Puedes asignarle un nombre descriptivo."})]}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(F.J,{htmlFor:"newKeyName",children:"Nombre de la API Key (opcional)"}),(0,r.jsx)(T.p,{id:"newKeyName",placeholder:"ej. Producci\xf3n, Desarrollo, Equipo Frontend...",value:W,onChange:e=>$(e.target.value)})]})}),(0,r.jsxs)(L.Es,{children:[(0,r.jsx)(n.Button,{variant:"outline",onClick:()=>Z(!1),children:"Cancelar"}),(0,r.jsx)(n.Button,{onClick:ey,disabled:eu,children:eu?"Creando...":"Crear API Key"})]})]})]})]}),children:[(0,r.jsx)(R.os,{title:"Filtros y B\xfasqueda",icon:(0,r.jsx)(S.mm,{icon:g.A,size:"md",context:"primary"}),description:"Encuentra r\xe1pidamente las API Keys que necesitas",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-6 sm:flex-row sm:items-center sm:gap-6",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(S.mm,{icon:N.A,size:"sm",context:"muted",className:"absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,r.jsx)(T.p,{placeholder:"Buscar por nombre o ID...",value:es,onChange:e=>er(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,r.jsx)(F.J,{htmlFor:"status-filter",className:"text-sm font-medium whitespace-nowrap",children:"Estado:"}),(0,r.jsxs)(B.l6,{value:et,onValueChange:e=>en(e),children:[(0,r.jsx)(B.bq,{id:"status-filter",className:"w-32",children:(0,r.jsx)(B.yv,{})}),(0,r.jsxs)(B.gC,{children:[(0,r.jsx)(B.eb,{value:"all",children:"Todas"}),(0,r.jsx)(B.eb,{value:"active",children:"Activas"}),(0,r.jsx)(B.eb,{value:"revoked",children:"Revocadas"})]})]}),(es||"all"!==et)&&(0,r.jsx)(n.Button,{variant:"outline",size:"sm",onClick:eI,children:"Limpiar"})]})]}),(es||"all"!==et)&&(0,r.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:["Mostrando ",ef.length," de ",eb," API Keys"]})]})}),(0,r.jsx)(R.os,{title:"Tus API Keys",icon:(0,r.jsx)(S.mm,{icon:j.A,size:"md",context:"primary"}),description:"Cada clave funciona independientemente y puede ser usada para diferentes proyectos o entornos.",children:(0,r.jsx)("div",{className:"overflow-hidden",children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{className:"bg-muted/10",children:(0,r.jsxs)(c.Hj,{className:"border-b border-border/30",children:[(0,r.jsx)(c.nd,{className:"font-semibold",children:"Nombre"}),(0,r.jsx)(c.nd,{className:"font-semibold",children:"Clave"}),(0,r.jsx)(c.nd,{className:"font-semibold",children:"Estado"}),(0,r.jsx)(c.nd,{className:"font-semibold",children:"Creaci\xf3n"}),(0,r.jsx)(c.nd,{className:"font-semibold",children:"\xdaltimo uso"}),(0,r.jsxs)(c.nd,{className:"font-semibold",children:["Uso ",(0,r.jsx)("span",{className:"text-xs font-normal",children:"(pr\xf3ximamente)"})]}),(0,r.jsx)(c.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,r.jsx)(c.BF,{children:ef&&ef.length>0?ef.map((e,a)=>(0,r.jsxs)(R.AP,{index:a,className:e.is_active?"":"opacity-60",children:[(0,r.jsx)(c.nA,{className:"font-medium py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(S.mm,{icon:e.is_active?j.A:v.A,size:"xs",context:e.is_active?"success":"error"}),e.name||"API Key ".concat(e.id)]})}),(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("code",{className:"text-sm bg-muted/50 px-2 py-1 rounded border font-mono",children:ev(e)}),e.is_active&&(0,r.jsx)(n.Button,{variant:"ghost",size:"sm",onClick:()=>eA(e),className:"h-6 w-6 p-0 hover:bg-muted/50",title:"Copiar API Key completa",children:(0,r.jsx)(S.mm,{icon:A.A,size:"xs",context:"muted"})})]})}),(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsx)(o.E,{variant:e.is_active?"success":"destructive",children:e.is_active?"Activa":"Revocada"})}),(0,r.jsx)(c.nA,{className:"text-muted-foreground py-4",children:e.created_at?(0,p.GP)(new Date(e.created_at),"d 'de' MMMM, yyyy",{locale:f.es}):"N/A"}),(0,r.jsx)(c.nA,{className:"text-muted-foreground py-4",children:e.last_used?(0,p.GP)(new Date(e.last_used),"d 'de' MMMM, yyyy",{locale:f.es}):"Nunca"}),(0,r.jsx)(c.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-1 text-muted-foreground",children:[(0,r.jsx)(S.mm,{icon:E.A,size:"xs",context:"muted"}),(0,r.jsx)("span",{className:"text-xs",children:"--"})]})}),(0,r.jsx)(c.nA,{className:"text-right py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[e.is_active&&(0,r.jsx)(n.Button,{variant:"ghost",size:"sm",onClick:()=>eE(e),className:"h-8 w-8 p-0 hover:bg-muted/50",title:"Editar nombre",children:(0,r.jsx)(S.mm,{icon:I.A,size:"sm",context:"muted"})}),e.is_active&&(0,r.jsxs)(D.Lt,{children:[(0,r.jsx)(D.tv,{asChild:!0,children:(0,r.jsx)(n.Button,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10",title:"Revocar API Key",children:(0,r.jsx)(S.mm,{icon:b.A,size:"sm",context:"error"})})}),(0,r.jsxs)(D.EO,{children:[(0,r.jsxs)(D.wd,{children:[(0,r.jsx)(D.r7,{children:"\xbfEst\xe1s seguro?"}),(0,r.jsxs)(D.$v,{children:['Esta acci\xf3n no se puede deshacer. Esto revocar\xe1 permanentemente la API Key "',e.name||"API Key ".concat(e.id),'" y cualquier aplicaci\xf3n que la use dejar\xe1 de funcionar.']})]}),(0,r.jsxs)(D.ck,{children:[(0,r.jsx)(D.Zr,{children:"Cancelar"}),(0,r.jsx)(D.Rx,{onClick:()=>eN(e.id,e.name||void 0),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:eh,children:eh?"Revocando...":"Revocar API Key"})]})]})]})]})})]},e.id)):(0,r.jsx)(c.Hj,{children:(0,r.jsx)(c.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===eb?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(S.mm,{icon:v.A,size:"2xl",context:"muted"}),(0,r.jsx)("p",{children:"No tienes API Keys creadas a\xfan"}),(0,r.jsx)("p",{className:"text-sm",children:"Crea tu primera API Key para comenzar a usar la API"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(S.mm,{icon:N.A,size:"2xl",context:"muted"}),(0,r.jsx)("p",{children:"No se encontraron API Keys con los filtros aplicados"}),(0,r.jsx)(n.Button,{variant:"outline",size:"sm",onClick:eI,children:"Limpiar filtros"})]})})})})})]})})}),(0,r.jsxs)(d.Fc,{variant:"info",children:[(0,r.jsx)(S.mm,{icon:w.A,size:"sm",context:"info"}),(0,r.jsx)(d.XL,{children:"Sistema Multi-API Key: Gesti\xf3n Profesional"}),(0,r.jsx)(d.TN,{children:(0,r.jsxs)("div",{className:"space-y-4 text-sm mt-3",children:[(0,r.jsxs)("p",{children:["Rayuela soporta m\xfaltiples API Keys para ofrecerte m\xe1xima flexibilidad y seguridad. Cada clave funciona de forma independiente y las claves completas ",(0,r.jsx)("strong",{children:"solo se muestran una vez"})," al crearlas."]}),(0,r.jsxs)("div",{className:"grid gap-3 mt-4",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,r.jsx)(S.mm,{icon:C.A,size:"md",context:"success",className:"mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-foreground",children:"Seguridad Avanzada"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Revoca claves espec\xedficas sin afectar otras integraciones. Cada clave act\xfaa independientemente."})]})]}),(0,r.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,r.jsx)(S.mm,{icon:P.A,size:"md",context:"info",className:"mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-foreground",children:"Organizaci\xf3n por Entorno"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:'Asigna nombres descriptivos: "Producci\xf3n", "Desarrollo", "Testing". Identifica f\xe1cilmente el prop\xf3sito de cada clave.'})]})]}),(0,r.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,r.jsx)(S.mm,{icon:K.A,size:"md",context:"primary",className:"mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-foreground",children:"Gesti\xf3n por Equipos"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Crea claves separadas para diferentes equipos o aplicaciones. Control granular sobre el acceso."})]})]}),(0,r.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,r.jsx)(S.mm,{icon:E.A,size:"md",context:"metric",className:"mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-foreground",children:"M\xe9tricas Futuras"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Pr\xf3ximamente: an\xe1lisis de uso individual por API Key, incluyendo requests, errores y patrones de consumo."})]})]})]}),(0,r.jsx)("div",{className:"mt-4 p-3 bg-info-light rounded-lg border border-info/20",children:(0,r.jsxs)("p",{className:"text-sm text-info",children:[(0,r.jsx)("strong",{children:"Tip profesional:"})," Mant\xe9n claves separadas para diferentes entornos. Si comprometes una clave en desarrollo, tu producci\xf3n seguir\xe1 segura."]})})]})})]}),q&&G&&(0,r.jsx)(k.A,{apiKey:G,onClose:()=>{U(!1)}}),(0,r.jsx)(L.lG,{open:V,onOpenChange:J,children:(0,r.jsxs)(L.Cf,{children:[(0,r.jsxs)(L.c7,{children:[(0,r.jsx)(L.L3,{children:"Editar API Key"}),(0,r.jsx)(L.rr,{children:"Actualiza el nombre descriptivo de tu API Key. La clave en s\xed no se puede modificar."})]}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)(F.J,{htmlFor:"editKeyName",children:"Nombre de la API Key"}),(0,r.jsx)(T.p,{id:"editKeyName",placeholder:"ej. Producci\xf3n, Desarrollo, Equipo Frontend...",value:ee,onChange:e=>ea(e.target.value)})]})}),(0,r.jsxs)(L.Es,{children:[(0,r.jsx)(n.Button,{variant:"outline",onClick:()=>J(!1),children:"Cancelar"}),(0,r.jsx)(n.Button,{onClick:eg,disabled:ex,children:ex?"Actualizando...":"Actualizar"})]})]})})]})}},7588:(e,a,s)=>{"use strict";s.d(a,{h:()=>d});var r=s(6671),t=s(3464);class n extends Error{static isApiError(e){return e instanceof n}static fromResponse(e){return new n(e.message,e.status_code,e.error_code,e.details)}constructor(e,a,s,r){super(e),this.status=a,this.errorCode=s,this.details=r,this.name="ApiError"}}let i=t.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{{var a,s,r;let t=localStorage.getItem("rayuela-token"),n=localStorage.getItem("rayuela-apiKey");e.headers=null!=(r=e.headers)?r:{},t&&(e.headers.Authorization="Bearer ".concat(t)),!n||(null==(a=e.url)?void 0:a.includes("/auth/token"))||(null==(s=e.url)?void 0:s.includes("/auth/register"))||(e.headers["X-API-Key"]=n)}return e}),i.interceptors.response.use(e=>e,e=>{if(e.response){let a=e.response.data;throw n.fromResponse(a)}if(e.request)throw new n("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new n(e.message,0,"REQUEST_ERROR",null)});var l=s(6874),c=s.n(l),o=s(2115);function d(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Ha ocurrido un error";return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof n)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void r.o.error(o.createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",o.createElement(c(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void r.o.error(o.createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",o.createElement(c(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void r.o.error(o.createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",o.createElement(c(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void r.o.error(o.createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",o.createElement(c(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void r.o.error(o.createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",o.createElement(c(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void r.o.error(o.createElement("div",{},"Error de validacion: "+e.message+". ",o.createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void r.o.error(o.createElement("div",{},"Datos insuficientes para generar recomendaciones. ",o.createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void r.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(r.o.error(e.message||a),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void r.o.error(e.message||a):void(r.o.error(a),console.groupEnd())}},8534:(e,a,s)=>{"use strict";s.d(a,{mm:()=>l,vK:()=>o});var r=s(5155);s(2115);var t=s(9434);let n={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",xl:"h-8 w-8","2xl":"h-12 w-12"},i={success:"text-success",warning:"text-warning",error:"text-destructive",info:"text-info",primary:"text-primary",secondary:"text-secondary-foreground",muted:"text-muted-foreground",interactive:"text-primary hover:text-primary/80",neutral:"text-foreground",subtle:"text-muted-foreground",metric:"text-primary",action:"text-primary",navigation:"text-muted-foreground hover:text-foreground"};function l(e){let{icon:a,size:s="md",context:l="neutral",className:c,"aria-label":o,"aria-hidden":d=!o,...m}=e;return(0,r.jsx)(a,{className:(0,t.cn)(n[s],i[l],"shrink-0",c),"aria-label":o,"aria-hidden":d,...m})}let c={tight:"gap-1",normal:"gap-2",loose:"gap-3"};function o(e){let{icon:a,children:s,size:n="sm",context:i="neutral",iconPosition:o="left",spacing:d="normal",className:m}=e,u=(0,r.jsx)(l,{icon:a,size:n,context:i,"aria-hidden":!0});return(0,r.jsxs)("span",{className:(0,t.cn)("inline-flex items-center",c[d],m),children:["left"===o&&u,s,"right"===o&&u]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[9352,6874,1445,5674,3753,4214,3843,8034,9566,5813,6604,2092,3999,4485,8441,1684,7358],()=>a(6718)),_N_E=e.O()}]);