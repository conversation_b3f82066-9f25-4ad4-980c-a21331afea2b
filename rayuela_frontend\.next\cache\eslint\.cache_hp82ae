[{"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\api-keys\\page.tsx": "1", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\billing\\page.tsx": "2", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx": "3", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\models\\page.tsx": "4", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\page.tsx": "5", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx": "6", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\page.tsx": "7", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx": "8", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\recommendation-metrics\\page.tsx": "9", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\settings\\page.tsx": "10", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\usage\\page.tsx": "11", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\contact-sales\\page.tsx": "12", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\page.tsx": "13", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\quickstart\\python\\page.tsx": "14", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\features\\page.tsx": "15", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\home\\page.tsx": "16", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\layout.tsx": "17", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\cookies\\page.tsx": "18", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\dpa\\page.tsx": "19", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\layout.tsx": "20", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\notice\\page.tsx": "21", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\privacy\\page.tsx": "22", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\terms\\page.tsx": "23", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\login\\page.tsx": "24", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\pricing\\page.tsx": "25", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\verify-email\\[token]\\page.tsx": "26", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\api\\health\\route.ts": "27", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx": "28", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\page.tsx": "29", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\register\\page.tsx": "30", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\sitemap.ts": "31", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\AdminRoute.tsx": "32", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\InitialApiKeyModal.tsx": "33", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\LoginForm.tsx": "34", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\RegisterForm.tsx": "35", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\CurrentPlanCard.tsx": "36", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\PlanCard.tsx": "37", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\PlansGrid.tsx": "38", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\ApiStatus.tsx": "39", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\BillingButton.tsx": "40", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\BillingPortalButton.tsx": "41", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\ConfidenceMetricsChart.tsx": "42", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\DateRangeSelector.tsx": "43", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\EmailVerificationBanner.tsx": "44", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\GettingStartedChecklist.tsx": "45", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\Header.tsx": "46", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\MetricRecommendations.tsx": "47", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\QuickActions.tsx": "48", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\RecommendationMetricsChart.tsx": "49", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\SandboxResetButton.tsx": "50", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\Sidebar.tsx": "51", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\UsageChart.tsx": "52", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\UsageDashboard.tsx": "53", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\documentation\\iconography-guide.tsx": "54", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\pipeline\\DataIngestionModal.tsx": "55", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\pipeline\\TrainingModal.tsx": "56", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\accordion.tsx": "57", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\alert-dialog.tsx": "58", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\alert.tsx": "59", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\animation-examples.tsx": "60", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\badge.tsx": "61", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\button.tsx": "62", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\calendar.tsx": "63", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\card.tsx": "64", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\checkbox.tsx": "65", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\contrast-improvements.tsx": "66", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\dialog.tsx": "67", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\form.tsx": "68", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\icon.tsx": "69", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\iconography-improvements-showcase.tsx": "70", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\input.tsx": "71", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\label.tsx": "72", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\layout.tsx": "73", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\logo.tsx": "74", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\popover.tsx": "75", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\progress.tsx": "76", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\select.tsx": "77", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\skeleton.tsx": "78", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\sonner.tsx": "79", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\spacing-system.tsx": "80", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\table.tsx": "81", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tabs.tsx": "82", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tooltip-helper.tsx": "83", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tooltip.tsx": "84", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\typography-showcase.tsx": "85", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\ui-improvements-showcase.tsx": "86", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\analysis.ts": "87", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api\\recommendation-metrics.ts": "88", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api.ts": "89", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\auth.tsx": "90", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\chart-utils.ts": "91", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\constants.ts": "92", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\error-handler.ts": "93", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\api-client.ts": "94", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\migration-helper.ts": "95", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\rayuelaAPI.ts": "96", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\schemas\\index.ts": "97", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\index.ts": "98", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useIngestionJobs.ts": "99", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useModels.ts": "100", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useTrainingJobs.ts": "101", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\recommendationRules.ts": "102", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\seo.ts": "103", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useAccountInfo.ts": "104", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useApiKeys.ts": "105", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\usePlans.ts": "106", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useRecommendationMetrics.ts": "107", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useUsageHistory.ts": "108", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useUsageSummary.ts": "109", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils\\billing.ts": "110", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils\\format.tsx": "111", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils.ts": "112", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\fetch-openapi.js": "113", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\fetch-openapi.ts": "114", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\generate-og-image.js": "115", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\test-openapi-generation.ts": "116", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\types\\checklist.ts": "117", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx": "118", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\patch-openapi-static.js": "119", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useJobTable.ts": "120"}, {"size": 26716, "mtime": 1751089596968, "results": "121", "hashOfConfig": "122"}, {"size": 3760, "mtime": 1750792521325, "results": "123", "hashOfConfig": "122"}, {"size": 2319, "mtime": 1750097912302, "results": "124", "hashOfConfig": "122"}, {"size": 7624, "mtime": 1750553525426, "results": "125", "hashOfConfig": "122"}, {"size": 14858, "mtime": 1751262846449, "results": "126", "hashOfConfig": "122"}, {"size": 20425, "mtime": 1751262275567, "results": "127", "hashOfConfig": "122"}, {"size": 17084, "mtime": 1750793583840, "results": "128", "hashOfConfig": "122"}, {"size": 16658, "mtime": 1751262302975, "results": "129", "hashOfConfig": "122"}, {"size": 17586, "mtime": 1751262417947, "results": "130", "hashOfConfig": "122"}, {"size": 10604, "mtime": 1750383021571, "results": "131", "hashOfConfig": "122"}, {"size": 478, "mtime": 1750097913004, "results": "132", "hashOfConfig": "122"}, {"size": 9927, "mtime": 1751666689275, "results": "133", "hashOfConfig": "122"}, {"size": 8810, "mtime": 1750097913235, "results": "134", "hashOfConfig": "122"}, {"size": 10535, "mtime": 1750556509149, "results": "135", "hashOfConfig": "122"}, {"size": 6923, "mtime": 1751566002765, "results": "136", "hashOfConfig": "122"}, {"size": 8673, "mtime": 1751666948267, "results": "137", "hashOfConfig": "122"}, {"size": 365, "mtime": 1750097913049, "results": "138", "hashOfConfig": "122"}, {"size": 15089, "mtime": 1750097913508, "results": "139", "hashOfConfig": "122"}, {"size": 32153, "mtime": 1750266312726, "results": "140", "hashOfConfig": "122"}, {"size": 2258, "mtime": 1750223072241, "results": "141", "hashOfConfig": "122"}, {"size": 16680, "mtime": 1750266309161, "results": "142", "hashOfConfig": "122"}, {"size": 40452, "mtime": 1750097913762, "results": "143", "hashOfConfig": "122"}, {"size": 27427, "mtime": 1750097913827, "results": "144", "hashOfConfig": "122"}, {"size": 710, "mtime": 1750771153337, "results": "145", "hashOfConfig": "122"}, {"size": 9966, "mtime": 1751565945233, "results": "146", "hashOfConfig": "122"}, {"size": 3643, "mtime": 1749492521016, "results": "147", "hashOfConfig": "122"}, {"size": 287, "mtime": 1748519405030, "results": "148", "hashOfConfig": "122"}, {"size": 1057, "mtime": 1750222740893, "results": "149", "hashOfConfig": "122"}, {"size": 927, "mtime": 1750097912256, "results": "150", "hashOfConfig": "122"}, {"size": 914, "mtime": 1750097914558, "results": "151", "hashOfConfig": "122"}, {"size": 3089, "mtime": 1749090290031, "results": "152", "hashOfConfig": "122"}, {"size": 877, "mtime": 1750217695638, "results": "153", "hashOfConfig": "122"}, {"size": 11276, "mtime": 1750556485369, "results": "154", "hashOfConfig": "122"}, {"size": 6563, "mtime": 1750793626203, "results": "155", "hashOfConfig": "122"}, {"size": 6311, "mtime": 1750097914699, "results": "156", "hashOfConfig": "122"}, {"size": 3240, "mtime": 1750793566845, "results": "157", "hashOfConfig": "122"}, {"size": 5632, "mtime": 1750794392438, "results": "158", "hashOfConfig": "122"}, {"size": 5078, "mtime": 1750569869152, "results": "159", "hashOfConfig": "122"}, {"size": 3563, "mtime": 1750097914873, "results": "160", "hashOfConfig": "122"}, {"size": 2775, "mtime": 1750793651265, "results": "161", "hashOfConfig": "122"}, {"size": 2076, "mtime": 1750793667709, "results": "162", "hashOfConfig": "122"}, {"size": 4239, "mtime": 1750772157366, "results": "163", "hashOfConfig": "122"}, {"size": 3361, "mtime": 1750793682868, "results": "164", "hashOfConfig": "122"}, {"size": 2596, "mtime": 1750281496798, "results": "165", "hashOfConfig": "122"}, {"size": 39008, "mtime": 1751297735195, "results": "166", "hashOfConfig": "122"}, {"size": 1524, "mtime": 1750097915309, "results": "167", "hashOfConfig": "122"}, {"size": 10531, "mtime": 1750793756001, "results": "168", "hashOfConfig": "122"}, {"size": 2668, "mtime": 1750097915369, "results": "169", "hashOfConfig": "122"}, {"size": 21559, "mtime": 1750771731594, "results": "170", "hashOfConfig": "122"}, {"size": 5876, "mtime": 1750770267031, "results": "171", "hashOfConfig": "122"}, {"size": 5422, "mtime": 1751262971390, "results": "172", "hashOfConfig": "122"}, {"size": 9790, "mtime": 1750795653633, "results": "173", "hashOfConfig": "122"}, {"size": 16649, "mtime": 1751263027328, "results": "174", "hashOfConfig": "122"}, {"size": 9835, "mtime": 1750281337684, "results": "175", "hashOfConfig": "122"}, {"size": 9375, "mtime": 1750787739602, "results": "176", "hashOfConfig": "122"}, {"size": 9223, "mtime": 1750694351391, "results": "177", "hashOfConfig": "122"}, {"size": 2235, "mtime": 1750097916496, "results": "178", "hashOfConfig": "122"}, {"size": 4572, "mtime": 1750097916550, "results": "179", "hashOfConfig": "122"}, {"size": 2050, "mtime": 1750097916563, "results": "180", "hashOfConfig": "122"}, {"size": 5015, "mtime": 1750267481350, "results": "181", "hashOfConfig": "122"}, {"size": 2260, "mtime": 1750097916610, "results": "182", "hashOfConfig": "122"}, {"size": 2342, "mtime": 1751089791672, "results": "183", "hashOfConfig": "122"}, {"size": 2917, "mtime": 1750097916636, "results": "184", "hashOfConfig": "122"}, {"size": 2467, "mtime": 1751089447830, "results": "185", "hashOfConfig": "122"}, {"size": 1088, "mtime": 1750097916677, "results": "186", "hashOfConfig": "122"}, {"size": 6595, "mtime": 1750442081530, "results": "187", "hashOfConfig": "122"}, {"size": 3952, "mtime": 1751089456301, "results": "188", "hashOfConfig": "122"}, {"size": 4624, "mtime": 1750097917041, "results": "189", "hashOfConfig": "122"}, {"size": 4749, "mtime": 1750787760535, "results": "190", "hashOfConfig": "122"}, {"size": 18348, "mtime": 1750441898294, "results": "191", "hashOfConfig": "122"}, {"size": 815, "mtime": 1751089750433, "results": "192", "hashOfConfig": "122"}, {"size": 635, "mtime": 1750097917249, "results": "193", "hashOfConfig": "122"}, {"size": 5631, "mtime": 1751262630924, "results": "194", "hashOfConfig": "122"}, {"size": 1079, "mtime": 1750097917259, "results": "195", "hashOfConfig": "122"}, {"size": 1683, "mtime": 1750097917306, "results": "196", "hashOfConfig": "122"}, {"size": 819, "mtime": 1750097917315, "results": "197", "hashOfConfig": "122"}, {"size": 5954, "mtime": 1751089761220, "results": "198", "hashOfConfig": "122"}, {"size": 382, "mtime": 1750097917361, "results": "199", "hashOfConfig": "122"}, {"size": 589, "mtime": 1750097917372, "results": "200", "hashOfConfig": "122"}, {"size": 3947, "mtime": 1750097917916, "results": "201", "hashOfConfig": "122"}, {"size": 3279, "mtime": 1751089710552, "results": "202", "hashOfConfig": "122"}, {"size": 1975, "mtime": 1750097917966, "results": "203", "hashOfConfig": "122"}, {"size": 8748, "mtime": 1750097917988, "results": "204", "hashOfConfig": "122"}, {"size": 1952, "mtime": 1750097918008, "results": "205", "hashOfConfig": "122"}, {"size": 7459, "mtime": 1750097918044, "results": "206", "hashOfConfig": "122"}, {"size": 8715, "mtime": 1750097918090, "results": "207", "hashOfConfig": "122"}, {"size": 44517, "mtime": 1750796195453, "results": "208", "hashOfConfig": "122"}, {"size": 3979, "mtime": 1750794635711, "results": "209", "hashOfConfig": "122"}, {"size": 9593, "mtime": 1751835422840, "results": "210", "hashOfConfig": "122"}, {"size": 13590, "mtime": 1750795729435, "results": "211", "hashOfConfig": "122"}, {"size": 5461, "mtime": 1750794927868, "results": "212", "hashOfConfig": "122"}, {"size": 15006, "mtime": 1748519405047, "results": "213", "hashOfConfig": "122"}, {"size": 4507, "mtime": 1749588879246, "results": "214", "hashOfConfig": "122"}, {"size": 2758, "mtime": 1751666251823, "results": "215", "hashOfConfig": "122"}, {"size": 1996, "mtime": 1750058002911, "results": "216", "hashOfConfig": "122"}, {"size": 118919, "mtime": 1751835186024, "results": "217", "hashOfConfig": "122"}, {"size": 114, "mtime": 1750058003810, "results": "218", "hashOfConfig": "122"}, {"size": 625, "mtime": 1751261140193, "results": "219", "hashOfConfig": "122"}, {"size": 3934, "mtime": 1751602688553, "results": "220", "hashOfConfig": "122"}, {"size": 2222, "mtime": 1750553152059, "results": "221", "hashOfConfig": "122"}, {"size": 3936, "mtime": 1751602705514, "results": "222", "hashOfConfig": "122"}, {"size": 12402, "mtime": 1750222554841, "results": "223", "hashOfConfig": "122"}, {"size": 3387, "mtime": 1749582311992, "results": "224", "hashOfConfig": "122"}, {"size": 4265, "mtime": 1750694300273, "results": "225", "hashOfConfig": "122"}, {"size": 7745, "mtime": 1750222559034, "results": "226", "hashOfConfig": "122"}, {"size": 1619, "mtime": 1750793552175, "results": "227", "hashOfConfig": "122"}, {"size": 9921, "mtime": 1750796177291, "results": "228", "hashOfConfig": "122"}, {"size": 5398, "mtime": 1750282113311, "results": "229", "hashOfConfig": "122"}, {"size": 6624, "mtime": 1751602792795, "results": "230", "hashOfConfig": "122"}, {"size": 7821, "mtime": 1750796220356, "results": "231", "hashOfConfig": "122"}, {"size": 6022, "mtime": 1750771203086, "results": "232", "hashOfConfig": "122"}, {"size": 172, "mtime": 1748519405052, "results": "233", "hashOfConfig": "122"}, {"size": 10632, "mtime": 1750218396315, "results": "234", "hashOfConfig": "235"}, {"size": 12928, "mtime": 1751071096956, "results": "236", "hashOfConfig": "122"}, {"size": 3941, "mtime": 1748915794149, "results": "237", "hashOfConfig": "235"}, {"size": 12167, "mtime": 1750223700806, "results": "238", "hashOfConfig": "122"}, {"size": 657, "mtime": 1748519405054, "results": "239", "hashOfConfig": "122"}, {"size": 35, "mtime": 1751061361940, "results": "240", "hashOfConfig": "122"}, {"size": 2495, "mtime": 1751075630966, "results": "241", "hashOfConfig": "235"}, {"size": 1210, "mtime": 1751261018750, "results": "242", "hashOfConfig": "122"}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8d3bfx", {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 13, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xktfx2", {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\api-keys\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\billing\\page.tsx", ["603"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\models\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx", ["604", "605", "606", "607"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx", ["608", "609", "610", "611"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\recommendation-metrics\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\settings\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\usage\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\contact-sales\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\quickstart\\python\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\features\\page.tsx", ["612"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\home\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\cookies\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\dpa\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\notice\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\privacy\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\terms\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\login\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\pricing\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\verify-email\\[token]\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\api\\health\\route.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\register\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\sitemap.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\AdminRoute.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\InitialApiKeyModal.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\CurrentPlanCard.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\PlanCard.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\PlansGrid.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\ApiStatus.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\BillingButton.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\BillingPortalButton.tsx", ["613"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\ConfidenceMetricsChart.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\DateRangeSelector.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\EmailVerificationBanner.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\GettingStartedChecklist.tsx", ["614", "615", "616"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\Header.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\MetricRecommendations.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\QuickActions.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\RecommendationMetricsChart.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\SandboxResetButton.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\Sidebar.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\UsageChart.tsx", [], ["617", "618", "619", "620"], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\UsageDashboard.tsx", [], ["621"], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\documentation\\iconography-guide.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\pipeline\\DataIngestionModal.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\pipeline\\TrainingModal.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\accordion.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\alert-dialog.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\alert.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\animation-examples.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\badge.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\button.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\calendar.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\card.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\checkbox.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\contrast-improvements.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\dialog.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\form.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\icon.tsx", ["622"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\iconography-improvements-showcase.tsx", ["623", "624"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\input.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\label.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\logo.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\popover.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\progress.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\select.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\skeleton.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\sonner.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\spacing-system.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\table.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tabs.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tooltip-helper.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tooltip.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\typography-showcase.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\ui-improvements-showcase.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\analysis.ts", ["625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api\\recommendation-metrics.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\auth.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\chart-utils.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\constants.ts", ["643"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\error-handler.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\api-client.ts", ["644"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\migration-helper.ts", ["645", "646"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\rayuelaAPI.ts", ["647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\schemas\\index.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\index.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useIngestionJobs.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useModels.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useTrainingJobs.ts", ["660"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\recommendationRules.ts", ["661"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\seo.ts", ["662"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useAccountInfo.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useApiKeys.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\usePlans.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useRecommendationMetrics.ts", ["663"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useUsageHistory.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useUsageSummary.ts", ["664"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils\\billing.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils\\format.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\fetch-openapi.js", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\fetch-openapi.ts", ["665", "666", "667", "668", "669"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\generate-og-image.js", ["670", "671"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\test-openapi-generation.ts", ["672", "673", "674", "675"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\types\\checklist.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\patch-openapi-static.js", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useJobTable.ts", [], [], {"ruleId": "676", "severity": 1, "message": "677", "line": 78, "column": 31, "nodeType": "678", "messageId": "679", "endLine": 78, "endColumn": 34, "suggestions": "680"}, {"ruleId": "681", "severity": 1, "message": "682", "line": 4, "column": 29, "nodeType": null, "messageId": "683", "endLine": 4, "endColumn": 44}, {"ruleId": "681", "severity": 1, "message": "684", "line": 4, "column": 58, "nodeType": null, "messageId": "683", "endLine": 4, "endColumn": 67}, {"ruleId": "681", "severity": 1, "message": "685", "line": 12, "column": 3, "nodeType": null, "messageId": "683", "endLine": 12, "endColumn": 14}, {"ruleId": "681", "severity": 1, "message": "686", "line": 23, "column": 8, "nodeType": null, "messageId": "683", "endLine": 23, "endColumn": 12}, {"ruleId": "681", "severity": 1, "message": "682", "line": 4, "column": 29, "nodeType": null, "messageId": "683", "endLine": 4, "endColumn": 44}, {"ruleId": "681", "severity": 1, "message": "684", "line": 4, "column": 58, "nodeType": null, "messageId": "683", "endLine": 4, "endColumn": 67}, {"ruleId": "681", "severity": 1, "message": "685", "line": 11, "column": 3, "nodeType": null, "messageId": "683", "endLine": 11, "endColumn": 14}, {"ruleId": "681", "severity": 1, "message": "686", "line": 21, "column": 8, "nodeType": null, "messageId": "683", "endLine": 21, "endColumn": 12}, {"ruleId": "681", "severity": 1, "message": "687", "line": 107, "column": 39, "nodeType": null, "messageId": "683", "endLine": 107, "endColumn": 44}, {"ruleId": "688", "severity": 1, "message": "689", "line": 10, "column": 11, "nodeType": "690", "messageId": "691", "endLine": 10, "endColumn": 35, "suggestions": "692"}, {"ruleId": "693", "severity": 1, "message": "694", "line": 401, "column": 6, "nodeType": "695", "endLine": 401, "endColumn": 8, "suggestions": "696"}, {"ruleId": "693", "severity": 1, "message": "697", "line": 478, "column": 6, "nodeType": "695", "endLine": 478, "endColumn": 30, "suggestions": "698"}, {"ruleId": "693", "severity": 1, "message": "697", "line": 552, "column": 6, "nodeType": "695", "endLine": 552, "endColumn": 64, "suggestions": "699"}, {"ruleId": "681", "severity": 1, "message": "700", "line": 128, "column": 9, "nodeType": null, "messageId": "683", "endLine": 128, "endColumn": 21, "suppressions": "701"}, {"ruleId": "681", "severity": 1, "message": "702", "line": 144, "column": 9, "nodeType": null, "messageId": "683", "endLine": 144, "endColumn": 20, "suppressions": "703"}, {"ruleId": "681", "severity": 1, "message": "704", "line": 159, "column": 9, "nodeType": null, "messageId": "683", "endLine": 159, "endColumn": 24, "suppressions": "705"}, {"ruleId": "681", "severity": 1, "message": "706", "line": 200, "column": 9, "nodeType": null, "messageId": "683", "endLine": 200, "endColumn": 23, "suppressions": "707"}, {"ruleId": "681", "severity": 1, "message": "708", "line": 139, "column": 9, "nodeType": null, "messageId": "683", "endLine": 139, "endColumn": 15, "suppressions": "709"}, {"ruleId": "688", "severity": 1, "message": "689", "line": 91, "column": 11, "nodeType": "690", "messageId": "691", "endLine": 91, "endColumn": 26, "suggestions": "710"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 270, "column": 66, "nodeType": "678", "messageId": "679", "endLine": 270, "endColumn": 69, "suggestions": "711"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 293, "column": 82, "nodeType": "678", "messageId": "679", "endLine": 293, "endColumn": 85, "suggestions": "712"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 69, "column": 43, "nodeType": "678", "messageId": "679", "endLine": 69, "endColumn": 46, "suggestions": "713"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 221, "column": 80, "nodeType": "678", "messageId": "679", "endLine": 221, "endColumn": 83, "suggestions": "714"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 239, "column": 64, "nodeType": "678", "messageId": "679", "endLine": 239, "endColumn": 67, "suggestions": "715"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 240, "column": 70, "nodeType": "678", "messageId": "679", "endLine": 240, "endColumn": 73, "suggestions": "716"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 244, "column": 89, "nodeType": "678", "messageId": "679", "endLine": 244, "endColumn": 92, "suggestions": "717"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 246, "column": 65, "nodeType": "678", "messageId": "679", "endLine": 246, "endColumn": 68, "suggestions": "718"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 259, "column": 42, "nodeType": "678", "messageId": "679", "endLine": 259, "endColumn": 45, "suggestions": "719"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 261, "column": 60, "nodeType": "678", "messageId": "679", "endLine": 261, "endColumn": 63, "suggestions": "720"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 270, "column": 64, "nodeType": "678", "messageId": "679", "endLine": 270, "endColumn": 67, "suggestions": "721"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 271, "column": 70, "nodeType": "678", "messageId": "679", "endLine": 271, "endColumn": 73, "suggestions": "722"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 276, "column": 59, "nodeType": "678", "messageId": "679", "endLine": 276, "endColumn": 62, "suggestions": "723"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 292, "column": 62, "nodeType": "678", "messageId": "679", "endLine": 292, "endColumn": 65, "suggestions": "724"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 301, "column": 61, "nodeType": "678", "messageId": "679", "endLine": 301, "endColumn": 64, "suggestions": "725"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 325, "column": 73, "nodeType": "678", "messageId": "679", "endLine": 325, "endColumn": 76, "suggestions": "726"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 443, "column": 27, "nodeType": "678", "messageId": "679", "endLine": 443, "endColumn": 30, "suggestions": "727"}, {"ruleId": "681", "severity": 1, "message": "728", "line": 571, "column": 3, "nodeType": null, "messageId": "683", "endLine": 571, "endColumn": 14}, {"ruleId": "676", "severity": 1, "message": "677", "line": 681, "column": 67, "nodeType": "678", "messageId": "679", "endLine": 681, "endColumn": 70, "suggestions": "729"}, {"ruleId": "681", "severity": 1, "message": "730", "line": 816, "column": 22, "nodeType": null, "messageId": "683", "endLine": 816, "endColumn": 27}, {"ruleId": "676", "severity": 1, "message": "677", "line": 223, "column": 74, "nodeType": "678", "messageId": "679", "endLine": 223, "endColumn": 77, "suggestions": "731"}, {"ruleId": "688", "severity": 1, "message": "689", "line": 56, "column": 18, "nodeType": "690", "messageId": "691", "endLine": 56, "endColumn": 37, "suggestions": "732"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 47, "column": 19, "nodeType": "678", "messageId": "679", "endLine": 47, "endColumn": 22, "suggestions": "733"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 75, "column": 22, "nodeType": "678", "messageId": "679", "endLine": 75, "endColumn": 25, "suggestions": "734"}, {"ruleId": null, "message": "735", "line": 271, "column": 1, "severity": 1, "nodeType": null, "fix": "736"}, {"ruleId": null, "message": "735", "line": 304, "column": 1, "severity": 1, "nodeType": null, "fix": "737"}, {"ruleId": null, "message": "735", "line": 345, "column": 1, "severity": 1, "nodeType": null, "fix": "738"}, {"ruleId": null, "message": "735", "line": 554, "column": 1, "severity": 1, "nodeType": null, "fix": "739"}, {"ruleId": null, "message": "735", "line": 580, "column": 1, "severity": 1, "nodeType": null, "fix": "740"}, {"ruleId": null, "message": "735", "line": 880, "column": 1, "severity": 1, "nodeType": null, "fix": "741"}, {"ruleId": null, "message": "735", "line": 964, "column": 1, "severity": 1, "nodeType": null, "fix": "742"}, {"ruleId": null, "message": "735", "line": 1048, "column": 1, "severity": 1, "nodeType": null, "fix": "743"}, {"ruleId": null, "message": "735", "line": 1065, "column": 1, "severity": 1, "nodeType": null, "fix": "744"}, {"ruleId": null, "message": "735", "line": 1119, "column": 1, "severity": 1, "nodeType": null, "fix": "745"}, {"ruleId": null, "message": "735", "line": 1164, "column": 1, "severity": 1, "nodeType": null, "fix": "746"}, {"ruleId": null, "message": "735", "line": 1187, "column": 1, "severity": 1, "nodeType": null, "fix": "747"}, {"ruleId": null, "message": "735", "line": 1330, "column": 1, "severity": 1, "nodeType": null, "fix": "748"}, {"ruleId": "681", "severity": 1, "message": "749", "line": 86, "column": 32, "nodeType": null, "messageId": "683", "endLine": 86, "endColumn": 42}, {"ruleId": "676", "severity": 1, "message": "677", "line": 32, "column": 29, "nodeType": "678", "messageId": "679", "endLine": 32, "endColumn": 32, "suggestions": "750"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 86, "column": 101, "nodeType": "678", "messageId": "679", "endLine": 86, "endColumn": 104, "suggestions": "751"}, {"ruleId": "681", "severity": 1, "message": "752", "line": 173, "column": 11, "nodeType": null, "messageId": "683", "endLine": 173, "endColumn": 33}, {"ruleId": "676", "severity": 1, "message": "677", "line": 49, "column": 28, "nodeType": "678", "messageId": "679", "endLine": 49, "endColumn": 31, "suggestions": "753"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 24, "column": 36, "nodeType": "678", "messageId": "679", "endLine": 24, "endColumn": 39, "suggestions": "754"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 89, "column": 38, "nodeType": "678", "messageId": "679", "endLine": 89, "endColumn": 41, "suggestions": "755"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 158, "column": 17, "nodeType": "678", "messageId": "679", "endLine": 158, "endColumn": 20, "suggestions": "756"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 216, "column": 17, "nodeType": "678", "messageId": "679", "endLine": 216, "endColumn": 20, "suggestions": "757"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 217, "column": 17, "nodeType": "678", "messageId": "679", "endLine": 217, "endColumn": 20, "suggestions": "758"}, {"ruleId": "759", "severity": 1, "message": "760", "line": 4, "column": 12, "nodeType": "761", "messageId": "762", "endLine": 4, "endColumn": 25}, {"ruleId": "759", "severity": 1, "message": "760", "line": 5, "column": 14, "nodeType": "761", "messageId": "762", "endLine": 5, "endColumn": 29}, {"ruleId": "676", "severity": 1, "message": "677", "line": 22, "column": 13, "nodeType": "678", "messageId": "679", "endLine": 22, "endColumn": 16, "suggestions": "763"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 37, "column": 79, "nodeType": "678", "messageId": "679", "endLine": 37, "endColumn": 82, "suggestions": "764"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 260, "column": 37, "nodeType": "678", "messageId": "679", "endLine": 260, "endColumn": 40, "suggestions": "765"}, {"ruleId": "676", "severity": 1, "message": "677", "line": 260, "column": 62, "nodeType": "678", "messageId": "679", "endLine": 260, "endColumn": 65, "suggestions": "766"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["767", "768"], "@typescript-eslint/no-unused-vars", "'CardDescription' is defined but never used.", "unusedVar", "'CardTitle' is defined but never used.", "'ChevronLeft' is defined but never used.", "'Link' is defined but never used.", "'index' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["769"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'accountData' and 'getChecklistStatus'. Either include them or remove the dependency array.", "ArrayExpression", ["770"], "React Hook useEffect has a missing dependency: 'updateChecklistState'. Either include it or remove the dependency array.", ["771"], ["772"], "'apiCallsData' is assigned a value but never used.", ["773"], "'storageData' is assigned a value but never used.", ["774"], "'apiCallsOptions' is assigned a value but never used.", ["775"], "'storageOptions' is assigned a value but never used.", ["776"], "'totals' is assigned a value but never used.", ["777"], ["778"], ["779", "780"], ["781", "782"], ["783", "784"], ["785", "786"], ["787", "788"], ["789", "790"], ["791", "792"], ["793", "794"], ["795", "796"], ["797", "798"], ["799", "800"], ["801", "802"], ["803", "804"], ["805", "806"], ["807", "808"], ["809", "810"], ["811", "812"], "'isNewApiKey' is defined but never used.", ["813", "814"], "'model' is defined but never used.", ["815", "816"], ["817"], ["818", "819"], ["820", "821"], "Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-redeclare').", {"range": "822", "text": "823"}, {"range": "824", "text": "823"}, {"range": "825", "text": "823"}, {"range": "826", "text": "823"}, {"range": "827", "text": "823"}, {"range": "828", "text": "823"}, {"range": "829", "text": "823"}, {"range": "830", "text": "823"}, {"range": "831", "text": "823"}, {"range": "832", "text": "823"}, {"range": "833", "text": "823"}, {"range": "834", "text": "823"}, {"range": "835", "text": "823"}, "'parameters' is defined but never used.", ["836", "837"], ["838", "839"], "'ModelConfidenceMetrics' is defined but never used.", ["840", "841"], ["842", "843"], ["844", "845"], ["846", "847"], ["848", "849"], ["850", "851"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["852", "853"], ["854", "855"], ["856", "857"], ["858", "859"], {"messageId": "860", "fix": "861", "desc": "862"}, {"messageId": "863", "fix": "864", "desc": "865"}, {"messageId": "866", "fix": "867", "desc": "868"}, {"desc": "869", "fix": "870"}, {"desc": "871", "fix": "872"}, {"desc": "873", "fix": "874"}, {"kind": "875", "justification": "876"}, {"kind": "875", "justification": "876"}, {"kind": "875", "justification": "876"}, {"kind": "875", "justification": "876"}, {"kind": "875", "justification": "876"}, {"messageId": "866", "fix": "877", "desc": "868"}, {"messageId": "860", "fix": "878", "desc": "862"}, {"messageId": "863", "fix": "879", "desc": "865"}, {"messageId": "860", "fix": "880", "desc": "862"}, {"messageId": "863", "fix": "881", "desc": "865"}, {"messageId": "860", "fix": "882", "desc": "862"}, {"messageId": "863", "fix": "883", "desc": "865"}, {"messageId": "860", "fix": "884", "desc": "862"}, {"messageId": "863", "fix": "885", "desc": "865"}, {"messageId": "860", "fix": "886", "desc": "862"}, {"messageId": "863", "fix": "887", "desc": "865"}, {"messageId": "860", "fix": "888", "desc": "862"}, {"messageId": "863", "fix": "889", "desc": "865"}, {"messageId": "860", "fix": "890", "desc": "862"}, {"messageId": "863", "fix": "891", "desc": "865"}, {"messageId": "860", "fix": "892", "desc": "862"}, {"messageId": "863", "fix": "893", "desc": "865"}, {"messageId": "860", "fix": "894", "desc": "862"}, {"messageId": "863", "fix": "895", "desc": "865"}, {"messageId": "860", "fix": "896", "desc": "862"}, {"messageId": "863", "fix": "897", "desc": "865"}, {"messageId": "860", "fix": "898", "desc": "862"}, {"messageId": "863", "fix": "899", "desc": "865"}, {"messageId": "860", "fix": "900", "desc": "862"}, {"messageId": "863", "fix": "901", "desc": "865"}, {"messageId": "860", "fix": "902", "desc": "862"}, {"messageId": "863", "fix": "903", "desc": "865"}, {"messageId": "860", "fix": "904", "desc": "862"}, {"messageId": "863", "fix": "905", "desc": "865"}, {"messageId": "860", "fix": "906", "desc": "862"}, {"messageId": "863", "fix": "907", "desc": "865"}, {"messageId": "860", "fix": "908", "desc": "862"}, {"messageId": "863", "fix": "909", "desc": "865"}, {"messageId": "860", "fix": "910", "desc": "862"}, {"messageId": "863", "fix": "911", "desc": "865"}, {"messageId": "860", "fix": "912", "desc": "862"}, {"messageId": "863", "fix": "913", "desc": "865"}, {"messageId": "860", "fix": "914", "desc": "862"}, {"messageId": "863", "fix": "915", "desc": "865"}, {"messageId": "866", "fix": "916", "desc": "868"}, {"messageId": "860", "fix": "917", "desc": "862"}, {"messageId": "863", "fix": "918", "desc": "865"}, {"messageId": "860", "fix": "919", "desc": "862"}, {"messageId": "863", "fix": "920", "desc": "865"}, [7341, 7400], " ", [8329, 8388], [9476, 9535], [14631, 14690], [15144, 15203], [22506, 22565], [24486, 24545], [26449, 26508], [26802, 26861], [28127, 28186], [29054, 29113], [29776, 29835], [33997, 34056], {"messageId": "860", "fix": "921", "desc": "862"}, {"messageId": "863", "fix": "922", "desc": "865"}, {"messageId": "860", "fix": "923", "desc": "862"}, {"messageId": "863", "fix": "924", "desc": "865"}, {"messageId": "860", "fix": "925", "desc": "862"}, {"messageId": "863", "fix": "926", "desc": "865"}, {"messageId": "860", "fix": "927", "desc": "862"}, {"messageId": "863", "fix": "928", "desc": "865"}, {"messageId": "860", "fix": "929", "desc": "862"}, {"messageId": "863", "fix": "930", "desc": "865"}, {"messageId": "860", "fix": "931", "desc": "862"}, {"messageId": "863", "fix": "932", "desc": "865"}, {"messageId": "860", "fix": "933", "desc": "862"}, {"messageId": "863", "fix": "934", "desc": "865"}, {"messageId": "860", "fix": "935", "desc": "862"}, {"messageId": "863", "fix": "936", "desc": "865"}, {"messageId": "860", "fix": "937", "desc": "862"}, {"messageId": "863", "fix": "938", "desc": "865"}, {"messageId": "860", "fix": "939", "desc": "862"}, {"messageId": "863", "fix": "940", "desc": "865"}, {"messageId": "860", "fix": "941", "desc": "862"}, {"messageId": "863", "fix": "942", "desc": "865"}, {"messageId": "860", "fix": "943", "desc": "862"}, {"messageId": "863", "fix": "944", "desc": "865"}, "suggestUnknown", {"range": "945", "text": "946"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "947", "text": "948"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceEmptyInterfaceWithSuper", {"range": "949", "text": "950"}, "Replace empty interface with a type alias.", "Update the dependencies array to be: [accountData, getChecklistStatus]", {"range": "951", "text": "952"}, "Update the dependencies array to be: [accountData, updateChecklistState, usageData]", {"range": "953", "text": "954"}, "Update the dependencies array to be: [accountData, usageData, isAccountLoading, isUsageLoading, updateChecklistState]", {"range": "955", "text": "956"}, "directive", "", {"range": "957", "text": "958"}, {"range": "959", "text": "946"}, {"range": "960", "text": "948"}, {"range": "961", "text": "946"}, {"range": "962", "text": "948"}, {"range": "963", "text": "946"}, {"range": "964", "text": "948"}, {"range": "965", "text": "946"}, {"range": "966", "text": "948"}, {"range": "967", "text": "946"}, {"range": "968", "text": "948"}, {"range": "969", "text": "946"}, {"range": "970", "text": "948"}, {"range": "971", "text": "946"}, {"range": "972", "text": "948"}, {"range": "973", "text": "946"}, {"range": "974", "text": "948"}, {"range": "975", "text": "946"}, {"range": "976", "text": "948"}, {"range": "977", "text": "946"}, {"range": "978", "text": "948"}, {"range": "979", "text": "946"}, {"range": "980", "text": "948"}, {"range": "981", "text": "946"}, {"range": "982", "text": "948"}, {"range": "983", "text": "946"}, {"range": "984", "text": "948"}, {"range": "985", "text": "946"}, {"range": "986", "text": "948"}, {"range": "987", "text": "946"}, {"range": "988", "text": "948"}, {"range": "989", "text": "946"}, {"range": "990", "text": "948"}, {"range": "991", "text": "946"}, {"range": "992", "text": "948"}, {"range": "993", "text": "946"}, {"range": "994", "text": "948"}, {"range": "995", "text": "946"}, {"range": "996", "text": "948"}, {"range": "997", "text": "998"}, {"range": "999", "text": "946"}, {"range": "1000", "text": "948"}, {"range": "1001", "text": "946"}, {"range": "1002", "text": "948"}, {"range": "1003", "text": "946"}, {"range": "1004", "text": "948"}, {"range": "1005", "text": "946"}, {"range": "1006", "text": "948"}, {"range": "1007", "text": "946"}, {"range": "1008", "text": "948"}, {"range": "1009", "text": "946"}, {"range": "1010", "text": "948"}, {"range": "1011", "text": "946"}, {"range": "1012", "text": "948"}, {"range": "1013", "text": "946"}, {"range": "1014", "text": "948"}, {"range": "1015", "text": "946"}, {"range": "1016", "text": "948"}, {"range": "1017", "text": "946"}, {"range": "1018", "text": "948"}, {"range": "1019", "text": "946"}, {"range": "1020", "text": "948"}, {"range": "1021", "text": "946"}, {"range": "1022", "text": "948"}, {"range": "1023", "text": "946"}, {"range": "1024", "text": "948"}, {"range": "1025", "text": "946"}, {"range": "1026", "text": "948"}, [2945, 2948], "unknown", [2945, 2948], "never", [308, 365], "type BillingPortalButtonProps = ButtonProps", [13677, 13679], "[account<PERSON><PERSON>, getChecklistStatus]", [16255, 16279], "[accountData, updateChecklistState, usageData]", [18807, 18865], "[accountData, usageData, isAccountLoading, isUsageLoading, updateChecklistState]", [2866, 2999], "type StatusIconProps = Omit<SemanticIconProps, 'context'>", [12580, 12583], [12580, 12583], [13546, 13549], [13546, 13549], [1886, 1889], [1886, 1889], [6742, 6745], [6742, 6745], [7446, 7449], [7446, 7449], [7556, 7559], [7556, 7559], [7869, 7872], [7869, 7872], [7997, 8000], [7997, 8000], [8383, 8386], [8383, 8386], [8496, 8499], [8496, 8499], [8840, 8843], [8840, 8843], [8932, 8935], [8932, 8935], [9268, 9271], [9268, 9271], [9758, 9761], [9758, 9761], [10112, 10115], [10112, 10115], [11162, 11165], [11162, 11165], [15414, 15417], [15414, 15417], [24288, 24291], [24288, 24291], [14234, 14237], [14234, 14237], [1336, 1395], "type CustomRequestConfig = AxiosRequestConfig", [1048, 1051], [1048, 1051], [1822, 1825], [1822, 1825], [1236, 1239], [1236, 1239], [2062, 2065], [2062, 2065], [1278, 1281], [1278, 1281], [849, 852], [849, 852], [2722, 2725], [2722, 2725], [4930, 4933], [4930, 4933], [7576, 7579], [7576, 7579], [7625, 7628], [7625, 7628], [699, 702], [699, 702], [1037, 1040], [1037, 1040], [8321, 8324], [8321, 8324], [8346, 8349], [8346, 8349]]