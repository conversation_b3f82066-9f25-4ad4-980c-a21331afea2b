(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5507],{381:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},996:(e,s,a)=>{Promise.resolve().then(a.bind(a,5812))},3365:(e,s,a)=>{"use strict";a.d(s,{G:()=>r});var t=a(2115),n=a(2656);function r(){let[e,s]=(0,t.useState)([]),[a,r]=(0,t.useState)(!0),[l,i]=(0,t.useState)(null),d=async()=>{try{r(!0),i(null);try{let e=[].map(e=>{var s,a,t,n;let r={...e,model_name:null!=(t=null==(s=e.model)?void 0:s.artifact_name)?t:"Recommendation Model",model_version:null!=(n=null==(a=e.model)?void 0:a.artifact_version)?n:"v1.0",status:e.status.toUpperCase(),parameters:e.parameters?Object.fromEntries(Object.entries(e.parameters).filter(e=>{let[,s]=e;return"number"==typeof s||"string"==typeof s})):void 0,metrics:e.metrics?Object.fromEntries(Object.entries(e.metrics).filter(e=>{let[,s]=e;return"number"==typeof s})):void 0};if(r.started_at&&r.completed_at){let e=new Date(r.started_at).getTime(),s=new Date(r.completed_at).getTime();r.duration=Math.round((s-e)/1e3)}return r});s(e);return}catch(e){i("Error fetching training jobs"),console.error("Error fetching training jobs:",e)}}catch(e){i(e instanceof Error?e.message:"Error loading training jobs"),console.error("Error loading training jobs:",e)}finally{r(!1)}},c=async e=>{try{return(await (0,n._C)().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(e)).data}catch(e){throw console.error("Error fetching training job status:",e),e}},o=async e=>{try{let e=await (0,n._C)().trainModelsApiV1PipelineTrainPost();return await d(),e.data}catch(e){throw console.error("Error starting training:",e),e}};return(0,t.useEffect)(()=>{d()},[]),{jobs:e,isLoading:a,error:l,fetchJobs:d,getJobStatus:c,startTraining:o}}},5812:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>M});var t=a(5155),n=a(2115),r=a(6695),l=a(285),i=a(8856),d=a(5127),c=a(5365),o=a(5690),m=a(9376),h=a(5339),x=a(6932),j=a(7924),u=a(2657),p=a(133),g=a(3008),v=a(3439),N=a(2523),b=a(5057),f=a(9409),y=a(4165),_=a(3365),w=a(4113),E=a(646),A=a(381),C=a(1154);function F(e){let{onTrainingStart:s,trigger:a}=e,[r,i]=(0,n.useState)(!1),[d,x]=(0,n.useState)("hybrid"),[j,u]=(0,n.useState)(!1),[p,g]=(0,n.useState)({learning_rate:.001,epochs:50,batch_size:32,embedding_dim:64,regularization:.001}),[v,_]=(0,n.useState)(!1),[w,F]=(0,n.useState)(null),[k,z]=(0,n.useState)(!1),M=(e,s)=>{g(a=>({...a,[e]:s}))},S=async()=>{_(!0),F(null);try{let e={model_type:d,force:!1};j&&(e.hyperparameters={learning_rate:p.learning_rate,epochs:p.epochs,batch_size:p.batch_size,embedding_dim:p.embedding_dim,regularization:p.regularization});let a=await s(e);console.log("Training started:",a),z(!0),setTimeout(()=>{i(!1),z(!1),u(!1),x("hybrid")},3e3)}catch(e){F(e instanceof Error?e.message:"Error iniciando entrenamiento")}finally{_(!1)}};return(0,t.jsxs)(y.lG,{open:r,onOpenChange:i,children:[(0,t.jsx)(y.zM,{asChild:!0,children:a||(0,t.jsxs)(l.Button,{children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Nuevo Entrenamiento"]})}),(0,t.jsxs)(y.Cf,{className:"sm:max-w-lg",children:[(0,t.jsxs)(y.c7,{children:[(0,t.jsx)(y.L3,{children:"Nuevo Entrenamiento de Modelo"}),(0,t.jsx)(y.rr,{children:"Inicia el entrenamiento de un modelo de recomendaci\xf3n personalizado con tus datos"})]}),k?(0,t.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,t.jsx)(E.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,t.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Entrenamiento iniciado!"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu modelo est\xe1 siendo entrenado"})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{htmlFor:"modelType",children:"Tipo de modelo"}),(0,t.jsxs)(f.l6,{value:d,onValueChange:e=>x(e),children:[(0,t.jsx)(f.bq,{children:(0,t.jsx)(f.yv,{placeholder:"Selecciona el tipo de modelo"})}),(0,t.jsxs)(f.gC,{children:[(0,t.jsx)(f.eb,{value:"hybrid",children:"H\xedbrido (recomendado)"}),(0,t.jsx)(f.eb,{value:"collaborative",children:"Filtrado colaborativo"}),(0,t.jsx)(f.eb,{value:"content",children:"Basado en contenido"})]})]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["hybrid"===d&&"Combina m\xfaltiples t\xe9cnicas para mejores resultados","collaborative"===d&&"Basado en comportamiento de usuarios similares","content"===d&&"Basado en caracter\xedsticas de productos"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{id:"advanced",type:"checkbox",className:"rounded border-gray-300",checked:j,onChange:e=>u(e.target.checked)}),(0,t.jsxs)(b.J,{htmlFor:"advanced",className:"flex items-center",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"Configuraci\xf3n avanzada"]})]}),j&&(0,t.jsxs)("div",{className:"space-y-3 p-4 border rounded-lg bg-muted/50",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{htmlFor:"learning_rate",children:"Learning Rate"}),(0,t.jsx)(N.p,{id:"learning_rate",type:"number",step:"0.0001",value:p.learning_rate,onChange:e=>M("learning_rate",parseFloat(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{htmlFor:"epochs",children:"\xc9pocas"}),(0,t.jsx)(N.p,{id:"epochs",type:"number",value:p.epochs,onChange:e=>M("epochs",parseInt(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{htmlFor:"batch_size",children:"Batch Size"}),(0,t.jsx)(N.p,{id:"batch_size",type:"number",value:p.batch_size,onChange:e=>M("batch_size",parseInt(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{htmlFor:"embedding_dim",children:"Embedding Dim"}),(0,t.jsx)(N.p,{id:"embedding_dim",type:"number",value:p.embedding_dim,onChange:e=>M("embedding_dim",parseInt(e.target.value))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{htmlFor:"regularization",children:"Regularizaci\xf3n"}),(0,t.jsx)(N.p,{id:"regularization",type:"number",step:"0.0001",value:p.regularization,onChange:e=>M("regularization",parseFloat(e.target.value))})]})]}),w&&(0,t.jsxs)(c.Fc,{variant:"destructive",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)(c.TN,{children:w})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,t.jsx)("p",{children:"El entrenamiento puede tomar varios minutos dependiendo del volumen de datos"}),(0,t.jsx)("p",{children:"Se requiere un m\xednimo de 100 interacciones para entrenar un modelo"})]})]}),(0,t.jsxs)(y.Es,{children:[(0,t.jsx)(l.Button,{variant:"outline",onClick:()=>{v||(i(!1),F(null),z(!1),u(!1),x("hybrid"))},disabled:v,children:"Cancelar"}),!k&&(0,t.jsx)(l.Button,{onClick:S,disabled:v,children:v?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Entrenando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Iniciar Entrenamiento"]})})]})]})]})}var k=a(8338),z=a(7271);function M(){let{jobs:e,isLoading:s,error:a,startTraining:E}=(0,_.G)(),[A,C]=(0,n.useState)(null),{filteredJobs:M,searchQuery:S,setSearchQuery:J,statusFilter:T,setStatusFilter:I,clearFilters:L}=(0,w.x)(e,(e,s)=>{let a=s.toLowerCase();return e.model_name.toLowerCase().includes(a)||e.model_version.toLowerCase().includes(a)||e.job_id.toString().includes(s)}),P=e=>"FAILED"===e.status,B=e=>{console.log("Retrying job:",e)};return s?(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,t.jsx)(i.E,{className:"h-8 w-64 mb-2"}),(0,t.jsx)(i.E,{className:"h-4 w-96"})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(i.E,{className:"h-6 w-48"}),(0,t.jsx)(i.E,{className:"h-4 w-32"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)(i.E,{className:"h-64 w-full"})})]})]}):(0,t.jsxs)(z.hI,{title:"Historial de Entrenamientos",description:"Seguimiento completo de todos tus trabajos de entrenamiento de modelos",actions:(0,t.jsx)(F,{onTrainingStart:E,trigger:(0,t.jsxs)(l.Button,{children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Nuevo Entrenamiento"]})}),children:[(0,t.jsx)(z.os,{title:"Resumen",icon:(0,t.jsx)(m.A,{className:"h-6 w-6 text-purple-500"}),children:(0,t.jsxs)("div",{className:"flex gap-4 text-sm text-muted-foreground p-6",children:[(0,t.jsxs)("span",{children:["Total: ",e.length]}),(0,t.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,t.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,t.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})}),a&&(0,t.jsxs)(c.Fc,{variant:"destructive",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)(c.XL,{children:"Error"}),(0,t.jsx)(c.TN,{children:a})]}),(0,t.jsx)(z.os,{title:"Filtros",icon:(0,t.jsx)(x.A,{className:"h-5 w-5"}),children:(0,t.jsxs)(r.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(N.p,{placeholder:"Buscar por modelo, versi\xf3n o ID...",value:S,onChange:e=>J(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsx)(b.J,{htmlFor:"status-filter",className:"text-sm font-medium whitespace-nowrap",children:"Estado:"}),(0,t.jsxs)(f.l6,{value:T,onValueChange:e=>I(e),children:[(0,t.jsx)(f.bq,{id:"status-filter",className:"w-40",children:(0,t.jsx)(f.yv,{})}),(0,t.jsxs)(f.gC,{children:[(0,t.jsx)(f.eb,{value:"all",children:"Todos"}),(0,t.jsx)(f.eb,{value:"pending",children:"Pendientes"}),(0,t.jsx)(f.eb,{value:"processing",children:"Procesando"}),(0,t.jsx)(f.eb,{value:"completed",children:"Completados"}),(0,t.jsx)(f.eb,{value:"failed",children:"Fallidos"})]})]}),(S||"all"!==T)&&(0,t.jsx)(l.Button,{variant:"outline",size:"sm",onClick:L,children:"Limpiar"})]})]}),(S||"all"!==T)&&(0,t.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:["Mostrando ",M.length," de ",e.length," trabajos"]})]})}),(0,t.jsx)(z.os,{title:"Trabajos de Entrenamiento",description:"Lista completa de entrenamientos con detalles y m\xe9tricas",children:(0,t.jsx)(r.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-hidden",children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{className:"bg-muted/10",children:(0,t.jsxs)(d.Hj,{className:"border-b border-border/30",children:[(0,t.jsx)(d.nd,{className:"font-semibold",children:"Job ID"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Modelo / Versi\xf3n"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Estado"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"M\xe9tricas"}),(0,t.jsx)(d.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,t.jsx)(d.BF,{children:M.length>0?M.map((e,s)=>(0,t.jsxs)(z.AP,{index:s,children:[(0,t.jsx)(d.nA,{className:"font-medium py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,k.cR)(e.status),"#",e.job_id]})}),(0,t.jsx)(d.nA,{className:"py-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.model_name}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:e.model_version})]})}),(0,t.jsx)(d.nA,{className:"py-4",children:(0,k.KC)(e.status)}),(0,t.jsx)(d.nA,{className:"py-4 text-muted-foreground",children:(0,g.GP)(new Date(e.created_at),"d 'de' MMM, yyyy HH:mm",{locale:v.es})}),(0,t.jsx)(d.nA,{className:"py-4 text-muted-foreground",children:e.duration?(0,k.a3)(e.duration):"PROCESSING"===e.status?"⏳ En curso":"—"}),(0,t.jsx)(d.nA,{className:"py-4",children:e.metrics?(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Acc: ",(100*e.metrics.accuracy).toFixed(1),"%"]}),(0,t.jsxs)("div",{className:"text-muted-foreground",children:["F1: ",(100*e.metrics.f1_score).toFixed(1),"%"]})]}):(0,t.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,t.jsx)(d.nA,{className:"text-right py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,t.jsxs)(y.lG,{children:[(0,t.jsx)(y.zM,{asChild:!0,children:(0,t.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>C(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,t.jsx)(u.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(y.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(y.c7,{children:[(0,t.jsxs)(y.L3,{children:["Detalles del Job #",e.job_id]}),(0,t.jsx)(y.rr,{children:"Informaci\xf3n completa del trabajo de entrenamiento"})]}),A&&(0,t.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{className:"text-sm font-medium",children:"Modelo"}),(0,t.jsxs)("p",{className:"text-sm",children:[A.model_name," ",A.model_version]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{className:"text-sm font-medium",children:"Estado"}),(0,t.jsx)("div",{className:"mt-1",children:(0,k.KC)(A.status)})]})]}),A.parameters&&(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{className:"text-sm font-medium",children:"Par\xe1metros"}),(0,t.jsx)("pre",{className:"text-xs bg-muted p-2 rounded mt-1 overflow-x-auto",children:JSON.stringify(A.parameters,null,2)})]}),A.metrics&&(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{className:"text-sm font-medium",children:"M\xe9tricas"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-2 mt-1",children:Object.entries(A.metrics).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,t.jsx)("div",{className:"text-xs font-medium",children:s}),(0,t.jsxs)("div",{className:"text-sm",children:[(100*a).toFixed(2),"%"]})]},s)})})]}),A.error_message&&(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,t.jsxs)(c.Fc,{variant:"destructive",className:"mt-1",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)(c.TN,{className:"text-sm",children:A.error_message})]})]}),A.task_id&&(0,t.jsxs)("div",{children:[(0,t.jsx)(b.J,{className:"text-sm font-medium",children:"Task ID"}),(0,t.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:A.task_id})]})]})]})]}),P(e)&&(0,t.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>B(e.job_id),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})]})})]},e.job_id)):(0,t.jsx)(z.AP,{index:0,children:(0,t.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,t.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-8 w-8"}),(0,t.jsx)("p",{children:"No hay trabajos de entrenamiento a\xfan"}),(0,t.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando inicies entrenamientos"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-8 w-8"}),(0,t.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,t.jsx)(l.Button,{variant:"outline",size:"sm",onClick:L,children:"Limpiar filtros"})]})})})})})]})})})}),(0,t.jsxs)(c.Fc,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)(c.XL,{children:"Informaci\xf3n sobre entrenamientos"}),(0,t.jsx)(c.TN,{children:(0,t.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,t.jsx)("p",{children:"Los trabajos de entrenamiento pueden tomar desde minutos hasta horas dependiendo del tama\xf1o de los datos y la complejidad del modelo."}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Pendiente:"})," El trabajo est\xe1 en cola esperando recursos"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Procesando:"})," El entrenamiento est\xe1 en curso"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Completado:"})," El modelo se entren\xf3 exitosamente"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Fallido:"})," Ocurri\xf3 un error durante el entrenamiento"]})]})]})})]})]})}},9376:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[9352,1445,5674,4214,3843,8034,5813,2092,4485,2971,8441,1684,7358],()=>s(996)),_N_E=e.O()}]);