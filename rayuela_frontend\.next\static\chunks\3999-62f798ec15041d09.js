"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3999],{2193:(e,a,t)=>{t.d(a,{A:()=>y});var s=t(5155),r=t(2115),o=t(4165),n=t(285),i=t(2523),l=t(5365),c=t(1243),d=t(5196),u=t(4357),m=t(1684),p=t(1788),g=t(381),h=t(6671),f=t(7262),x=t(5695);let y=e=>{let{apiKey:a,onClose:t}=e,[y,v]=(0,r.useState)(!1),[b,j]=(0,r.useState)(!1),[A,w]=(0,r.useState)(!1),[N,k]=(0,r.useState)(!1),[P,I]=(0,r.useState)(!1),C=(0,x.useRouter)(),E='curl -X GET "'.concat("http://localhost:8001",'/health/auth" \\\n  -H "X-API-Key: ').concat(a,'"');(0,r.useEffect)(()=>{let e=e=>{if(!A)return e.preventDefault(),e.returnValue="",""};return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)},[A]);let K=()=>{navigator.clipboard.writeText(a).then(()=>{v(!0),k(!0),h.o.success("\xa1API Key copiada al portapapeles!"),setTimeout(()=>v(!1),2e3)}).catch(e=>{h.o.error("Error al copiar la API Key."),console.error("Error al copiar al portapapeles:",e)})},S=(e,a)=>{navigator.clipboard.writeText(e).then(()=>{j(!0),h.o.success("\xa1C\xf3digo ".concat(a," copiado al portapapeles!")),setTimeout(()=>j(!1),2e3)}).catch(e=>{h.o.error("Error al copiar el c\xf3digo."),console.error("Error al copiar al portapapeles:",e)})},D=()=>{try{let e=new Blob(["API Key de Rayuela\n","----------------\n","Fecha: ".concat(new Date().toLocaleString(),"\n"),"API Key: ".concat(a,"\n\n"),"IMPORTANTE: Guarda este archivo en un lugar seguro. Esta clave no se mostrar\xe1 completa nuevamente."],{type:"text/plain"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="rayuela-api-key.txt",document.body.appendChild(s),s.click(),setTimeout(()=>{document.body.removeChild(s),URL.revokeObjectURL(t)},0),k(!0),h.o.success("API Key descargada como archivo de texto")}catch(e){h.o.error("Error al descargar la API Key"),console.error("Error al descargar:",e)}},L=()=>{if(!N)return void I(!0);t()};return(0,s.jsx)(o.lG,{open:!0,onOpenChange:e=>!e&&L(),children:(0,s.jsxs)(o.Cf,{className:"sm:max-w-[650px] max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(o.c7,{children:[(0,s.jsxs)(o.L3,{className:"text-xl font-bold flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD11"})," Tu primera API Key est\xe1 lista"]}),(0,s.jsxs)(o.rr,{className:"py-2",children:[(0,s.jsx)("p",{className:"mb-3",children:"\xa1Bienvenido a Rayuela! Tu primera API Key se ha generado autom\xe1ticamente. \xdasala para autenticar todas tus solicitudes a la API."}),(0,s.jsxs)("p",{className:"mb-3 text-sm text-muted-foreground",children:["\uD83D\uDCA1 ",(0,s.jsx)("strong",{children:"Tip:"})," Puedes crear y gestionar m\xfaltiples API Keys para diferentes entornos o equipos desde la secci\xf3n 'API Keys' de tu dashboard."]}),(0,s.jsxs)(l.Fc,{variant:"warning",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)(l.XL,{children:"⚠️ Solo se muestra una vez"}),(0,s.jsx)(l.TN,{children:"Copia y guarda tu API Key ahora. No podr\xe1s verla completa nuevamente."})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-4 mt-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Tu primera API Key:"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.p,{id:"apiKey",readOnly:!0,value:a,className:"flex-1 font-mono text-sm bg-muted border-2"}),(0,s.jsxs)(n.Button,{type:"button",size:"sm",onClick:K,className:"min-w-[90px]",children:[y?(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}):(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),y?"\xa1Copiado!":"Copiar"]})]})]}),(0,s.jsxs)("div",{className:"bg-success-light border border-success/20 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 text-success"}),(0,s.jsx)("h4",{className:"font-semibold text-success-foreground",children:"\uD83D\uDE80 Prueba tu API Key ahora"})]}),(0,s.jsx)("p",{className:"text-sm text-success-foreground mb-3",children:"Ejecuta este comando para verificar que tu API Key funciona:"}),(0,s.jsxs)("div",{className:"bg-card border rounded-md p-3 relative",children:[(0,s.jsx)("pre",{className:"text-info text-xs overflow-x-auto",children:(0,s.jsx)("code",{children:E})}),(0,s.jsx)(n.Button,{type:"button",size:"sm",variant:"ghost",onClick:()=>S(E,"cURL"),className:"absolute top-2 right-2 h-8 w-8 p-0 text-muted-foreground hover:text-foreground",children:b?(0,s.jsx)(d.A,{className:"h-4 w-4"}):(0,s.jsx)(u.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("p",{className:"text-xs text-success-foreground mt-2",children:"Deber\xedas recibir una respuesta con status 200 y un mensaje de bienvenida."})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between gap-4 pt-4",children:[(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)(n.Button,{type:"button",onClick:D,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),"Descargar como archivo"]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(f.S,{id:"confirmSaved",checked:A,onCheckedChange:e=>w(e)}),(0,s.jsx)("label",{htmlFor:"confirmSaved",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"He guardado mi API Key de forma segura"})]})]}),(0,s.jsxs)(l.Fc,{variant:"info",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)(l.XL,{children:"Informaci\xf3n importante"}),(0,s.jsx)(l.TN,{children:(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm mt-2",children:[(0,s.jsxs)("li",{children:["Usa esta API Key en el header ",(0,s.jsx)("code",{className:"text-code-inline",children:"X-API-Key"})," de tus solicitudes"]}),(0,s.jsx)("li",{children:"No compartas tu API Key p\xfablicamente"}),(0,s.jsx)("li",{children:"Puedes crear m\xfaltiples API Keys desde tu panel de control"}),(0,s.jsx)("li",{children:"Si pierdes tu API Key, puedes crear una nueva y revocar la anterior"})]})})]})]}),(0,s.jsxs)(o.Es,{className:"flex flex-col gap-2 sm:flex-row sm:gap-3",children:[(0,s.jsxs)(n.Button,{onClick:()=>{t(),C.push("/api-keys")},variant:"outline",disabled:!N,className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),"Ir a Gesti\xf3n de API Keys"]}),(0,s.jsx)(n.Button,{onClick:t,disabled:!N,className:"flex-1",children:N?"Continuar al Dashboard":"Primero copia o descarga tu API Key"})]}),P&&(0,s.jsxs)(l.Fc,{variant:"warning",className:"mt-4",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)(l.XL,{children:"⚠️ Advertencia"}),(0,s.jsxs)(l.TN,{children:[(0,s.jsx)("p",{className:"text-sm mb-3",children:"A\xfan no has copiado o descargado tu API Key. Esta es la \xfanica vez que podr\xe1s verla completa."}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(n.Button,{onClick:K,size:"sm",variant:"outline",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Copiar API Key"]}),(0,s.jsxs)(n.Button,{onClick:D,size:"sm",variant:"outline",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Descargar"]}),(0,s.jsx)(n.Button,{onClick:()=>I(!1),size:"sm",variant:"ghost",children:"Cancelar"})]})]})]})]})})}},2523:(e,a,t)=>{t.d(a,{p:()=>o});var s=t(5155);t(2115);var r=t(9434);function o(e){let{className:a,type:t,...o}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-lg border bg-transparent px-3 py-1 text-base shadow-soft rayuela-interactive rayuela-focus-ring outline-none hover:border-ring/50 file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...o})}},3999:(e,a,t)=>{t.d(a,{A:()=>u,AuthProvider:()=>d});var s=t(5155),r=t(2115),o=t(5695),n=t(5731),i=t(6671),l=t(2193);let c=(0,r.createContext)(void 0),d=e=>{let{children:a}=e,[t,d]=(0,r.useState)(null),[u,m]=(0,r.useState)(null),[p,g]=(0,r.useState)(null),[h,f]=(0,r.useState)(!0),[x,y]=(0,r.useState)(!1),[v,b]=(0,r.useState)(null),j=(0,o.useRouter)(),A=(0,o.usePathname)(),[w,N]=(0,r.useState)(null),k=(0,r.useCallback)(()=>{localStorage.removeItem("rayuela-token"),localStorage.removeItem("rayuela-apiKey"),d(null),m(null),g(null)},[]),P=(0,r.useCallback)(async(e,a)=>{f(!0);try{let t=await (0,n.jp)();if(!t.is_active)throw Error("User account is inactive.");return d(t),m(e),a&&g(a),console.log("User data fetched successfully:",t),!0}catch(e){return console.error("Token validation/fetch user data failed:",e),k(),(null==A?void 0:A.startsWith("/dashboard"))&&(i.o.error("Tu sesi\xf3n ha expirado o es inv\xe1lida. Por favor, inicia sesi\xf3n de nuevo."),j.push("/login")),!1}finally{f(!1)}},[j,A,k]);(0,r.useEffect)(()=>{console.log("AuthProvider Mounted. Checking localStorage...");let e=localStorage.getItem("rayuela-token"),a=localStorage.getItem("rayuela-apiKey");e&&a?(console.log("Found token and apiKey in localStorage. Validating..."),P(e,a)):(console.log("No token or apiKey found in localStorage."),f(!1))},[P]);let I=(0,r.useCallback)(async()=>{if(!u)return i.o.error("No hay sesi\xf3n activa. Por favor, inicia sesi\xf3n de nuevo."),!1;try{return await (0,n.Hl)(),i.o.success("Email de verificaci\xf3n enviado. Por favor, revisa tu bandeja de entrada."),!0}catch(e){return console.error("Error al solicitar email de verificaci\xf3n:",e),e instanceof n.hD?i.o.error(e.message||"Error al solicitar email de verificaci\xf3n."):i.o.error("Error inesperado al solicitar email de verificaci\xf3n"),!1}},[u]),C=(0,r.useCallback)(async(e,a)=>{f(!0),N(null);try{let t=await (0,n.Lx)({email:e,password:a});if(t.access_token){localStorage.setItem("rayuela-token",t.access_token),m(t.access_token);let e=localStorage.getItem("rayuela-apiKey");if(e&&g(e),await P(t.access_token,e))return i.o.success("Login exitoso!"),j.push("/dashboard"),!0;return!1}throw Error("No se recibi\xf3 token de acceso.")}catch(s){if(console.error("Login processing failed:",s),s&&"object"==typeof s&&"error_code"in s&&"EMAIL_NOT_VERIFIED"===s.error_code)return N({email:e,password:a,message:s.message||"Por favor, verifica tu email para continuar."}),f(!1),!1;let t=s&&"object"==typeof s&&"message"in s?s.message:"Error al iniciar sesi\xf3n. Verifica tus credenciales.";return i.o.error(t),k(),f(!1),!1}},[P,j,k]),E=(0,r.useCallback)(async(e,a,t)=>{f(!0);try{let s=await (0,n.DY)(e,a,t);if(s.access_token){let e=s.api_key,a=s.access_token;return localStorage.setItem("rayuela-token",a),localStorage.setItem("rayuela-apiKey",e),m(a),g(e),b(e),y(!0),await P(a,e),{success:!0,apiKey:e}}throw Error("No se recibi\xf3 token de acceso")}catch(e){return console.error("Register processing failed:",e),e instanceof n.hD?i.o.error(e.message||"Error al registrarse"):i.o.error("Error inesperado al registrarse"),k(),{success:!1,error:e}}finally{f(!1)}},[P,k]),K=(0,r.useCallback)(async()=>{y(!1),b(null);let e=i.o.loading("Configurando tu cuenta...");f(!0);try{if(!u||!p)throw new n.hD("Token o API Key no disponibles",401,"AUTH_REQUIRED");let a=!1,t=0,s=null;for(;!a&&t<3;)try{await P(u,p),a=!0}catch(e){s=e,++t<3&&await new Promise(e=>setTimeout(e,500))}if(!a&&s)throw s;i.o.dismiss(e),i.o.success("\xa1Cuenta configurada correctamente!"),j.push("/dashboard")}catch(a){i.o.dismiss(e),a instanceof n.hD?i.o.error(a.message||"Error al inicializar la cuenta"):i.o.error("Error inesperado al configurar la cuenta")}finally{f(!1)}},[u,p,P,j]),S=(0,r.useCallback)(async()=>{try{u&&await (0,n.ri)(),k(),j.push("/login"),i.o.success("Sesi\xf3n cerrada correctamente")}catch(e){console.error("Logout error:",e),k(),j.push("/login")}},[u,k,j]);return(0,s.jsxs)(c.Provider,{value:{user:t,token:u,apiKey:p,setApiKey:g,login:C,register:E,logout:S,isLoading:h,emailVerificationError:w,requestNewVerificationEmail:I},children:[a,x&&v&&(0,s.jsx)(l.A,{apiKey:v,onClose:K})]})},u=()=>{let e=(0,r.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4165:(e,a,t)=>{t.d(a,{Cf:()=>u,Es:()=>p,L3:()=>g,c7:()=>m,lG:()=>i,rr:()=>h,zM:()=>l});var s=t(5155);t(2115);var r=t(9458),o=t(4416),n=t(9434);function i(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...a})}function l(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function u(e){let{className:a,children:t,...i}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-medium duration-200 sm:max-w-lg",a),...i,children:[t,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(o.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function p(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function g(e){let{className:a,...t}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",a),...t})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",a),...t})}},5365:(e,a,t)=>{t.d(a,{Fc:()=>l,TN:()=>d,XL:()=>c});var s=t(5155),r=t(2115),o=t(2085),n=t(9434);let i=(0,o.F)("relative w-full rounded-lg border p-4 transition-all hover:shadow-sm [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground border-border",destructive:"border-destructive/50 text-destructive bg-destructive/5 hover:bg-destructive/10 [&>svg]:text-destructive",success:"border-success/50 text-success bg-success-light hover:bg-success/10 [&>svg]:text-success",warning:"border-warning/50 text-warning bg-warning-light hover:bg-warning/10 [&>svg]:text-warning",info:"border-info/50 text-info bg-info-light hover:bg-info/10 [&>svg]:text-info"}},defaultVariants:{variant:"default"}}),l=r.forwardRef((e,a)=>{let{className:t,variant:r,...o}=e;return(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(i({variant:r}),t),...o})});l.displayName="Alert";let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})});c.displayName="AlertTitle";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...r})});d.displayName="AlertDescription"},5731:(e,a,t)=>{t.d(a,{A$:()=>u,DY:()=>l,Dm:()=>p,Hl:()=>d,Iq:()=>v,Lx:()=>i,M2:()=>f,PX:()=>y,S3:()=>x,T9:()=>h,XW:()=>b,_:()=>g,fw:()=>A,hD:()=>r,jp:()=>m,mA:()=>j,oE:()=>w,ri:()=>c});var s=t(2656);class r extends Error{constructor(e,a=500,t){super(e),this.name="ApiError",this.status=a,this.body=t}}function o(e,a){if(e&&"object"==typeof e&&"response"in e){var t,s;let o=(null==(t=e.response)?void 0:t.status)||500;throw new r(e.message||a,o,null==(s=e.response)?void 0:s.data)}if(e instanceof Error)throw new r(e.message,500);throw new r(a,500)}let n=(0,s._C)(),i=async e=>{try{return await n.loginApiV1AuthTokenPost(e)}catch(e){o(e,"Login failed")}},l=async(e,a,t)=>{try{return await n.registerApiV1AuthRegisterPost({accountName:e,email:a,password:t})}catch(e){o(e,"Registration failed")}},c=async()=>n.logoutApiV1AuthLogoutPost(),d=async()=>n.sendVerificationEmailApiV1AuthSendVerificationEmailPost(),u=async e=>n.verifyEmailApiV1AuthVerifyEmailGet({token:e}),m=async()=>{try{return await n.getCurrentUserInfoApiV1SystemUsersMeGet()}catch(e){o(e,"Failed to get current user")}},p=async()=>{try{return await n.getAccountInfoApiV1AccountsCurrentGet()}catch(e){o(e,"Failed to get current account")}},g=p,h=async()=>{try{return await n.getAvailablePlansApiV1PlansGet()}catch(e){o(e,"Failed to get plans")}},f=async(e,a)=>{let t={};return e&&(t.start_date=e),a&&(t.end_date=a),n.getUsageHistoryApiV1UsageHistoryGet(t)},x=async()=>{try{return await n.getUsageSummaryApiV1UsageSummaryGet()}catch(e){o(e,"Failed to get usage summary")}},y=async()=>{try{return await n.listApiKeysApiV1ApiKeysGet()}catch(e){o(e,"Failed to get API keys")}},v=async e=>{try{let a={name:e.name};return await n.createApiKeyApiV1ApiKeysPost(a)}catch(e){o(e,"Failed to create API key")}},b=async(e,a)=>{try{let t={name:a.name};return await n.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(e),t)}catch(e){o(e,"Failed to update API key")}},j=async e=>n.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(e),A=async e=>n.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost({price_id:e}),w=async()=>n.createPortalSessionApiV1BillingCreatePortalSessionPost({})},7262:(e,a,t)=>{t.d(a,{S:()=>l});var s=t(5155),r=t(2115),o=t(4927),n=t(5196),i=t(9434);let l=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(o.bL,{ref:a,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...r,children:(0,s.jsx)(o.C1,{className:(0,i.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})})});l.displayName=o.bL.displayName}}]);