(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7080],{467:(e,r,a)=>{"use strict";a.d(r,{K:()=>l,S:()=>i});var s=a(2656),t=a(5731);let n=(0,s._C)();async function i(e,r){try{let a={};return e&&(a.model_id=e),r&&(a.metric_type=r),await n.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(a)}catch(s){let e=s instanceof Error?s.message:"Error al obtener m\xe9tricas de rendimiento de recomendaciones",r=s.status||500,a=s.body;throw new t.hD(e,r,a)}}async function l(){try{return await n.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet()}catch(s){let e=s instanceof Error?s.message:"Error al obtener m\xe9tricas de confianza",r=s.status||500,a=s.body;throw new t.hD(e,r,a)}}},1169:(e,r,a)=>{"use strict";a.d(r,{A:()=>d});var s=a(5155),t=a(2115),n=a(5695),i=a(3999),l=a(8856);let d=function(e){let{children:r}=e,{user:a,isLoading:d}=(0,i.A)(),c=(0,n.useRouter)();return((0,t.useEffect)(()=>{d||a&&a.is_admin||c.push("/dashboard")},[a,d,c]),d)?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.E,{className:"h-8 w-[200px]"}),(0,s.jsx)(l.E,{className:"h-[400px] w-full"})]}):a&&a.is_admin?(0,s.jsx)(s.Fragment,{children:r}):null}},1363:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>W});var s=a(5155),t=a(2115),n=a(6072),i=a(3999),l=a(6695),d=a(285),c=a(9409),o=a(7313),m=a(6671),u=a(7588),x=a(6102),f=a(4788);function g(e){let{content:r,side:a="top",align:t="center",className:n="",iconSize:i=16,iconClassName:l=""}=e;return(0,s.jsx)(x.Bc,{children:(0,s.jsxs)(x.m_,{delayDuration:300,children:[(0,s.jsx)(x.k$,{asChild:!0,children:(0,s.jsx)("span",{className:"inline-flex cursor-help ".concat(l),children:(0,s.jsx)(f.A,{size:i,className:"text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"})})}),(0,s.jsx)(x.ZI,{side:a,align:t,className:"max-w-xs p-3 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-md ".concat(n),children:r})]})})}var h=a(3904),p=a(2713),v=a(232),b=a(6126),j=a(9287),N=a(6474),y=a(9434);let w=j.bL,E=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(j.q7,{ref:r,className:(0,y.cn)("border-b border-border/50 last:border-b-0",a),...t})});E.displayName="AccordionItem";let A=t.forwardRef((e,r)=>{let{className:a,children:t,...n}=e;return(0,s.jsx)(j.Y9,{className:"flex",children:(0,s.jsxs)(j.l9,{ref:r,className:(0,y.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:bg-muted/30 hover:text-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg -mx-2 px-2 [&[data-state=open]>svg]:rotate-180",a),...n,children:[t,(0,s.jsx)(N.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200 text-muted-foreground"})]})})});A.displayName="AccordionTrigger";let C=t.forwardRef((e,r)=>{let{className:a,children:t,...n}=e;return(0,s.jsx)(j.UC,{ref:r,className:"overflow-hidden text-sm text-muted-foreground transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...n,children:(0,s.jsx)("div",{className:(0,y.cn)("pb-4 pt-0 px-2",a),children:t})})});C.displayName="AccordionContent";var R=a(235),I=a(825),T=a(1539),k=a(5339),_=a(4213),z=a(7580),O=a(3109),D=a(463),P=a(646),S=a(3052),Z=a(8534),M=a(5524),L=a(1950);function B(e){let{performanceData:r,confidenceData:a,isLoading:n=!1}=e,[i,d]=(0,t.useState)([]),[c,o]=(0,t.useState)(void 0);(0,t.useEffect)(()=>{if(!r||!a)return;let e={BarChart2Icon:R.A,ShuffleIcon:I.A,ZapIcon:T.A,AlertCircleIcon:k.A,DatabaseIcon:_.A,UsersIcon:z.A,TrendingUpIcon:O.A,LightbulbIcon:D.A};d((0,M._X)(r,a,e))},[r,a]);let m=i.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{}),u={accuracy:{name:L.Nn.CATEGORY_ACCURACY,icon:(0,s.jsx)(Z.mm,{icon:R.A,size:"md",context:"metric"})},diversity:{name:L.Nn.CATEGORY_DIVERSITY,icon:(0,s.jsx)(Z.mm,{icon:I.A,size:"md",context:"metric"})},confidence:{name:L.Nn.CATEGORY_CONFIDENCE,icon:(0,s.jsx)(Z.mm,{icon:P.A,size:"md",context:"metric"})},performance:{name:L.Nn.CATEGORY_PERFORMANCE,icon:(0,s.jsx)(Z.mm,{icon:T.A,size:"md",context:"metric"})}};return n?(0,s.jsxs)(l.Zp,{className:"w-full",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(Z.mm,{icon:D.A,size:"md",context:"warning"}),L.Nn.COMPONENT_TITLE]}),(0,s.jsx)(l.BT,{children:L.Nn.LOADING_TEXT})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-full mb-1"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"})]},e))})})]}):r&&a&&0!==i.length?(0,s.jsxs)(l.Zp,{className:"w-full",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(Z.mm,{icon:D.A,size:"md",context:"warning"}),L.Nn.COMPONENT_TITLE]}),(0,s.jsx)(l.BT,{children:(0,L.JT)(L.Nn.COMPONENT_DESCRIPTION,{count:Object.values(m).flat().length})})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)(w,{type:"single",collapsible:!0,value:c,onValueChange:o,className:"space-y-4",children:Object.entries(m).map(e=>{var r,a;let[t,n]=e;return(0,s.jsxs)(E,{value:t,className:"border rounded-lg overflow-hidden",children:[(0,s.jsx)(A,{className:"px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-left",children:[null==(r=u[t])?void 0:r.icon,(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:(null==(a=u[t])?void 0:a.name)||t}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,L.JT)(L.Nn.RECOMMENDATION_COUNT,{count:n.length})})]})]})}),(0,s.jsx)(C,{className:"px-0",children:(0,s.jsx)("div",{className:"space-y-4 pt-2",children:n.map(e=>(0,s.jsx)("div",{className:"px-4 py-3 border-t border-gray-100 dark:border-gray-800",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"mt-1",children:e.icon}),(0,s.jsxs)("div",{className:"space-y-2 w-full",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h4",{className:"font-medium",children:e.title}),(0,s.jsx)(b.E,{variant:"high"===e.priority?"destructive":"medium"===e.priority?"default":"secondary",className:"capitalize",children:e.priority})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 my-3",children:e.metrics.map((e,r)=>(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 p-2 rounded-md",children:[(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.name}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"font-medium",children:[e.value.toFixed(1),e.unit]}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:(0,L.JT)(L.Nn.METRIC_TARGET,{value:e.target,unit:e.unit})})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1",children:(0,s.jsx)("div",{className:"h-1.5 rounded-full ".concat(e.value>=e.target?"bg-green-500":e.value>=.8*e.target?"bg-amber-500":"bg-red-500"),style:{width:"".concat(Math.min(100,e.value/e.target*100),"%")}})})]},r))}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"text-sm font-medium mb-2",children:L.Nn.ACTIONS_TITLE}),(0,s.jsx)("ul",{className:"space-y-1",children:e.actions.map((e,r)=>(0,s.jsxs)("li",{className:"text-sm flex items-start gap-2",children:[(0,s.jsx)(Z.mm,{icon:S.A,size:"sm",context:"muted",className:"mt-0.5 flex-shrink-0"}),(0,s.jsx)("span",{children:e})]},r))})]})]})]})},e.id))})})]},t)})})})]}):(0,s.jsxs)(l.Zp,{className:"w-full",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(Z.mm,{icon:P.A,size:"md",context:"success"}),L.Nn.OPTIMIZED_TITLE]}),(0,s.jsx)(l.BT,{children:L.Nn.OPTIMIZED_DESCRIPTION})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("p",{className:"text-muted-foreground",children:L.Nn.OPTIMIZED_MESSAGE})})]})}var U=a(467),F=a(1169),V=a(7271);function W(){let{token:e,apiKey:r}=(0,i.A)(),[a,x]=(0,t.useState)(!1),[f,b]=(0,t.useState)("performance"),[j,N]=(0,t.useState)("all"),[y,w]=(0,t.useState)("all"),{data:E,error:A,isLoading:C,mutate:R}=(0,n.Ay)(e&&r?["recommendation-performance",j,y]:null,async e=>{let[,r,a]=e;return await (0,U.S)("all"!==r?parseInt(r):void 0,"all"!==a?a:void 0)}),{data:I,error:T,isLoading:k,mutate:_}=(0,n.Ay)(e&&r?"confidence-metrics":null,async()=>await (0,U.K)()),z=async()=>{x(!0);try{await Promise.all([R(),_()]),m.o.success("M\xe9tricas actualizadas")}catch(e){(0,u.h)(e,"Error al actualizar las m\xe9tricas")}finally{x(!1)}},O=[];if(E&&"object"==typeof E){let e=E.offline_metrics;(null==e?void 0:e.models)&&Array.isArray(e.models)&&e.models.forEach(e=>{var r,a;O.push({id:(null==(r=e.model_id)?void 0:r.toString())||(null==(a=e.id)?void 0:a.toString())||Math.random().toString(),name:"".concat(e.model_type||e.type||"Model"," (").concat(e.version||"v1",")")})})}return(0,s.jsx)(F.A,{children:(0,s.jsxs)(V.hI,{title:"M\xe9tricas de Recomendaci\xf3n",description:"An\xe1lisis detallado del rendimiento y confianza de los modelos de recomendaci\xf3n",actions:(0,s.jsxs)(d.Button,{variant:"outline",onClick:z,disabled:a||C||k,className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 ".concat(a?"animate-spin":"")}),"Actualizar"]}),children:[(0,s.jsx)(V.os,{title:"Acerca de las M\xe9tricas",icon:(0,s.jsx)(p.A,{className:"h-5 w-5"}),children:(0,s.jsx)(l.Wu,{className:"p-6 space-y-3 text-sm",children:(0,s.jsx)("p",{children:"Esta p\xe1gina muestra m\xe9tricas detalladas sobre el rendimiento y la confianza de los modelos de recomendaci\xf3n. Pase el cursor sobre cualquier m\xe9trica para obtener m\xe1s informaci\xf3n."})})}),E&&I&&(0,s.jsx)(B,{performanceData:E,confidenceData:I,isLoading:C||k}),(0,s.jsx)(o.tU,{value:f,onValueChange:b,className:"w-full",children:(0,s.jsxs)(o.j7,{className:"grid grid-cols-2 w-full max-w-md",children:[(0,s.jsxs)(o.Xi,{value:"performance",className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),"Rendimiento"]}),(0,s.jsxs)(o.Xi,{value:"confidence",className:"flex items-center gap-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),"Confianza"]})]})}),"performance"===f&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsxs)("div",{className:"w-full md:w-auto",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Modelo"}),(0,s.jsxs)(c.l6,{value:j,onValueChange:N,children:[(0,s.jsx)(c.bq,{className:"w-full md:w-[200px]",children:(0,s.jsx)(c.yv,{placeholder:"Todos los modelos"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"all",children:"Todos los modelos"}),O.map(e=>(0,s.jsx)(c.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,s.jsxs)("div",{className:"w-full md:w-auto",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Tipo de M\xe9trica"}),(0,s.jsxs)(c.l6,{value:y,onValueChange:w,children:[(0,s.jsx)(c.bq,{className:"w-full md:w-[200px]",children:(0,s.jsx)(c.yv,{placeholder:"Todas las m\xe9tricas"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"all",children:"Todas las m\xe9tricas"}),[{id:"precision",name:"Precisi\xf3n"},{id:"recall",name:"Recall"},{id:"ndcg",name:"NDCG"},{id:"map",name:"MAP"},{id:"catalog_coverage",name:"Cobertura del Cat\xe1logo"},{id:"diversity",name:"Diversidad"},{id:"novelty",name:"Novedad"},{id:"serendipity",name:"Serendipia"}].map(e=>(0,s.jsx)(c.eb,{value:e.id,children:e.name},e.id))]})]})]})]}),E&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{className:"pb-2",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Precisi\xf3n"}),(0,s.jsx)(g,{content:(0,s.jsx)("div",{children:"Porcentaje de recomendaciones relevantes del total de recomendaciones mostradas"}),iconSize:14})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof E&&E){let e=E.offline_metrics,r=null==e?void 0:e.precision;return r?"".concat((100*r).toFixed(1),"%"):"--"}return"--"})()})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{className:"pb-2",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"NDCG"}),(0,s.jsx)(g,{content:(0,s.jsx)("div",{children:"Normalized Discounted Cumulative Gain - eval\xfaa la calidad del ranking"}),iconSize:14})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof E&&E){let e=E.offline_metrics,r=null==e?void 0:e.ndcg;return r?"".concat((100*r).toFixed(1),"%"):"--"}return"--"})()})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{className:"pb-2",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Diversidad"}),(0,s.jsx)(g,{content:(0,s.jsx)("div",{children:"Variedad en las recomendaciones mostradas"}),iconSize:14})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof E&&E){let e=E.offline_metrics,r=null==e?void 0:e.diversity;return r?"".concat((100*r).toFixed(1),"%"):"--"}return"--"})()})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{className:"pb-2",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Cobertura"}),(0,s.jsx)(g,{content:(0,s.jsx)("div",{children:"Porcentaje del cat\xe1logo que se est\xe1 recomendando"}),iconSize:14})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof E&&E){let e=E.offline_metrics,r=null==e?void 0:e.catalog_coverage;return r?"".concat((100*r).toFixed(1),"%"):"--"}return"--"})()})})]})]}),C&&(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),A&&(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-red-500",children:"Error al cargar m\xe9tricas de rendimiento"})})]}),"confidence"===f&&(0,s.jsxs)("div",{className:"space-y-6",children:[I&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Distribuci\xf3n de Confianza"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:"object"==typeof I&&I?Object.entries(I.confidence_distribution||{}).map(e=>{let[r,a]=e;return(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"capitalize",children:r}),(0,s.jsx)("span",{className:"font-medium",children:a.avg?"".concat((100*a.avg).toFixed(1),"%"):"N/A"})]},r)}):(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Datos de confianza no disponibles"})})})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Confianza por Categor\xeda"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:"object"==typeof I&&I?Object.entries(I.category_confidence||{}).slice(0,5).map(e=>{let[r,a]=e;return(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"capitalize",children:r}),(0,s.jsx)("span",{className:"font-medium",children:"number"==typeof a?"".concat((100*a).toFixed(1),"%"):"N/A"})]},r)}):(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Datos por categor\xeda no disponibles"})})})})]})]}),k&&(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),T&&(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-red-500",children:"Error al cargar m\xe9tricas de confianza"})})]})]})})}},3270:(e,r,a)=>{Promise.resolve().then(a.bind(a,1363))},6126:(e,r,a)=>{"use strict";a.d(r,{E:()=>l});var s=a(5155);a(2115);var t=a(2085),n=a(9434);let i=(0,t.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:a,...t}=e;return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:a}),r),...t})}},6695:(e,r,a)=>{"use strict";a.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>o});var s=a(5155);a(2115);var t=a(9434);function n(e){var r;let{className:a,elevation:n="soft",...i}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border",null!=(r=({none:"shadow-none",sm:"shadow-sm",soft:"shadow-soft",medium:"shadow-medium",glow:"shadow-glow"})[n])?r:"shadow-soft","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",a),...i})}function i(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...a})}function l(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("text-subheading rayuela-accent",r),...a})}function d(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-caption",r),...a})}function c(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",r),...a})}function o(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,t.cn)("flex items-center px-6 [.border-t]:pt-6",r),...a})}},7271:(e,r,a)=>{"use strict";a.d(r,{AP:()=>o,hI:()=>d,os:()=>c});var s=a(5155);a(2115);var t=a(6695),n=a(9434);function i(e){let{children:r,variant:a="default",className:t,...i}=e;return(0,s.jsx)("div",{className:(0,n.cn)({default:"",subtle:"bg-card/30 dark:bg-card/20 border border-border/50 rounded-lg p-6 rayuela-subtle-gradient",elevated:"bg-card border border-border shadow-sm rounded-lg p-6 rayuela-card-gradient"}[a],t),...i,children:r})}function l(e){let{variant:r="line",spacing:a="md",className:t,...i}=e;return(0,s.jsx)("div",{className:(0,n.cn)({sm:"my-4",md:"my-6",lg:"my-8"}[a],{line:"border-t border-border/50",space:"h-px",gradient:"h-px bg-gradient-to-r from-transparent via-border/30 to-transparent"}[r],t),...i})}function d(e){let{children:r,title:a,description:t,actions:n}=e;return(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(i,{variant:"subtle",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:a}),t&&(0,s.jsx)("p",{className:"text-muted-foreground text-lg",children:t})]}),n&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:n})]})}),(0,s.jsx)(l,{variant:"gradient",spacing:"lg"}),(0,s.jsx)("div",{className:"space-y-6",children:r})]})}function c(e){let{title:r,description:a,icon:i,children:l,headerActions:d,className:c,...o}=e;return(0,s.jsxs)(t.Zp,{className:(0,n.cn)("shadow-sm border-border/50 overflow-hidden",c),...o,children:[(0,s.jsx)(t.aR,{className:"border-b border-border/20 bg-muted/10",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(t.ZB,{className:"flex items-center gap-2 text-xl",children:[i&&(0,s.jsx)("span",{className:"text-primary",children:i}),r]}),a&&(0,s.jsx)(t.BT,{className:"text-base",children:a})]}),d&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:d})]})}),(0,s.jsx)(t.Wu,{className:"p-0",children:l})]})}function o(e){let{children:r,index:a,className:t,...i}=e;return(0,s.jsx)("tr",{className:(0,n.cn)("border-b border-border/20 hover:bg-muted/30 transition-colors",a%2==0?"bg-background":"bg-muted/5",t),...i,children:r})}},7313:(e,r,a)=>{"use strict";a.d(r,{Xi:()=>c,av:()=>o,j7:()=>d,tU:()=>l});var s=a(5155),t=a(2115),n=a(1414),i=a(9434);let l=n.bL,d=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.B8,{ref:r,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",a),...t})});d.displayName=n.B8.displayName;let c=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.l9,{ref:r,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...t})});c.displayName=n.l9.displayName;let o=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.UC,{ref:r,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...t})});o.displayName=n.UC.displayName},7588:(e,r,a)=>{"use strict";a.d(r,{h:()=>o});var s=a(6671),t=a(3464);class n extends Error{static isApiError(e){return e instanceof n}static fromResponse(e){return new n(e.message,e.status_code,e.error_code,e.details)}constructor(e,r,a,s){super(e),this.status=r,this.errorCode=a,this.details=s,this.name="ApiError"}}let i=t.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{{var r,a,s;let t=localStorage.getItem("rayuela-token"),n=localStorage.getItem("rayuela-apiKey");e.headers=null!=(s=e.headers)?s:{},t&&(e.headers.Authorization="Bearer ".concat(t)),!n||(null==(r=e.url)?void 0:r.includes("/auth/token"))||(null==(a=e.url)?void 0:a.includes("/auth/register"))||(e.headers["X-API-Key"]=n)}return e}),i.interceptors.response.use(e=>e,e=>{if(e.response){let r=e.response.data;throw n.fromResponse(r)}if(e.request)throw new n("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new n(e.message,0,"REQUEST_ERROR",null)});var l=a(6874),d=a.n(l),c=a(2115);function o(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Ha ocurrido un error";return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof n)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void s.o.error(c.createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",c.createElement(d(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void s.o.error(c.createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",c.createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void s.o.error(c.createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",c.createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void s.o.error(c.createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",c.createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void s.o.error(c.createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",c.createElement(d(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void s.o.error(c.createElement("div",{},"Error de validacion: "+e.message+". ",c.createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void s.o.error(c.createElement("div",{},"Datos insuficientes para generar recomendaciones. ",c.createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void s.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(s.o.error(e.message||r),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void s.o.error(e.message||r):void(s.o.error(r),console.groupEnd())}},8534:(e,r,a)=>{"use strict";a.d(r,{mm:()=>l,vK:()=>c});var s=a(5155);a(2115);var t=a(9434);let n={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",xl:"h-8 w-8","2xl":"h-12 w-12"},i={success:"text-success",warning:"text-warning",error:"text-destructive",info:"text-info",primary:"text-primary",secondary:"text-secondary-foreground",muted:"text-muted-foreground",interactive:"text-primary hover:text-primary/80",neutral:"text-foreground",subtle:"text-muted-foreground",metric:"text-primary",action:"text-primary",navigation:"text-muted-foreground hover:text-foreground"};function l(e){let{icon:r,size:a="md",context:l="neutral",className:d,"aria-label":c,"aria-hidden":o=!c,...m}=e;return(0,s.jsx)(r,{className:(0,t.cn)(n[a],i[l],"shrink-0",d),"aria-label":c,"aria-hidden":o,...m})}let d={tight:"gap-1",normal:"gap-2",loose:"gap-3"};function c(e){let{icon:r,children:a,size:n="sm",context:i="neutral",iconPosition:c="left",spacing:o="normal",className:m}=e,u=(0,s.jsx)(l,{icon:r,size:n,context:i,"aria-hidden":!0});return(0,s.jsxs)("span",{className:(0,t.cn)("inline-flex items-center",d[o],m),children:["left"===c&&u,a,"right"===c&&u]})}},8856:(e,r,a)=>{"use strict";a.d(r,{E:()=>n});var s=a(5155),t=a(9434);function n(e){let{className:r,...a}=e;return(0,s.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-lg bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-shimmer",r),...a})}},9409:(e,r,a)=>{"use strict";a.d(r,{bq:()=>u,eb:()=>h,gC:()=>g,l6:()=>o,yv:()=>m});var s=a(5155),t=a(2115),n=a(4140),i=a(6474),l=a(7863),d=a(5196),c=a(9434);let o=n.bL;n.YJ;let m=n.WT,u=t.forwardRef((e,r)=>{let{className:a,children:t,...l}=e;return(0,s.jsxs)(n.l9,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-lg border border-input bg-background px-3 py-2 text-sm shadow-soft rayuela-interactive rayuela-focus-ring hover:border-ring/50 ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...l,children:[t,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"h-4 w-4 opacity-50 transition-transform group-data-[state=open]:rotate-180"})})]})});u.displayName=n.l9.displayName;let x=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.PP,{ref:r,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})});x.displayName=n.PP.displayName;let f=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.wn,{ref:r,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})});f.displayName=n.wn.displayName;let g=t.forwardRef((e,r)=>{let{className:a,children:t,position:i="popper",...l}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{ref:r,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-lg border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...l,children:[(0,s.jsx)(x,{}),(0,s.jsx)(n.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(f,{})]})})});g.displayName=n.UC.displayName,t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.JU,{ref:r,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...t})}).displayName=n.JU.displayName;let h=t.forwardRef((e,r)=>{let{className:a,children:t,...i}=e;return(0,s.jsxs)(n.q7,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-md py-1.5 pl-8 pr-2 text-sm outline-none transition-colors hover:bg-accent/50 focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(n.p4,{children:t})]})});h.displayName=n.q7.displayName,t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.wv,{ref:r,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...t})}).displayName=n.wv.displayName}},e=>{var r=r=>e(e.s=r);e.O(0,[9352,6874,1445,5674,3753,3843,8034,9566,5813,9521,448,833,2092,3999,9843,8441,1684,7358],()=>r(3270)),_N_E=e.O()}]);