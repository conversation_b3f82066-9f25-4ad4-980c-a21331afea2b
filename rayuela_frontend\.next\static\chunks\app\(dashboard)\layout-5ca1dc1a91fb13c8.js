(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9305],{2881:(e,a,r)=>{"use strict";r.d(a,{BJ:()=>l,YJ:()=>c,hf:()=>i});var s=r(5155);r(2115);var n=r(9434);let t={iconText:{sm:"gap-1.5",md:"gap-2",lg:"gap-3"},elementGroup:{tight:"gap-1",normal:"gap-2",loose:"gap-3"},stack:{xs:"space-y-1",sm:"space-y-1.5",md:"space-y-2",lg:"space-y-3",xl:"space-y-4"}};function i(e){let{icon:a,children:r,size:i="md",align:l="center",className:c,...o}=e;return(0,s.jsxs)("div",{className:(0,n.cn)("flex",t.iconText[i],{start:"items-start",center:"items-center",end:"items-end"}[l],c),...o,children:[(0,s.jsx)("span",{className:"shrink-0 flex items-center",children:a}),(0,s.jsx)("span",{className:"min-w-0",children:r})]})}function l(e){let{spacing:a="md",className:r,children:i,...l}=e;return(0,s.jsx)("div",{className:(0,n.cn)(t.stack[a],r),...l,children:i})}function c(e){let{spacing:a="normal",align:r="center",wrap:i=!1,className:l,children:c,...o}=e;return(0,s.jsx)("div",{className:(0,n.cn)("flex",t.elementGroup[a],{start:"items-start",center:"items-center",end:"items-end",stretch:"items-stretch"}[r],i&&"flex-wrap",l),...o,children:c})}},3862:(e,a,r)=>{Promise.resolve().then(r.bind(r,8301))},8301:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>S});var s=r(5155),n=r(2115),t=r(5695),i=r(3999),l=r(285),c=r(6874),o=r.n(c),d=r(9434);function m(e){let{variant:a="default",className:r,linkClassName:n,href:t="/"}=e,i=(0,s.jsx)("div",{className:(0,d.cn)("font-bold transition-all duration-300 ease-in-out rayuela-accent","marketing"===a?"text-display tracking-extra-tight":"text-heading tracking-tight","hover:scale-105",r),children:"Rayuela.ai"});return t?(0,s.jsx)(o(),{href:t,className:(0,d.cn)("rayuela-interactive hover:opacity-90 rayuela-focus-ring rounded-lg p-1 -m-1",n),children:i}):i}var x=r(2881);function u(){let{logout:e,user:a}=(0,i.A)();return(0,s.jsx)("header",{className:"bg-card shadow-sm border-b border-border h-16 shrink-0",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-full px-4",children:[(0,s.jsxs)(x.YJ,{children:[(0,s.jsx)(m,{className:"text-foreground hidden md:block"}),(0,s.jsx)("span",{className:"text-heading text-foreground",children:"Dashboard"})]}),(0,s.jsx)(x.YJ,{spacing:"normal",children:a&&(0,s.jsx)(l.Button,{onClick:e,variant:"outline",size:"sm",children:"Logout"})})]})})}var h=r(7340),f=r(9803),g=r(3314),v=r(9397),b=r(3109),p=r(1586),j=r(9376),y=r(381),N=r(5040),w=r(3786),k=r(1497),A=r(5456),z=r(8534);let E=[{href:"/dashboard",label:"Dashboard",icon:(0,s.jsx)(z.mm,{icon:h.A,size:"sm",context:"navigation",className:"rayuela-icon-accent"})},{href:"/api-keys",label:"API Keys",icon:(0,s.jsx)(z.mm,{icon:f.A,size:"sm",context:"navigation",className:"rayuela-icon-accent"})},{href:"/pipeline",label:"Pipeline",icon:(0,s.jsx)(z.mm,{icon:g.A,size:"sm",context:"navigation",className:"rayuela-icon-primary"})},{href:"/usage",label:"Usage",icon:(0,s.jsx)(z.mm,{icon:v.A,size:"sm",context:"navigation",className:"rayuela-icon-progress"})},{href:"/recommendation-metrics",label:"Metrics",icon:(0,s.jsx)(z.mm,{icon:b.A,size:"sm",context:"navigation",className:"rayuela-icon-progress"})},{href:"/billing",label:"Billing",icon:(0,s.jsx)(z.mm,{icon:p.A,size:"sm",context:"navigation",className:"rayuela-icon-accent"})},{href:"/models",label:"Models",icon:(0,s.jsx)(z.mm,{icon:j.A,size:"sm",context:"navigation",className:"rayuela-icon-exploration"})},{href:"/settings",label:"Settings",icon:(0,s.jsx)(z.mm,{icon:y.A,size:"sm",context:"navigation",className:"rayuela-icon-accent"})},{href:"https://docs.rayuela.ai",label:"Docs",external:!0,icon:(0,s.jsx)(z.mm,{icon:N.A,size:"sm",context:"navigation",className:"rayuela-icon-exploration"})}];function C(){return(0,s.jsxs)("aside",{className:"w-64 bg-sidebar text-sidebar-foreground border-r border-sidebar-border p-4 flex flex-col h-full shrink-0 rayuela-subtle-gradient",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(m,{href:"/dashboard",className:"text-sidebar-foreground"})}),(0,s.jsx)("nav",{children:(0,s.jsx)(x.BJ,{spacing:"sm",className:"rayuela-stagger-2",children:E.map(e=>(0,s.jsx)(l.Button,{variant:"ghost",className:"justify-start text-left text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground px-3 rayuela-interactive group",asChild:!0,children:e.external?(0,s.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"w-full",children:(0,s.jsx)(x.hf,{icon:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,s.jsx)(z.mm,{icon:w.A,size:"xs",context:"navigation",className:"opacity-60"})]}),className:"w-full justify-between",children:e.label})}):(0,s.jsx)(o(),{href:e.href,className:"w-full",children:(0,s.jsx)(x.hf,{icon:e.icon,children:e.label})})},e.href))})}),(0,s.jsx)("div",{className:"flex-grow"}),(0,s.jsxs)("div",{className:"mt-6 pt-4 border-t border-sidebar-border/50 rayuela-exploration-glow",children:[(0,s.jsx)(l.Button,{variant:"ghost",size:"sm",className:"w-full justify-start text-sidebar-foreground/80 hover:text-sidebar-foreground hover:bg-sidebar-accent/50 rayuela-interactive group",asChild:!0,children:(0,s.jsx)("a",{href:"mailto:<EMAIL>?subject=Feedback%20sobre%20Rayuela.ai",target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(x.hf,{icon:(0,s.jsx)(z.mm,{icon:k.A,size:"sm",context:"navigation",className:"rayuela-icon-exploration"}),children:"Feedback"})})}),(0,s.jsx)("div",{className:"mt-3 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"flex items-center gap-1 text-xs text-sidebar-foreground/60 group",children:[(0,s.jsx)(z.mm,{icon:A.A,size:"xs",context:"navigation",className:"rayuela-icon-exploration animate-pulse"}),(0,s.jsx)("span",{children:"Explora Rayuela.ai"})]})})]})]})}var _=r(5365),B=r(6671),J=r(4416),P=r(1264);function R(e){let{onClose:a}=e,{requestNewVerificationEmail:r}=(0,i.A)(),[t,c]=(0,n.useState)(!1),[o,d]=(0,n.useState)(!1);if(o)return null;let m=async()=>{c(!0);try{await r()&&B.o.success("Email de verificaci\xf3n enviado. Por favor, revisa tu bandeja de entrada.")}catch(a){console.error("Error al reenviar email de verificaci\xf3n:",a);let e=a instanceof Error?a.message:"Error al reenviar email de verificaci\xf3n.";B.o.error(e)}finally{c(!1)}};return(0,s.jsxs)(_.Fc,{variant:"warning",className:"mb-6 relative",children:[(0,s.jsxs)(_.XL,{className:"flex items-center",children:["Verificaci\xf3n de email pendiente",(0,s.jsxs)(l.Button,{variant:"ghost",size:"sm",className:"p-0 h-auto absolute top-2 right-2 text-amber-500 hover:text-amber-700 hover:bg-transparent",onClick:()=>{d(!0),a&&a()},children:[(0,s.jsx)(J.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Cerrar"})]})]}),(0,s.jsxs)(_.TN,{children:[(0,s.jsx)("p",{children:"Tu email a\xfan no ha sido verificado. Por favor, verifica tu email para acceder a todas las funcionalidades."}),(0,s.jsxs)(l.Button,{variant:"outline",size:"sm",className:"mt-2 border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800",onClick:m,disabled:t,children:[(0,s.jsx)(P.A,{className:"mr-2 h-4 w-4"}),t?"Enviando...":"Reenviar email de verificaci\xf3n"]})]})]})}function S(e){let{children:a}=e,{user:r,isLoading:l}=(0,i.A)(),c=(0,t.useRouter)(),[o,d]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{l||r||c.push("/login")},[r,l,c]),(0,n.useEffect)(()=>{r&&d(!1===r.email_verified)},[r]),l)?(0,s.jsx)("div",{children:"Cargando..."}):r?(0,s.jsxs)("div",{className:"flex h-screen bg-background",children:[(0,s.jsx)(C,{}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)(u,{}),(0,s.jsxs)("main",{className:"flex-1 overflow-x-hidden overflow-y-auto bg-muted/30 p-6",children:[o&&(0,s.jsx)(R,{onClose:()=>d(!1)}),a]})]})]}):null}},8534:(e,a,r)=>{"use strict";r.d(a,{mm:()=>l,vK:()=>o});var s=r(5155);r(2115);var n=r(9434);let t={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",xl:"h-8 w-8","2xl":"h-12 w-12"},i={success:"text-success",warning:"text-warning",error:"text-destructive",info:"text-info",primary:"text-primary",secondary:"text-secondary-foreground",muted:"text-muted-foreground",interactive:"text-primary hover:text-primary/80",neutral:"text-foreground",subtle:"text-muted-foreground",metric:"text-primary",action:"text-primary",navigation:"text-muted-foreground hover:text-foreground"};function l(e){let{icon:a,size:r="md",context:l="neutral",className:c,"aria-label":o,"aria-hidden":d=!o,...m}=e;return(0,s.jsx)(a,{className:(0,n.cn)(t[r],i[l],"shrink-0",c),"aria-label":o,"aria-hidden":d,...m})}let c={tight:"gap-1",normal:"gap-2",loose:"gap-3"};function o(e){let{icon:a,children:r,size:t="sm",context:i="neutral",iconPosition:o="left",spacing:d="normal",className:m}=e,x=(0,s.jsx)(l,{icon:a,size:t,context:i,"aria-hidden":!0});return(0,s.jsxs)("span",{className:(0,n.cn)("inline-flex items-center",c[d],m),children:["left"===o&&x,r,"right"===o&&x]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[9352,6874,1445,5674,3753,9876,2092,3999,8441,1684,7358],()=>a(3862)),_N_E=e.O()}]);