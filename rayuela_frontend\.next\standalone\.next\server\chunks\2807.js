exports.id=2807,exports.ids=[2807],exports.modules={151:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var s=a(49384),i=a(82348);function r(...e){return(0,i.QP)((0,s.$)(e))}},11823:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},23252:(e,t,a)=>{Promise.resolve().then(a.bind(a,6931)),Promise.resolve().then(a.bind(a,36515))},29523:(e,t,a)=>{"use strict";a.d(t,{Button:()=>l,r:()=>c});var s=a(60687),i=a(43210),r=a(8730),o=a(24224),n=a(4780);let c=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-body-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border focus-visible:border-ring",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 shadow-xs hover:shadow-soft rayuela-button-hover",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/95 shadow-xs hover:shadow-soft rayuela-button-hover",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80 shadow-xs hover:shadow-soft rayuela-button-hover",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90 shadow-xs hover:shadow-soft rayuela-button-hover",ghost:"hover:bg-accent hover:text-accent-foreground active:bg-accent/80 rayuela-button-hover",link:"text-primary underline-offset-4 hover:underline active:text-primary/80 rayuela-button-hover"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-lg px-3 text-caption-lg",lg:"h-11 rounded-lg px-8 text-body-lg",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef(({className:e,variant:t,size:a,asChild:i=!1,...o},l)=>{let d=i?r.DX:"button";return(0,s.jsx)(d,{className:(0,n.cn)(c({variant:t,size:a,className:e})),ref:l,...o})});l.displayName="Button"},36515:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>i});var s=a(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\auth.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\auth.tsx","useAuth")},44957:(e,t,a)=>{"use strict";a.d(t,{A:()=>p,AuthProvider:()=>d});var s=a(60687),i=a(43210),r=a(16189),o=a(62185),n=a(52581),c=a(81079);let l=(0,i.createContext)(void 0),d=({children:e})=>{let[t,a]=(0,i.useState)(null),[d,p]=(0,i.useState)(null),[u,m]=(0,i.useState)(null),[g,A]=(0,i.useState)(!0),[v,h]=(0,i.useState)(!1),[y,f]=(0,i.useState)(null),x=(0,r.useRouter)(),b=(0,r.usePathname)(),[P,V]=(0,i.useState)(null),I=(0,i.useCallback)(()=>{localStorage.removeItem("rayuela-token"),localStorage.removeItem("rayuela-apiKey"),a(null),p(null),m(null)},[]),j=(0,i.useCallback)(async(e,t)=>{A(!0);try{let s=await (0,o.jp)();if(!s.is_active)throw Error("User account is inactive.");return a(s),p(e),t&&m(t),console.log("User data fetched successfully:",s),!0}catch(e){return console.error("Token validation/fetch user data failed:",e),I(),b?.startsWith("/dashboard")&&(n.o.error("Tu sesi\xf3n ha expirado o es inv\xe1lida. Por favor, inicia sesi\xf3n de nuevo."),x.push("/login")),!1}finally{A(!1)}},[x,b,I]);(0,i.useEffect)(()=>{console.log("AuthProvider Mounted. Checking localStorage...");let e=localStorage.getItem("rayuela-token"),t=localStorage.getItem("rayuela-apiKey");e&&t?(console.log("Found token and apiKey in localStorage. Validating..."),j(e,t)):(console.log("No token or apiKey found in localStorage."),A(!1))},[j]);let w=(0,i.useCallback)(async()=>{if(!d)return n.o.error("No hay sesi\xf3n activa. Por favor, inicia sesi\xf3n de nuevo."),!1;try{return await (0,o.Hl)(),n.o.success("Email de verificaci\xf3n enviado. Por favor, revisa tu bandeja de entrada."),!0}catch(e){return console.error("Error al solicitar email de verificaci\xf3n:",e),e instanceof o.hD?n.o.error(e.message||"Error al solicitar email de verificaci\xf3n."):n.o.error("Error inesperado al solicitar email de verificaci\xf3n"),!1}},[d]),C=(0,i.useCallback)(async(e,t)=>{A(!0),V(null);try{let a=await (0,o.Lx)({email:e,password:t});if(a.access_token){localStorage.setItem("rayuela-token",a.access_token),p(a.access_token);let e=localStorage.getItem("rayuela-apiKey");if(e&&m(e),await j(a.access_token,e))return n.o.success("Login exitoso!"),x.push("/dashboard"),!0;return!1}throw Error("No se recibi\xf3 token de acceso.")}catch(s){if(console.error("Login processing failed:",s),s&&"object"==typeof s&&"error_code"in s&&"EMAIL_NOT_VERIFIED"===s.error_code)return V({email:e,password:t,message:s.message||"Por favor, verifica tu email para continuar."}),A(!1),!1;let a=s&&"object"==typeof s&&"message"in s?s.message:"Error al iniciar sesi\xf3n. Verifica tus credenciales.";return n.o.error(a),I(),A(!1),!1}},[j,x,I]),k=(0,i.useCallback)(async(e,t,a)=>{A(!0);try{let s=await (0,o.DY)(e,t,a);if(s.access_token){let e=s.api_key,t=s.access_token;return localStorage.setItem("rayuela-token",t),localStorage.setItem("rayuela-apiKey",e),p(t),m(e),f(e),h(!0),await j(t,e),{success:!0,apiKey:e}}throw Error("No se recibi\xf3 token de acceso")}catch(e){return console.error("Register processing failed:",e),e instanceof o.hD?n.o.error(e.message||"Error al registrarse"):n.o.error("Error inesperado al registrarse"),I(),{success:!1,error:e}}finally{A(!1)}},[j,I]),S=(0,i.useCallback)(async()=>{h(!1),f(null);let e=n.o.loading("Configurando tu cuenta...");A(!0);try{if(!d||!u)throw new o.hD("Token o API Key no disponibles",401,"AUTH_REQUIRED");let t=!1,a=0,s=null;for(;!t&&a<3;)try{await j(d,u),t=!0}catch(e){s=e,++a<3&&await new Promise(e=>setTimeout(e,500))}if(!t&&s)throw s;n.o.dismiss(e),n.o.success("\xa1Cuenta configurada correctamente!"),x.push("/dashboard")}catch(t){n.o.dismiss(e),t instanceof o.hD?n.o.error(t.message||"Error al inicializar la cuenta"):n.o.error("Error inesperado al configurar la cuenta")}finally{A(!1)}},[d,u,j,x]),R=(0,i.useCallback)(async()=>{try{d&&await (0,o.ri)(),I(),x.push("/login"),n.o.success("Sesi\xf3n cerrada correctamente")}catch(e){console.error("Logout error:",e),I(),x.push("/login")}},[d,I,x]);return(0,s.jsxs)(l.Provider,{value:{user:t,token:d,apiKey:u,setApiKey:m,login:C,register:k,logout:R,isLoading:g,emailVerificationError:P,requestNewVerificationEmail:w},children:[e,v&&y&&(0,s.jsx)(c.A,{apiKey:y,onClose:S})]})},p=()=>{let e=(0,i.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},56896:(e,t,a)=>{"use strict";a.d(t,{S:()=>c});var s=a(60687),i=a(43210),r=a(76194),o=a(13964),n=a(4780);let c=i.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.bL,{ref:a,className:(0,n.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:(0,s.jsx)(r.C1,{className:(0,n.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}));c.displayName=r.bL.displayName},59636:(e,t,a)=>{Promise.resolve().then(a.bind(a,52581)),Promise.resolve().then(a.bind(a,44957))},61135:()=>{},62185:(e,t,a)=>{"use strict";a.d(t,{A$:()=>p,DY:()=>c,Dm:()=>m,Hl:()=>d,Iq:()=>f,Lx:()=>n,M2:()=>v,PX:()=>y,S3:()=>h,T9:()=>A,XW:()=>x,_:()=>g,fw:()=>P,hD:()=>i,jp:()=>u,mA:()=>b,oE:()=>V,ri:()=>l});var s=a(81184);class i extends Error{constructor(e,t=500,a){super(e),this.name="ApiError",this.status=t,this.body=a}}function r(e,t){if(e&&"object"==typeof e&&"response"in e){let a=e.response?.status||500;throw new i(e.message||t,a,e.response?.data)}if(e instanceof Error)throw new i(e.message,500);throw new i(t,500)}let o=(0,s._C)(),n=async e=>{try{return await o.loginApiV1AuthTokenPost(e)}catch(e){r(e,"Login failed")}},c=async(e,t,a)=>{try{return await o.registerApiV1AuthRegisterPost({accountName:e,email:t,password:a})}catch(e){r(e,"Registration failed")}},l=async()=>o.logoutApiV1AuthLogoutPost(),d=async()=>o.sendVerificationEmailApiV1AuthSendVerificationEmailPost(),p=async e=>o.verifyEmailApiV1AuthVerifyEmailGet({token:e}),u=async()=>{try{return await o.getCurrentUserInfoApiV1SystemUsersMeGet()}catch(e){r(e,"Failed to get current user")}},m=async()=>{try{return await o.getAccountInfoApiV1AccountsCurrentGet()}catch(e){r(e,"Failed to get current account")}},g=m,A=async()=>{try{return await o.getAvailablePlansApiV1PlansGet()}catch(e){r(e,"Failed to get plans")}},v=async(e,t)=>{let a={};return e&&(a.start_date=e),t&&(a.end_date=t),o.getUsageHistoryApiV1UsageHistoryGet(a)},h=async()=>{try{return await o.getUsageSummaryApiV1UsageSummaryGet()}catch(e){r(e,"Failed to get usage summary")}},y=async()=>{try{return await o.listApiKeysApiV1ApiKeysGet()}catch(e){r(e,"Failed to get API keys")}},f=async e=>{try{let t={name:e.name};return await o.createApiKeyApiV1ApiKeysPost(t)}catch(e){r(e,"Failed to create API key")}},x=async(e,t)=>{try{let a={name:t.name};return await o.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(e),a)}catch(e){r(e,"Failed to update API key")}},b=async e=>o.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(e),P=async e=>o.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost({price_id:e}),V=async()=>o.createPortalSessionApiV1BillingCreatePortalSessionPost({})},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>p,Es:()=>m,L3:()=>g,c7:()=>u,lG:()=>n,rr:()=>A,zM:()=>c});var s=a(60687);a(43210);var i=a(4590),r=a(11860),o=a(4780);function n({...e}){return(0,s.jsx)(i.bL,{"data-slot":"dialog",...e})}function c({...e}){return(0,s.jsx)(i.l9,{"data-slot":"dialog-trigger",...e})}function l({...e}){return(0,s.jsx)(i.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,s.jsx)(i.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function p({className:e,children:t,...a}){return(0,s.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(i.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-medium duration-200 sm:max-w-lg",e),...a,children:[t,(0,s.jsxs)(i.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(r.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function m({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function g({className:e,...t}){return(0,s.jsx)(i.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t})}function A({className:e,...t}){return(0,s.jsx)(i.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}},81079:(e,t,a)=>{"use strict";a.d(t,{A:()=>y});var s=a(60687),i=a(43210),r=a(63503),o=a(29523),n=a(89667),c=a(91821),l=a(43649),d=a(13964),p=a(70615),u=a(24366),m=a(31158),g=a(84027),A=a(52581),v=a(56896),h=a(16189);let y=({apiKey:e,onClose:t})=>{let[a,y]=(0,i.useState)(!1),[f,x]=(0,i.useState)(!1),[b,P]=(0,i.useState)(!1),[V,I]=(0,i.useState)(!1),[j,w]=(0,i.useState)(!1),C=(0,h.useRouter)(),k=`curl -X GET "http://localhost:8001/health/auth" \\
  -H "X-API-Key: ${e}"`;(0,i.useEffect)(()=>{let e=e=>{if(!b)return e.preventDefault(),e.returnValue="",""};return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)},[b]);let S=()=>{navigator.clipboard.writeText(e).then(()=>{y(!0),I(!0),A.o.success("\xa1API Key copiada al portapapeles!"),setTimeout(()=>y(!1),2e3)}).catch(e=>{A.o.error("Error al copiar la API Key."),console.error("Error al copiar al portapapeles:",e)})},R=(e,t)=>{navigator.clipboard.writeText(e).then(()=>{x(!0),A.o.success(`\xa1C\xf3digo ${t} copiado al portapapeles!`),setTimeout(()=>x(!1),2e3)}).catch(e=>{A.o.error("Error al copiar el c\xf3digo."),console.error("Error al copiar al portapapeles:",e)})},N=()=>{try{let t=new Blob([`API Key de Rayuela
`,`----------------
`,`Fecha: ${new Date().toLocaleString()}
`,`API Key: ${e}

`,`IMPORTANTE: Guarda este archivo en un lugar seguro. Esta clave no se mostrar\xe1 completa nuevamente.`],{type:"text/plain"}),a=URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.download="rayuela-api-key.txt",document.body.appendChild(s),s.click(),setTimeout(()=>{document.body.removeChild(s),URL.revokeObjectURL(a)},0),I(!0),A.o.success("API Key descargada como archivo de texto")}catch(e){A.o.error("Error al descargar la API Key"),console.error("Error al descargar:",e)}},G=()=>{if(!V)return void w(!0);t()};return(0,s.jsx)(r.lG,{open:!0,onOpenChange:e=>!e&&G(),children:(0,s.jsxs)(r.Cf,{className:"sm:max-w-[650px] max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(r.c7,{children:[(0,s.jsxs)(r.L3,{className:"text-xl font-bold flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD11"})," Tu primera API Key est\xe1 lista"]}),(0,s.jsxs)(r.rr,{className:"py-2",children:[(0,s.jsx)("p",{className:"mb-3",children:"\xa1Bienvenido a Rayuela! Tu primera API Key se ha generado autom\xe1ticamente. \xdasala para autenticar todas tus solicitudes a la API."}),(0,s.jsxs)("p",{className:"mb-3 text-sm text-muted-foreground",children:["\uD83D\uDCA1 ",(0,s.jsx)("strong",{children:"Tip:"})," Puedes crear y gestionar m\xfaltiples API Keys para diferentes entornos o equipos desde la secci\xf3n 'API Keys' de tu dashboard."]}),(0,s.jsxs)(c.Fc,{variant:"warning",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(c.XL,{children:"⚠️ Solo se muestra una vez"}),(0,s.jsx)(c.TN,{children:"Copia y guarda tu API Key ahora. No podr\xe1s verla completa nuevamente."})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-4 mt-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Tu primera API Key:"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.p,{id:"apiKey",readOnly:!0,value:e,className:"flex-1 font-mono text-sm bg-muted border-2"}),(0,s.jsxs)(o.Button,{type:"button",size:"sm",onClick:S,className:"min-w-[90px]",children:[a?(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}):(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),a?"\xa1Copiado!":"Copiar"]})]})]}),(0,s.jsxs)("div",{className:"bg-success-light border border-success/20 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-success"}),(0,s.jsx)("h4",{className:"font-semibold text-success-foreground",children:"\uD83D\uDE80 Prueba tu API Key ahora"})]}),(0,s.jsx)("p",{className:"text-sm text-success-foreground mb-3",children:"Ejecuta este comando para verificar que tu API Key funciona:"}),(0,s.jsxs)("div",{className:"bg-card border rounded-md p-3 relative",children:[(0,s.jsx)("pre",{className:"text-info text-xs overflow-x-auto",children:(0,s.jsx)("code",{children:k})}),(0,s.jsx)(o.Button,{type:"button",size:"sm",variant:"ghost",onClick:()=>R(k,"cURL"),className:"absolute top-2 right-2 h-8 w-8 p-0 text-muted-foreground hover:text-foreground",children:f?(0,s.jsx)(d.A,{className:"h-4 w-4"}):(0,s.jsx)(p.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("p",{className:"text-xs text-success-foreground mt-2",children:"Deber\xedas recibir una respuesta con status 200 y un mensaje de bienvenida."})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between gap-4 pt-4",children:[(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)(o.Button,{type:"button",onClick:N,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),"Descargar como archivo"]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(v.S,{id:"confirmSaved",checked:b,onCheckedChange:e=>P(e)}),(0,s.jsx)("label",{htmlFor:"confirmSaved",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"He guardado mi API Key de forma segura"})]})]}),(0,s.jsxs)(c.Fc,{variant:"info",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(c.XL,{children:"Informaci\xf3n importante"}),(0,s.jsx)(c.TN,{children:(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm mt-2",children:[(0,s.jsxs)("li",{children:["Usa esta API Key en el header ",(0,s.jsx)("code",{className:"text-code-inline",children:"X-API-Key"})," de tus solicitudes"]}),(0,s.jsx)("li",{children:"No compartas tu API Key p\xfablicamente"}),(0,s.jsx)("li",{children:"Puedes crear m\xfaltiples API Keys desde tu panel de control"}),(0,s.jsx)("li",{children:"Si pierdes tu API Key, puedes crear una nueva y revocar la anterior"})]})})]})]}),(0,s.jsxs)(r.Es,{className:"flex flex-col gap-2 sm:flex-row sm:gap-3",children:[(0,s.jsxs)(o.Button,{onClick:()=>{t(),C.push("/api-keys")},variant:"outline",disabled:!V,className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),"Ir a Gesti\xf3n de API Keys"]}),(0,s.jsx)(o.Button,{onClick:t,disabled:!V,className:"flex-1",children:V?"Continuar al Dashboard":"Primero copia o descarga tu API Key"})]}),j&&(0,s.jsxs)(c.Fc,{variant:"warning",className:"mt-4",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(c.XL,{children:"⚠️ Advertencia"}),(0,s.jsxs)(c.TN,{children:[(0,s.jsx)("p",{className:"text-sm mb-3",children:"A\xfan no has copiado o descargado tu API Key. Esta es la \xfanica vez que podr\xe1s verla completa."}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(o.Button,{onClick:S,size:"sm",variant:"outline",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Copiar API Key"]}),(0,s.jsxs)(o.Button,{onClick:N,size:"sm",variant:"outline",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Descargar"]}),(0,s.jsx)(o.Button,{onClick:()=>w(!1),size:"sm",variant:"ghost",children:"Cancelar"})]})]})]})]})})}},81184:(e,t,a)=>{"use strict";a.d(t,{_C:()=>i});var s=a(51060);let i=()=>({healthCheckHealthGet:e=>s.A.get("/health",e),sendVerificationEmailApiV1AuthSendVerificationEmailPost:e=>s.A.post("/api/v1/auth/send-verification-email",void 0,e),verifyEmailApiV1AuthVerifyEmailGet:(e,t)=>s.A.get("/api/v1/auth/verify-email",{...t,params:{...e,...t?.params}}),registerApiV1AuthRegisterPost:(e,t)=>s.A.post("/api/v1/auth/register",e,t),loginApiV1AuthTokenPost:(e,t)=>s.A.post("/api/v1/auth/token",e,t),logoutApiV1AuthLogoutPost:e=>s.A.post("/api/v1/auth/logout",void 0,e),listAccountsApiV1AccountsGet:e=>s.A.get("/api/v1/accounts/",e),createAccountApiV1AccountsAccountsPost:(e,t)=>s.A.post("/api/v1/accounts/accounts",e,t),getAccountApiV1AccountsAccountIdGet:(e,t)=>s.A.get(`/api/v1/accounts/${e}`,t),deactivateAccountApiV1AccountsAccountIdDeactivatePatch:(e,t)=>s.A.patch(`/api/v1/accounts/${e}/deactivate`,void 0,t),activateAccountApiV1AccountsAccountIdActivatePatch:(e,t)=>s.A.patch(`/api/v1/accounts/${e}/activate`,void 0,t),getAccountInfoApiV1AccountsCurrentGet:e=>s.A.get("/api/v1/accounts/current",e),updateCurrentAccountApiV1AccountsCurrentPut:(e,t)=>s.A.put("/api/v1/accounts/current",e,t),patchCurrentAccountApiV1AccountsCurrentPatch:(e,t)=>s.A.patch("/api/v1/accounts/current",e,t),getAuditLogsApiV1AccountsAccountIdAuditLogsGet:(e,t,a)=>s.A.get(`/api/v1/accounts/${e}/audit-logs`,{...a,params:{...t,...a?.params}}),getApiUsageApiV1AccountsUsageGet:e=>s.A.get("/api/v1/accounts/usage",e),getAvailablePlansApiV1PlansGet:e=>s.A.get("/api/v1/plans/",e),getCurrentUserInfoApiV1SystemUsersMeGet:e=>s.A.get("/api/v1/system-users/me",e),updateUserMeApiV1SystemUsersMePut:(e,t)=>s.A.put("/api/v1/system-users/me",e,t),deleteUserMeApiV1SystemUsersMeDelete:e=>s.A.delete("/api/v1/system-users/me",e),createSystemUserApiV1SystemUsersPost:(e,t)=>s.A.post("/api/v1/system-users/",e,t),getSystemUserApiV1SystemUsersUserIdGet:(e,t)=>s.A.get(`/api/v1/system-users/${e}`,t),createRoleApiV1SystemUsersRolesPost:(e,t)=>s.A.post("/api/v1/system-users/roles/",e,t),assignRoleApiV1SystemUsersUserIdRolesRoleIdPost:(e,t,a)=>s.A.post(`/api/v1/system-users/${e}/roles/${t}`,void 0,a),removeRoleApiV1SystemUsersUserIdRolesRoleIdDelete:(e,t,a)=>s.A.delete(`/api/v1/system-users/${e}/roles/${t}`,a),getUserRolesApiV1SystemUsersUserIdRolesGet:(e,t)=>s.A.get(`/api/v1/system-users/${e}/roles`,t),getUserPermissionsApiV1SystemUsersUserIdPermissionsGet:(e,t)=>s.A.get(`/api/v1/system-users/${e}/permissions`,t),createEndUserApiV1EndUsersPost:(e,t)=>s.A.post("/api/v1/end-users/",e,t),readEndUsersApiV1EndUsersGet:(e,t)=>s.A.get("/api/v1/end-users/",{...t,params:{...e,...t?.params}}),readEndUserApiV1EndUsersUserIdGet:(e,t)=>s.A.get(`/api/v1/end-users/${e}`,t),createProductApiV1ProductsPost:(e,t)=>s.A.post("/api/v1/products/",e,t),readProductsApiV1ProductsGet:(e,t)=>s.A.get("/api/v1/products/",{...t,params:{...e,...t?.params}}),getProductApiV1ProductsProductIdGet:(e,t)=>s.A.get(`/api/v1/products/${e}`,t),updateProductApiV1ProductsProductIdPut:(e,t,a)=>s.A.put(`/api/v1/products/${e}`,t,a),updateInventoryApiV1ProductsProductIdInventoryPatch:(e,t,a)=>s.A.patch(`/api/v1/products/${e}/inventory`,t,a),getMostSearchedApiV1RecommendationsMostSearchedGet:(e,t)=>s.A.get("/api/v1/recommendations/most-searched/",{...t,params:{...e,...t?.params}}),getTrendingSearchesApiV1RecommendationsTrendingSearchesGet:(e,t)=>s.A.get("/api/v1/recommendations/trending-searches/",{...t,params:{...e,...t?.params}}),getPopularTrendsApiV1RecommendationsPopularTrendsGet:(e,t)=>s.A.get("/api/v1/recommendations/popular-trends/",{...t,params:{...e,...t?.params}}),getRelatedSearchesApiV1RecommendationsRelatedSearchesProductIdGet:(e,t,a)=>s.A.get(`/api/v1/recommendations/related-searches/${e}`,{...a,params:{...t,...a?.params}}),getMostSoldApiV1RecommendationsMostSoldGet:(e,t)=>s.A.get("/api/v1/recommendations/most-sold/",{...t,params:{...e,...t?.params}}),getTopRatedApiV1RecommendationsTopRatedGet:(e,t)=>s.A.get("/api/v1/recommendations/top-rated/",{...t,params:{...e,...t?.params}}),getCategoryProductsApiV1RecommendationsCategoryCategoryGet:(e,t,a)=>s.A.get(`/api/v1/recommendations/category/${e}`,{...a,params:{...t,...a?.params}}),getRelatedCategoriesApiV1RecommendationsRelatedCategoriesCategoryGet:(e,t,a)=>s.A.get(`/api/v1/recommendations/related-categories/${e}`,{...a,params:{...t,...a?.params}}),getAlsoBoughtApiV1RecommendationsAlsoBoughtProductIdGet:(e,t,a)=>s.A.get(`/api/v1/recommendations/also-bought/${e}`,{...a,params:{...t,...a?.params}}),getSimilarProductsApiV1RecommendationsProductsProductIdSimilarGet:(e,t,a)=>s.A.get(`/api/v1/recommendations/products/${e}/similar`,{...a,params:{...t,...a?.params}}),invalidateUserCacheApiV1RecommendationsInvalidateCacheUserIdPost:(e,t)=>s.A.post(`/api/v1/recommendations/invalidate-cache/${e}`,void 0,t),invalidateAccountCacheApiV1RecommendationsInvalidateCachePost:e=>s.A.post("/api/v1/recommendations/invalidate-cache",void 0,e),queryPersonalizedRecommendationsApiV1RecommendationsPersonalizedQueryPost:(e,t)=>s.A.post("/api/v1/recommendations/personalized/query",e,t),getRecommendationExplanationApiV1RecommendationsExplainUserIdItemIdGet:(e,t,a)=>s.A.get(`/api/v1/recommendations/explain/${e}/${t}`,a),getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet:e=>s.A.get("/api/v1/recommendations/confidence-metrics",e),rollbackModelApiV1RecommendationsRollbackArtifactVersionPost:(e,t)=>s.A.post(`/api/v1/recommendations/rollback/${e}`,void 0,t),createInteractionApiV1InteractionsPost:(e,t)=>s.A.post("/api/v1/interactions/",e,t),readInteractionsApiV1InteractionsGet:(e,t)=>s.A.get("/api/v1/interactions/",{...t,params:{...e,...t?.params}}),trainModelsApiV1PipelineTrainPost:e=>s.A.post("/api/v1/pipeline/train",void 0,e),getTrainingStatusApiV1PipelineStatusGet:e=>s.A.get("/api/v1/pipeline/status",e),getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet:(e,t)=>s.A.get(`/api/v1/pipeline/jobs/${e}/status`,t),listModelsApiV1PipelineModelsGet:(e,t)=>s.A.get("/api/v1/pipeline/models",{...t,params:{...e,...t?.params}}),getModelMetricsApiV1PipelineModelsModelIdMetricsGet:(e,t)=>s.A.get(`/api/v1/pipeline/models/${e}/metrics`,t),invalidateCacheApiV1PipelineInvalidateCachePost:(e,t)=>s.A.post("/api/v1/pipeline/invalidate-cache",void 0,{...t,params:{...e,...t?.params}}),trainArtifactForAccountApiV1PipelineTrainAccountIdPost:(e,t)=>s.A.post(`/api/v1/pipeline/train/${e}`,void 0,t),processTrainingJobApiV1PipelineProcessPost:(e,t)=>s.A.post("/api/v1/pipeline/process",e,t),trainingCallbackApiV1PipelineCallbackJobIdPost:(e,t,a)=>s.A.post(`/api/v1/pipeline/callback/${e}`,t,a),testCacheApiV1CacheTestCacheGet:e=>s.A.get("/api/v1/cache/test-cache",e),checkRedisApiV1CacheRedisHealthGet:e=>s.A.get("/api/v1/cache/redis-health",e),getAccountAnalyticsApiV1AnalyticsAnalyticsAccountGet:(e,t)=>s.A.get("/api/v1/analytics/analytics/account",{...t,params:{...e,...t?.params}}),getEndpointAnalyticsApiV1AnalyticsAnalyticsEndpointsGet:(e,t)=>s.A.get("/api/v1/analytics/analytics/endpoints",{...t,params:{...e,...t?.params}}),getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet:(e,t)=>s.A.get("/api/v1/analytics/analytics/recommendation_performance",{...t,params:{...e,...t?.params}}),compareModelVersionsApiV1AnalyticsAnalyticsModelsCompareGet:(e,t)=>s.A.get("/api/v1/analytics/analytics/models/compare",{...t,params:{...e,...t?.params}}),getMetricsHistoryApiV1AnalyticsAnalyticsMetricsHistoryGet:(e,t)=>s.A.get("/api/v1/analytics/analytics/metrics/history",{...t,params:{...e,...t?.params}}),createCheckoutSessionApiV1BillingCreateCheckoutSessionPost:(e,t)=>s.A.post("/api/v1/billing/create-checkout-session",e,t),createPortalSessionApiV1BillingCreatePortalSessionPost:(e,t)=>s.A.post("/api/v1/billing/create-portal-session",e,t),mercadopagoWebhookApiV1BillingWebhookMercadopagoPost:e=>s.A.post("/api/v1/billing/webhook/mercadopago",void 0,e),listRolesApiV1RolesGet:e=>s.A.get("/api/v1/roles/",e),createRoleApiV1RolesPost:(e,t)=>s.A.post("/api/v1/roles/",e,t),getRoleApiV1RolesRoleIdGet:(e,t)=>s.A.get(`/api/v1/roles/${e}`,t),updateRoleApiV1RolesRoleIdPut:(e,t,a)=>s.A.put(`/api/v1/roles/${e}`,t,a),deleteRoleApiV1RolesRoleIdDelete:(e,t)=>s.A.delete(`/api/v1/roles/${e}`,t),getRolePermissionsApiV1RolesRoleIdPermissionsGet:(e,t)=>s.A.get(`/api/v1/roles/${e}/permissions`,t),assignPermissionToRoleApiV1RolesRoleIdPermissionsPermissionIdPost:(e,t,a)=>s.A.post(`/api/v1/roles/${e}/permissions/${t}`,void 0,a),removePermissionFromRoleApiV1RolesRoleIdPermissionsPermissionIdDelete:(e,t,a)=>s.A.delete(`/api/v1/roles/${e}/permissions/${t}`,a),listPermissionsApiV1PermissionsGet:e=>s.A.get("/api/v1/permissions/",e),createPermissionApiV1PermissionsPost:(e,t)=>s.A.post("/api/v1/permissions/",e,t),getPermissionApiV1PermissionsPermissionIdGet:(e,t)=>s.A.get(`/api/v1/permissions/${e}`,t),getRolesWithPermissionApiV1PermissionsPermissionIdRolesGet:(e,t)=>s.A.get(`/api/v1/permissions/${e}/roles`,t),cleanupAuditLogsApiV1MaintenanceMaintenanceCleanupAuditLogsPost:(e,t)=>s.A.post("/api/v1/maintenance/maintenance/cleanup-audit-logs",void 0,{...t,params:{...e,...t?.params}}),cleanupInteractionsApiV1MaintenanceMaintenanceCleanupInteractionsPost:(e,t)=>s.A.post("/api/v1/maintenance/maintenance/cleanup-interactions",void 0,{...t,params:{...e,...t?.params}}),getTaskStatusApiV1MaintenanceMaintenanceTaskTaskIdGet:(e,t)=>s.A.get(`/api/v1/maintenance/maintenance/task/${e}`,t),cleanupDataSecureEndpointApiV1MaintenanceMaintenanceCleanupDataSecurePost:(e,t)=>s.A.post("/api/v1/maintenance/maintenance/cleanup-data-secure",void 0,{...t,params:{...e,...t?.params}}),archiveAndCleanupAuditLogsApiV1MaintenanceMaintenanceArchiveAndCleanupAuditLogsPost:(e,t)=>s.A.post("/api/v1/maintenance/maintenance/archive-and-cleanup-audit-logs",void 0,{...t,params:{...e,...t?.params}}),archiveAndCleanupInteractionsApiV1MaintenanceMaintenanceArchiveAndCleanupInteractionsPost:(e,t)=>s.A.post("/api/v1/maintenance/maintenance/archive-and-cleanup-interactions",void 0,{...t,params:{...e,...t?.params}}),listArchivedFilesApiV1MaintenanceMaintenanceArchivedFilesTableNameGet:(e,t,a)=>s.A.get(`/api/v1/maintenance/maintenance/archived-files/${e}`,{...a,params:{...t,...a?.params}}),cleanupSoftDeletedRecordsApiV1MaintenanceMaintenanceCleanupSoftDeletedRecordsPost:(e,t)=>s.A.post("/api/v1/maintenance/maintenance/cleanup-soft-deleted-records",void 0,{...t,params:{...e,...t?.params}}),getSoftDeleteStatisticsApiV1MaintenanceMaintenanceSoftDeleteStatisticsGet:(e,t)=>s.A.get("/api/v1/maintenance/maintenance/soft-delete-statistics",{...t,params:{...e,...t?.params}}),monitorTablesApiV1MaintenanceMaintenanceMonitorTablesPost:(e,t)=>s.A.post("/api/v1/maintenance/maintenance/monitor-tables",void 0,{...t,params:{...e,...t?.params}}),getSubscriptionUsageApiV1SubscriptionUsageGet:e=>s.A.get("/api/v1/subscription/usage",e),getStorageUsageApiV1StorageUsageGet:e=>s.A.get("/api/v1/storage/usage",e),refreshStorageUsageApiV1StorageRefreshPost:e=>s.A.post("/api/v1/storage/refresh",void 0,e),batchDataIngestionApiV1IngestionBatchPost:(e,t)=>s.A.post("/api/v1/ingestion/batch",e,t),getBatchJobStatusApiV1IngestionBatchJobIdGet:(e,t)=>s.A.get(`/api/v1/ingestion/batch/${e}`,t),getUsageSummaryApiV1UsageSummaryGet:e=>s.A.get("/api/v1/usage/summary",e),getUsageHistoryApiV1UsageHistoryGet:(e,t)=>s.A.get("/api/v1/usage/history",{...t,params:{...e,...t?.params}}),listApiKeysApiV1ApiKeysGet:e=>s.A.get("/api/v1/api-keys/",e),createApiKeyApiV1ApiKeysPost:(e,t)=>s.A.post("/api/v1/api-keys/",e,t),revokeApiKeyApiV1ApiKeysDelete:e=>s.A.delete("/api/v1/api-keys/",e),getCurrentApiKeyApiV1ApiKeysCurrentGet:e=>s.A.get("/api/v1/api-keys/current",e),updateApiKeyApiV1ApiKeysApiKeyIdPut:(e,t,a)=>s.A.put(`/api/v1/api-keys/${e}`,t,a),revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete:(e,t)=>s.A.delete(`/api/v1/api-keys/${e}`,t)})},89667:(e,t,a)=>{"use strict";a.d(t,{p:()=>r});var s=a(60687);a(43210);var i=a(4780);function r({className:e,type:t,...a}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-lg border bg-transparent px-3 py-1 text-base shadow-soft rayuela-interactive rayuela-focus-ring outline-none hover:border-ring/50 file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...a})}},91821:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>c,TN:()=>d,XL:()=>l});var s=a(60687),i=a(43210),r=a(24224),o=a(4780);let n=(0,r.F)("relative w-full rounded-lg border p-4 transition-all hover:shadow-sm [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground border-border",destructive:"border-destructive/50 text-destructive bg-destructive/5 hover:bg-destructive/10 [&>svg]:text-destructive",success:"border-success/50 text-success bg-success-light hover:bg-success/10 [&>svg]:text-success",warning:"border-warning/50 text-warning bg-warning-light hover:bg-warning/10 [&>svg]:text-warning",info:"border-info/50 text-info bg-info-light hover:bg-info/10 [&>svg]:text-info"}},defaultVariants:{variant:"default"}}),c=i.forwardRef(({className:e,variant:t,...a},i)=>(0,s.jsx)("div",{ref:i,role:"alert",className:(0,o.cn)(n({variant:t}),e),...a}));c.displayName="Alert";let l=i.forwardRef(({className:e,...t},a)=>(0,s.jsx)("h5",{ref:a,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",e),...t}));l.displayName="AlertTitle";let d=i.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l,metadata:()=>c});var s=a(37413),i=a(86187),r=a.n(i);a(61135);var o=a(36515),n=a(6931);let c={title:"Rayuela.ai | Recommendation System as a Service",description:"Sistemas de recomendaci\xf3n avanzados para tu negocio, sin la complejidad de construirlos desde cero.",icons:{icon:"/favicon.ico"}};function l({children:e}){return(0,s.jsx)("html",{lang:"es",className:r().variable,children:(0,s.jsxs)("body",{className:r().className,children:[(0,s.jsx)(o.AuthProvider,{children:e}),(0,s.jsx)(n.Toaster,{position:"top-right",richColors:!0})]})})}}};