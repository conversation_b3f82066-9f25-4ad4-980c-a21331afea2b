#!/usr/bin/env python3
"""
Script para actualizar la tabla alembic_version directamente usando psycopg2.
Este script se ejecutará en el contenedor de Cloud Run donde psycopg2 está disponible.
"""

import os
import sys
import psycopg2
from psycopg2 import sql


def get_database_connection():
    """Crear conexión a la base de datos usando las variables de entorno."""
    try:
        conn = psycopg2.connect(
            host=os.getenv("POSTGRES_SERVER"),
            port=os.getenv("POSTGRES_PORT", "5432"),
            database=os.getenv("POSTGRES_DB"),
            user=os.getenv("POSTGRES_USER"),
            password=os.getenv("POSTGRES_PASSWORD")
        )
        return conn
    except Exception as e:
        print(f"❌ Error conectando a la base de datos: {e}")
        sys.exit(1)


def fix_alembic_version():
    """Actualizar la tabla alembic_version para apuntar a la migración consolidada."""
    
    print("🔧 REPARACIÓN DE ALEMBIC VERSION")
    print("=" * 40)
    
    # Conectar a la base de datos
    print("🔗 Conectando a la base de datos...")
    conn = get_database_connection()
    cursor = conn.cursor()
    
    try:
        # Verificar el estado actual
        cursor.execute("SELECT version_num FROM alembic_version LIMIT 1")
        current_version = cursor.fetchone()
        
        if current_version:
            current_version = current_version[0]
            print(f"📋 Versión actual: {current_version}")
        else:
            print("📋 No hay versión actual en alembic_version")
        
        # Nueva versión consolidada
        new_version = "ea4920022505"  # ID de nuestra migración consolidada
        print(f"🎯 Nueva versión objetivo: {new_version}")
        
        if current_version == new_version:
            print("✅ La versión ya está actualizada")
            return
        
        # Actualizar la tabla alembic_version
        print("🔄 Actualizando tabla alembic_version...")
        
        if current_version:
            # Actualizar la fila existente
            cursor.execute(
                "UPDATE alembic_version SET version_num = %s",
                (new_version,)
            )
        else:
            # Insertar nueva fila si no existe
            cursor.execute(
                "INSERT INTO alembic_version (version_num) VALUES (%s)",
                (new_version,)
            )
        
        # Confirmar los cambios
        conn.commit()
        
        # Verificar el cambio
        cursor.execute("SELECT version_num FROM alembic_version LIMIT 1")
        updated_version = cursor.fetchone()[0]
        print(f"✅ Versión actualizada: {updated_version}")
        
        if updated_version == new_version:
            print("🎉 ¡Actualización exitosa!")
        else:
            print("❌ Error: La actualización no se aplicó correctamente")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error al actualizar alembic_version: {e}")
        conn.rollback()
        sys.exit(1)
    finally:
        cursor.close()
        conn.close()
        print("🔒 Conexión cerrada")


if __name__ == "__main__":
    fix_alembic_version()
