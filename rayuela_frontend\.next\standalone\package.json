{"name": "<PERSON><PERSON><PERSON>-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run generate-api:dev && next dev --turbopack", "dev:watch": "next dev --turbopack", "build": "npm run generate-api:production && next build", "build:production": "npm run fetch-openapi:force && ORVAL_USE_STATIC=true npm run generate-api:production && next build", "build:static": "ORVAL_USE_STATIC=true npm run generate-api:production && next build", "start": "next start", "lint": "next lint", "fetch-openapi": "node src/scripts/fetch-openapi.js", "fetch-openapi:force": "npm run fetch-openapi -- --force", "fetch-openapi:verbose": "npm run fetch-openapi -- --verbose", "generate-api": "orval --config orval.config.ts", "generate-api:dev": "npm run generate-api", "generate-api:static": "npm run fetch-openapi:verbose && ORVAL_USE_STATIC=true npm run generate-api", "generate-api:force": "npm run fetch-openapi:force && ORVAL_USE_STATIC=true npm run generate-api", "generate-api:production": "node src/scripts/patch-openapi-static.js && ORVAL_USE_STATIC=true orval --config orval.config.ts", "sync-api": "node scripts/sync-api.js", "sync-api:force": "node scripts/sync-api.js --force", "sync-api:watch": "node scripts/sync-api.js --watch", "test-openapi": "ts-node --project tsconfig.node.json src/scripts/test-openapi-generation.ts", "test-openapi:verbose": "npm run test-openapi -- --verbose", "migrate:colors": "node scripts/migrations/replace-blue-tailwind.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.3", "@types/next": "^8.0.7", "autoprefixer": "^10.4.21", "axios": "^1.6.7", "chart.js": "^4.4.9", "chartjs-plugin-annotation": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.488.0", "next": "15.3.0", "next-themes": "^0.4.6", "orval": "^6.25.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.1.3", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/node-fetch": "^2.6.11", "@types/react": "^19.1.7", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.0", "node-fetch": "^3.3.2", "ts-node": "^10.9.2", "typescript": "^5", "glob": "^10.3.10"}, "type": "module"}