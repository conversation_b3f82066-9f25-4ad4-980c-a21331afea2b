<!DOCTYPE html><html lang="es" class="__variable_e8ce0c"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/74726d29afef2ab2.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/554aa4ecb4bf38a8.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-a18916eaa43bd3fa.js"/><script src="/_next/static/chunks/4bd1b696-1c31b9758c3426b8.js" async=""></script><script src="/_next/static/chunks/1684-6d160cce63225728.js" async=""></script><script src="/_next/static/chunks/main-app-733d1219104ce150.js" async=""></script><script src="/_next/static/chunks/9352-31b4412fb2722f24.js" async=""></script><script src="/_next/static/chunks/1445-21443766417aa7af.js" async=""></script><script src="/_next/static/chunks/5674-7748bf837df56152.js" async=""></script><script src="/_next/static/chunks/3753-51055f9a7954ec13.js" async=""></script><script src="/_next/static/chunks/2092-d6fb07d59d636626.js" async=""></script><script src="/_next/static/chunks/3999-62f798ec15041d09.js" async=""></script><script src="/_next/static/chunks/app/layout-71c41bba19c0ef97.js" async=""></script><script src="/_next/static/chunks/6874-da306f8e1ba36f76.js" async=""></script><script src="/_next/static/chunks/app/(public)/docs/quickstart/python/page-0e486aeb324c93f6.js" async=""></script><title>Inicio Rápido con Python - Documentación | Rayuela.ai</title><meta name="description" content="Aprende a integrar Rayuela en tu aplicación Python en menos de 5 minutos. Guía paso a paso con ejemplos de código."/><meta name="author" content="Rayuela Team"/><meta name="keywords" content="python, quickstart, tutorial, API, integración, SDK"/><meta name="creator" content="Rayuela"/><meta name="publisher" content="Rayuela"/><meta name="robots" content="index, follow"/><link rel="canonical" href="https://rayuela.ai/docs/quickstart/python"/><meta property="og:title" content="Inicio Rápido con Python - Documentación | Rayuela.ai"/><meta property="og:description" content="Aprende a integrar Rayuela en tu aplicación Python en menos de 5 minutos. Guía paso a paso con ejemplos de código."/><meta property="og:url" content="https://rayuela.ai/docs/quickstart/python"/><meta property="og:site_name" content="Rayuela.ai"/><meta property="og:locale" content="es_AR"/><meta property="og:image" content="https://rayuela.ai/og-image.png"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="Inicio Rápido con Python - Documentación"/><meta property="og:type" content="article"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@rayuela_ai"/><meta name="twitter:title" content="Inicio Rápido con Python - Documentación | Rayuela.ai"/><meta name="twitter:description" content="Aprende a integrar Rayuela en tu aplicación Python en menos de 5 minutos. Guía paso a paso con ejemplos de código."/><meta name="twitter:image" content="https://rayuela.ai/og-image.png"/><link rel="icon" href="/favicon.ico"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div><main><script type="application/ld+json">{"@context":"https://schema.org","@type":"APIReference","name":"Rayuela Python Quickstart Guide","description":"Complete guide to integrate Rayuela recommendation system with Python","url":"https://rayuela.ai/docs/quickstart/python","programmingLanguage":"Python"}</script><div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="container mx-auto px-4 py-16 max-w-4xl"><nav class="mb-8 text-sm"><a class="text-blue-600 hover:text-blue-800" href="/docs">Documentación</a><span class="mx-2 text-gray-500">/</span><a class="text-blue-600 hover:text-blue-800" href="/docs/quickstart">Inicio Rápido</a><span class="mx-2 text-gray-500">/</span><span class="text-gray-700">Python</span></nav><div class="mb-12"><h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">🐍 Inicio Rápido con Python</h1><p class="text-xl text-gray-600 dark:text-gray-300">Integra Rayuela en tu aplicación Python en menos de 5 minutos</p></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div data-slot="card-title" class="text-subheading rayuela-accent flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code w-5 h-5 mr-2"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg>Requisitos Previos</div></div><div data-slot="card-content" class="px-6"><ul class="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300"><li>Python 3.7 o superior</li><li>pip (gestor de paquetes de Python)</li><li>Una cuenta en Rayuela (gratis)</li><li>Tu API Key de Rayuela</li></ul></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div data-slot="card-title" class="text-subheading rayuela-accent">1. Instalación</div><div data-slot="card-description" class="text-caption">Instala el SDK de Python para Rayuela</div></div><div data-slot="card-content" class="px-6"><div class="bg-gray-900 rounded-lg p-4 mb-4"><code class="text-green-400 text-sm">pip install rayuela-python</code></div><p class="text-sm text-gray-600 dark:text-gray-400">O si usas poetry:</p><div class="bg-gray-900 rounded-lg p-4 mt-2"><code class="text-green-400 text-sm">poetry add rayuela-python</code></div></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div data-slot="card-title" class="text-subheading rayuela-accent">2. Configuración</div><div data-slot="card-description" class="text-caption">Configura tu cliente de Rayuela</div></div><div data-slot="card-content" class="px-6"><div class="bg-gray-900 rounded-lg p-4 mb-4"><pre class="text-green-400 text-sm overflow-x-auto">from rayuela import RayuelaClient

# Inicializa el cliente
client = RayuelaClient(
    api_key=&quot;tu-api-key-aqui&quot;,
    base_url=&quot;https://api.rayuela.ai&quot;  # Opcional
)

# Verifica la conexión
try:
    health = client.health_check()
    print(&quot;✅ Conexión exitosa:&quot;, health)
except Exception as e:
    print(&quot;❌ Error de conexión:&quot;, e)</pre></div><div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"><p class="text-sm text-blue-800 dark:text-blue-200"><strong>💡 Tip:</strong> Nunca hardcodees tu API key. Usa variables de entorno:</p><div class="bg-gray-900 rounded-lg p-2 mt-2"><code class="text-green-400 text-xs">import os<br/>api_key = os.getenv(&quot;RAYUELA_API_KEY&quot;)</code></div></div></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div data-slot="card-title" class="text-subheading rayuela-accent">3. Tu Primera Recomendación</div><div data-slot="card-description" class="text-caption">Obtén recomendaciones para un usuario</div></div><div data-slot="card-content" class="px-6"><div class="bg-gray-900 rounded-lg p-4 mb-4"><pre class="text-green-400 text-sm overflow-x-auto"># Obtener recomendaciones para un usuario
recommendations = client.get_recommendations(
    user_id=&quot;user_123&quot;,
    limit=5,
    filters={
        &quot;category&quot;: &quot;electronics&quot;,
        &quot;price_max&quot;: 1000
    }
)

print(&quot;Recomendaciones:&quot;)
for rec in recommendations:
    print(f&quot;- Producto {rec[&#x27;product_id&#x27;]}: {rec[&#x27;score&#x27;]:.2f}&quot;)</pre></div><h4 class="font-semibold mb-2">Respuesta esperada:</h4><div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4"><pre class="text-sm text-gray-700 dark:text-gray-300">Recomendaciones:
- Producto prod_456: 0.95
- Producto prod_789: 0.87
- Producto prod_123: 0.82
- Producto prod_321: 0.78
- Producto prod_654: 0.75</pre></div></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div data-slot="card-title" class="text-subheading rayuela-accent">4. Enviar Datos de Interacción</div><div data-slot="card-description" class="text-caption">Mejora las recomendaciones enviando datos de comportamiento</div></div><div data-slot="card-content" class="px-6"><div class="bg-gray-900 rounded-lg p-4 mb-4"><pre class="text-green-400 text-sm overflow-x-auto"># Registrar una interacción
client.track_interaction(
    user_id=&quot;user_123&quot;,
    product_id=&quot;prod_456&quot;,
    interaction_type=&quot;view&quot;,
    timestamp=&quot;2024-01-15T10:30:00Z&quot;
)

# Registrar una compra
client.track_interaction(
    user_id=&quot;user_123&quot;,
    product_id=&quot;prod_456&quot;,
    interaction_type=&quot;purchase&quot;,
    value=299.99,
    timestamp=&quot;2024-01-15T10:35:00Z&quot;
)</pre></div></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div data-slot="card-title" class="text-subheading rayuela-accent">🚀 Próximos Pasos</div></div><div data-slot="card-content" class="px-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><a class="p-4 border rounded-lg hover:shadow-md transition-shadow" href="/docs/guides/data-ingestion"><h4 class="font-semibold mb-2">📊 Ingesta de Datos</h4><p class="text-sm text-gray-600 dark:text-gray-400">Aprende a enviar datos de productos y usuarios</p></a><a class="p-4 border rounded-lg hover:shadow-md transition-shadow" href="/docs/api/recommendations"><h4 class="font-semibold mb-2">🎯 API de Recomendaciones</h4><p class="text-sm text-gray-600 dark:text-gray-400">Referencia completa de la API</p></a><a class="p-4 border rounded-lg hover:shadow-md transition-shadow" href="/docs/guides/cold-start"><h4 class="font-semibold mb-2">🆕 Cold Start</h4><p class="text-sm text-gray-600 dark:text-gray-400">Manejo de usuarios y productos nuevos</p></a><a class="p-4 border rounded-lg hover:shadow-md transition-shadow" href="/docs/examples"><h4 class="font-semibold mb-2">💡 Ejemplos</h4><p class="text-sm text-gray-600 dark:text-gray-400">Casos de uso y ejemplos completos</p></a></div></div></div><div class="text-center"><p class="text-gray-600 dark:text-gray-400 mb-4">¿Necesitas ayuda? Estamos aquí para apoyarte.</p><div class="flex justify-center gap-4"><a class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-body-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border focus-visible:border-ring border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80 shadow-xs hover:shadow-soft rayuela-button-hover h-10 px-4 py-2" href="/docs"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-4 h-4 mr-2"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg>Ver más documentación</a><a class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border focus-visible:border-ring bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 shadow-xs hover:shadow-soft rayuela-button-hover h-10 px-4 py-2" href="/contact">Contactar Soporte</a></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></main></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section><script src="/_next/static/chunks/webpack-a18916eaa43bd3fa.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[3999,[\"9352\",\"static/chunks/9352-31b4412fb2722f24.js\",\"1445\",\"static/chunks/1445-21443766417aa7af.js\",\"5674\",\"static/chunks/5674-7748bf837df56152.js\",\"3753\",\"static/chunks/3753-51055f9a7954ec13.js\",\"2092\",\"static/chunks/2092-d6fb07d59d636626.js\",\"3999\",\"static/chunks/3999-62f798ec15041d09.js\",\"7177\",\"static/chunks/app/layout-71c41bba19c0ef97.js\"],\"AuthProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[6671,[\"9352\",\"static/chunks/9352-31b4412fb2722f24.js\",\"1445\",\"static/chunks/1445-21443766417aa7af.js\",\"5674\",\"static/chunks/5674-7748bf837df56152.js\",\"3753\",\"static/chunks/3753-51055f9a7954ec13.js\",\"2092\",\"static/chunks/2092-d6fb07d59d636626.js\",\"3999\",\"static/chunks/3999-62f798ec15041d09.js\",\"7177\",\"static/chunks/app/layout-71c41bba19c0ef97.js\"],\"Toaster\"]\n6:I[6874,[\"9352\",\"static/chunks/9352-31b4412fb2722f24.js\",\"6874\",\"static/chunks/6874-da306f8e1ba36f76.js\",\"940\",\"static/chunks/app/(public)/docs/quickstart/python/page-0e486aeb324c93f6.js\"],\"\"]\n7:I[285,[\"9352\",\"static/chunks/9352-31b4412fb2722f24.js\",\"6874\",\"static/chunks/6874-da306f8e1ba36f76.js\",\"940\",\"static/chunks/app/(public)/docs/quickstart/python/page-0e486aeb324c93f6.js\"],\"Button\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/74726d29afef2ab2.css\",\"style\"]\n:HL[\"/_next/static/css/554aa4ecb4bf38a8.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"pDXSyABSoCVQNQiYYq3id\",\"p\":\"\",\"c\":[\"\",\"docs\",\"quickstart\",\"python\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(public)\",{\"children\":[\"docs\",{\"children\":[\"quickstart\",{\"children\":[\"python\",{\"children\":[\"__PAGE__\",{}]}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/74726d29afef2ab2.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/554aa4ecb4bf38a8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"es\",\"className\":\"__variable_e8ce0c\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L5\",null,{\"position\":\"top-right\",\"richColors\":true}]]}]}]]}],{\"children\":[\"(public)\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"div\",null,{\"children\":[\"$\",\"main\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:0:props:children:props:notFound:0:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"docs\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"quickstart\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"python\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"APIReference\\\",\\\"name\\\":\\\"Rayuela Python Quickstart Guide\\\",\\\"description\\\":\\\"Complete guide to integrate Rayuela recommendation system with Python\\\",\\\"url\\\":\\\"https://rayuela.ai/docs/quickstart/python\\\",\\\"programmingLanguage\\\":\\\"Python\\\"}\"}}],[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50 dark:bg-gray-900\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-16 max-w-4xl\",\"children\":[[\"$\",\"nav\",null,{\"className\":\"mb-8 text-sm\",\"children\":[[\"$\",\"$L6\",null,{\"href\":\"/docs\",\"className\":\"text-blue-600 hover:text-blue-800\",\"children\":\"Documentación\"}],[\"$\",\"span\",null,{\"className\":\"mx-2 text-gray-500\",\"children\":\"/\"}],[\"$\",\"$L6\",null,{\"href\":\"/docs/quickstart\",\"className\":\"text-blue-600 hover:text-blue-800\",\"children\":\"Inicio Rápido\"}],[\"$\",\"span\",null,{\"className\":\"mx-2 text-gray-500\",\"children\":\"/\"}],[\"$\",\"span\",null,{\"className\":\"text-gray-700\",\"children\":\"Python\"}]]}],[\"$\",\"div\",null,{\"className\":\"mb-12\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-4xl font-bold text-gray-900 dark:text-white mb-4\",\"children\":\"🐍 Inicio Rápido con Python\"}],[\"$\",\"p\",null,{\"className\":\"text-xl text-gray-600 dark:text-gray-300\",\"children\":\"Integra Rayuela en tu aplicación Python en menos de 5 minutos\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-code w-5 h-5 mr-2\",\"children\":[[\"$\",\"polyline\",\"z7tu5w\",{\"points\":\"16 18 22 12 16 6\"}],[\"$\",\"polyline\",\"1eg1df\",{\"points\":\"8 6 2 12 8 18\"}],\"$undefined\"]}],\"Requisitos Previos\"]}]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300\",\"children\":[[\"$\",\"li\",null,{\"children\":\"Python 3.7 o superior\"}],[\"$\",\"li\",null,{\"children\":\"pip (gestor de paquetes de Python)\"}],[\"$\",\"li\",null,{\"children\":\"Una cuenta en Rayuela (gratis)\"}],[\"$\",\"li\",null,{\"children\":\"Tu API Key de Rayuela\"}]]}]}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent\",\"children\":\"1. Instalación\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Instala el SDK de Python para Rayuela\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-gray-900 rounded-lg p-4 mb-4\",\"children\":[\"$\",\"code\",null,{\"className\":\"text-green-400 text-sm\",\"children\":\"pip install rayuela-python\"}]}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 dark:text-gray-400\",\"children\":\"O si usas poetry:\"}],[\"$\",\"div\",null,{\"className\":\"bg-gray-900 rounded-lg p-4 mt-2\",\"children\":[\"$\",\"code\",null,{\"className\":\"text-green-400 text-sm\",\"children\":\"poetry add rayuela-python\"}]}]]}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent\",\"children\":\"2. Configuración\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Configura tu cliente de Rayuela\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-gray-900 rounded-lg p-4 mb-4\",\"children\":[\"$\",\"pre\",null,{\"className\":\"text-green-400 text-sm overflow-x-auto\",\"children\":\"from rayuela import RayuelaClient\\n\\n# Inicializa el cliente\\nclient = RayuelaClient(\\n    api_key=\\\"tu-api-key-aqui\\\",\\n    base_url=\\\"https://api.rayuela.ai\\\"  # Opcional\\n)\\n\\n# Verifica la conexión\\ntry:\\n    health = client.health_check()\\n    print(\\\"✅ Conexión exitosa:\\\", health)\\nexcept Exception as e:\\n    print(\\\"❌ Error de conexión:\\\", e)\"}]}],[\"$\",\"div\",null,{\"className\":\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm text-blue-800 dark:text-blue-200\",\"children\":[[\"$\",\"strong\",null,{\"children\":\"💡 Tip:\"}],\" Nunca hardcodees tu API key. Usa variables de entorno:\"]}],[\"$\",\"div\",null,{\"className\":\"bg-gray-900 rounded-lg p-2 mt-2\",\"children\":[\"$\",\"code\",null,{\"className\":\"text-green-400 text-xs\",\"children\":[\"import os\",[\"$\",\"br\",null,{}],\"api_key = os.getenv(\\\"RAYUELA_API_KEY\\\")\"]}]}]]}]]}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent\",\"children\":\"3. Tu Primera Recomendación\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Obtén recomendaciones para un usuario\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-gray-900 rounded-lg p-4 mb-4\",\"children\":[\"$\",\"pre\",null,{\"className\":\"text-green-400 text-sm overflow-x-auto\",\"children\":\"# Obtener recomendaciones para un usuario\\nrecommendations = client.get_recommendations(\\n    user_id=\\\"user_123\\\",\\n    limit=5,\\n    filters={\\n        \\\"category\\\": \\\"electronics\\\",\\n        \\\"price_max\\\": 1000\\n    }\\n)\\n\\nprint(\\\"Recomendaciones:\\\")\\nfor rec in recommendations:\\n    print(f\\\"- Producto {rec['product_id']}: {rec['score']:.2f}\\\")\"}]}],[\"$\",\"h4\",null,{\"className\":\"font-semibold mb-2\",\"children\":\"Respuesta esperada:\"}],[\"$\",\"div\",null,{\"className\":\"bg-gray-100 dark:bg-gray-800 rounded-lg p-4\",\"children\":[\"$\",\"pre\",null,{\"className\":\"text-sm text-gray-700 dark:text-gray-300\",\"children\":\"Recomendaciones:\\n- Producto prod_456: 0.95\\n- Producto prod_789: 0.87\\n- Producto prod_123: 0.82\\n- Producto prod_321: 0.78\\n- Producto prod_654: 0.75\"}]}]]}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent\",\"children\":\"4. Enviar Datos de Interacción\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-caption\",\"children\":\"Mejora las recomendaciones enviando datos de comportamiento\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"bg-gray-900 rounded-lg p-4 mb-4\",\"children\":[\"$\",\"pre\",null,{\"className\":\"text-green-400 text-sm overflow-x-auto\",\"children\":\"# Registrar una interacción\\nclient.track_interaction(\\n    user_id=\\\"user_123\\\",\\n    product_id=\\\"prod_456\\\",\\n    interaction_type=\\\"view\\\",\\n    timestamp=\\\"2024-01-15T10:30:00Z\\\"\\n)\\n\\n# Registrar una compra\\nclient.track_interaction(\\n    user_id=\\\"user_123\\\",\\n    product_id=\\\"prod_456\\\",\\n    interaction_type=\\\"purchase\\\",\\n    value=299.99,\\n    timestamp=\\\"2024-01-15T10:35:00Z\\\"\\n)\"}]}]}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-soft rayuela-card-gradient rayuela-card-hover transition-all duration-300 ease-in-out mb-8\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-subheading rayuela-accent\",\"children\":\"🚀 Próximos Pasos\"}]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 md:grid-cols-2 gap-4\",\"children\":[[\"$\",\"$L6\",null,{\"href\":\"/docs/guides/data-ingestion\",\"className\":\"p-4 border rounded-lg hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold mb-2\",\"children\":\"📊 Ingesta de Datos\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 dark:text-gray-400\",\"children\":\"Aprende a enviar datos de productos y usuarios\"}]]}],[\"$\",\"$L6\",null,{\"href\":\"/docs/api/recommendations\",\"className\":\"p-4 border rounded-lg hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold mb-2\",\"children\":\"🎯 API de Recomendaciones\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 dark:text-gray-400\",\"children\":\"Referencia completa de la API\"}]]}],[\"$\",\"$L6\",null,{\"href\":\"/docs/guides/cold-start\",\"className\":\"p-4 border rounded-lg hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold mb-2\",\"children\":\"🆕 Cold Start\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 dark:text-gray-400\",\"children\":\"Manejo de usuarios y productos nuevos\"}]]}],[\"$\",\"$L6\",null,{\"href\":\"/docs/examples\",\"className\":\"p-4 border rounded-lg hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold mb-2\",\"children\":\"💡 Ejemplos\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 dark:text-gray-400\",\"children\":\"Casos de uso y ejemplos completos\"}]]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"text-center\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-gray-600 dark:text-gray-400 mb-4\",\"children\":\"¿Necesitas ayuda? Estamos aquí para apoyarte.\"}],[\"$\",\"div\",null,{\"className\":\"flex justify-center gap-4\",\"children\":[[\"$\",\"$L7\",null,{\"variant\":\"outline\",\"asChild\":true,\"children\":[\"$\",\"$L6\",null,{\"href\":\"/docs\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-external-link w-4 h-4 mr-2\",\"children\":[[\"$\",\"path\",\"1q9fwt\",{\"d\":\"M15 3h6v6\"}],[\"$\",\"path\",\"gplh6r\",{\"d\":\"M10 14 21 3\"}],[\"$\",\"path\",\"a6xqqp\",{\"d\":\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"}],\"$undefined\"]}],\"Ver más documentación\"]}]}],[\"$\",\"$L7\",null,{\"asChild\":true,\"children\":[\"$\",\"$L6\",null,{\"href\":\"/contact\",\"children\":\"Contactar Soporte\"}]}]]}]]}]]}]}]],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"MOu8ZJjvbDjkSkbBoCgtP\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Inicio Rápido con Python - Documentación | Rayuela.ai\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Aprende a integrar Rayuela en tu aplicación Python en menos de 5 minutos. Guía paso a paso con ejemplos de código.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Rayuela Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"python, quickstart, tutorial, API, integración, SDK\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Rayuela\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"Rayuela\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"7\",{\"rel\":\"canonical\",\"href\":\"https://rayuela.ai/docs/quickstart/python\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:title\",\"content\":\"Inicio Rápido con Python - Documentación | Rayuela.ai\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:description\",\"content\":\"Aprende a integrar Rayuela en tu aplicación Python en menos de 5 minutos. Guía paso a paso con ejemplos de código.\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:url\",\"content\":\"https://rayuela.ai/docs/quickstart/python\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:site_name\",\"content\":\"Rayuela.ai\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:locale\",\"content\":\"es_AR\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:image\",\"content\":\"https://rayuela.ai/og-image.png\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:alt\",\"content\":\"Inicio Rápido con Python - Documentación\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:creator\",\"content\":\"@rayuela_ai\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:title\",\"content\":\"Inicio Rápido con Python - Documentación | Rayuela.ai\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:description\",\"content\":\"Aprende a integrar Rayuela en tu aplicación Python en menos de 5 minutos. Guía paso a paso con ejemplos de código.\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:image\",\"content\":\"https://rayuela.ai/og-image.png\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"e:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>