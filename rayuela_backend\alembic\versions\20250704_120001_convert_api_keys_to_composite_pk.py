"""Convert api_keys primary key to composite (account_id, id)

Revision ID: 20250704_120001
Revises: 20250704_120000
Create Date: 2025-07-04 12:10:00
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250704_120001"
down_revision: Union[str, None] = "20250704_120000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _constraint_exists(name: str, table: str) -> bool:
    conn = op.get_bind()
    res = conn.execute(
        text(
            """
        SELECT constraint_name FROM information_schema.table_constraints
        WHERE table_name = :table AND constraint_name = :name AND table_schema = 'public'
        """
        ),
        {"table": table, "name": name},
    ).fetchone()
    return res is not None


def upgrade() -> None:
    # 1. Drop existing PK if present
    if _constraint_exists("api_keys_pkey", "api_keys"):
        op.drop_constraint("api_keys_pkey", "api_keys", type_="primary")

    # 2. Ensure account_id column is NOT NULL (should already be)
    op.alter_column("api_keys", "account_id", existing_type=sa.Integer(), nullable=False)

    # 3. Create new composite PK
    op.create_primary_key("api_keys_pkey", "api_keys", ["account_id", "id"])


def downgrade() -> None:
    op.drop_constraint("api_keys_pkey", "api_keys", type_="primary")
    op.create_primary_key("api_keys_pkey", "api_keys", ["id"]) 