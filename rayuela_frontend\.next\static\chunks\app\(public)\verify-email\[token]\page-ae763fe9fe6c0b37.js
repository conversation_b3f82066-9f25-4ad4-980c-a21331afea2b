(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9014],{1154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2509:(e,t,a)=>{Promise.resolve().then(a.bind(a,4128))},3453:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},4128:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(5155),s=a(2115),i=a(5695),c=a(6695),n=a(285),l=a(5731),o=a(6671),d=a(1154),u=a(3453),m=a(4861);function y(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),[a,y]=(0,s.useState)(!0),[h,f]=(0,s.useState)(!1),[p,x]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{if(!e.token){y(!1),x("Token de verificaci\xf3n no proporcionado.");return}try{let t=Array.isArray(e.token)?e.token[0]:e.token;await (0,l.A$)(t),f(!0),o.o.success("Email verificado correctamente.")}catch(e){console.error("Error al verificar email:",e),x(e.message||"Error al verificar el email. El token podr\xeda ser inv\xe1lido o haber expirado."),o.o.error("Error al verificar el email.")}finally{y(!1)}})()},[e.token]),(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gray-100",children:(0,r.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Verificaci\xf3n de Email"}),(0,r.jsx)(c.BT,{children:a?"Verificando tu direcci\xf3n de email...":h?"Tu email ha sido verificado correctamente.":"Error en la verificaci\xf3n de email."})]}),(0,r.jsx)(c.Wu,{className:"flex flex-col items-center justify-center py-6",children:a?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(d.A,{className:"h-16 w-16 text-blue-500 animate-spin mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Estamos verificando tu email..."})]}):h?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(u.A,{className:"h-16 w-16 text-green-500 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"Tu email ha sido verificado correctamente. Ahora puedes iniciar sesi\xf3n en tu cuenta."})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(m.A,{className:"h-16 w-16 text-red-500 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 text-center",children:p||"Ha ocurrido un error al verificar tu email."})]})}),(0,r.jsx)(c.wL,{className:"flex justify-center",children:!a&&(0,r.jsx)(n.Button,{onClick:()=>t.push("/login"),className:"w-full max-w-xs",children:h?"Ir a iniciar sesi\xf3n":"Volver a intentar"})})]})})}},4861:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5731:(e,t,a)=>{"use strict";a.d(t,{A$:()=>u,DY:()=>l,Dm:()=>y,Hl:()=>d,Iq:()=>g,Lx:()=>n,M2:()=>p,PX:()=>A,S3:()=>x,T9:()=>f,XW:()=>w,_:()=>h,fw:()=>j,hD:()=>s,jp:()=>m,mA:()=>v,oE:()=>k,ri:()=>o});var r=a(2656);class s extends Error{constructor(e,t=500,a){super(e),this.name="ApiError",this.status=t,this.body=a}}function i(e,t){if(e&&"object"==typeof e&&"response"in e){var a,r;let i=(null==(a=e.response)?void 0:a.status)||500;throw new s(e.message||t,i,null==(r=e.response)?void 0:r.data)}if(e instanceof Error)throw new s(e.message,500);throw new s(t,500)}let c=(0,r._C)(),n=async e=>{try{return await c.loginApiV1AuthTokenPost(e)}catch(e){i(e,"Login failed")}},l=async(e,t,a)=>{try{return await c.registerApiV1AuthRegisterPost({accountName:e,email:t,password:a})}catch(e){i(e,"Registration failed")}},o=async()=>c.logoutApiV1AuthLogoutPost(),d=async()=>c.sendVerificationEmailApiV1AuthSendVerificationEmailPost(),u=async e=>c.verifyEmailApiV1AuthVerifyEmailGet({token:e}),m=async()=>{try{return await c.getCurrentUserInfoApiV1SystemUsersMeGet()}catch(e){i(e,"Failed to get current user")}},y=async()=>{try{return await c.getAccountInfoApiV1AccountsCurrentGet()}catch(e){i(e,"Failed to get current account")}},h=y,f=async()=>{try{return await c.getAvailablePlansApiV1PlansGet()}catch(e){i(e,"Failed to get plans")}},p=async(e,t)=>{let a={};return e&&(a.start_date=e),t&&(a.end_date=t),c.getUsageHistoryApiV1UsageHistoryGet(a)},x=async()=>{try{return await c.getUsageSummaryApiV1UsageSummaryGet()}catch(e){i(e,"Failed to get usage summary")}},A=async()=>{try{return await c.listApiKeysApiV1ApiKeysGet()}catch(e){i(e,"Failed to get API keys")}},g=async e=>{try{let t={name:e.name};return await c.createApiKeyApiV1ApiKeysPost(t)}catch(e){i(e,"Failed to create API key")}},w=async(e,t)=>{try{let a={name:t.name};return await c.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(e),a)}catch(e){i(e,"Failed to update API key")}},v=async e=>c.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(e),j=async e=>c.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost({price_id:e}),k=async()=>c.createPortalSessionApiV1BillingCreatePortalSessionPost({})},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>c,wL:()=>d});var r=a(5155);a(2115);var s=a(9434);function i(e){var t;let{className:a,elevation:i="soft",...c}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border",null!=(t=({none:"shadow-none",sm:"shadow-sm",soft:"shadow-soft",medium:"shadow-medium",glow:"shadow-glow"})[i])?t:"shadow-soft","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",a),...c})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("text-subheading rayuela-accent",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-caption",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9352,1445,3753,2092,8441,1684,7358],()=>t(2509)),_N_E=e.O()}]);