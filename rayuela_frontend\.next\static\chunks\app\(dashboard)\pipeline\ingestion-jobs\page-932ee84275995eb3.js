(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9747],{1788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2620:(e,s,t)=>{Promise.resolve().then(t.bind(t,3160))},3160:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>F});var a=t(5155),r=t(2115),i=t(6695),l=t(285),n=t(8856),c=t(5127),d=t(5365),o=t(9869),x=t(4213),m=t(5339),h=t(6932),u=t(7924),j=t(2657),p=t(1788),g=t(133),v=t(3008),N=t(3439),f=t(2523),b=t(5057),_=t(9409),y=t(4165),w=t(5947),A=t(4113),S=t(646);let C=(0,t(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var E=t(1154);function k(e){let{onIngestionStart:s,trigger:t}=e,[i,n]=(0,r.useState)(!1),[c,x]=(0,r.useState)("batch"),[h,u]=(0,r.useState)(null),[j,p]=(0,r.useState)(!1),[g,v]=(0,r.useState)(null),[N,w]=(0,r.useState)(!1),A=async()=>{if(!h)return void v("Por favor selecciona un archivo");p(!0),v(null);try{let e,t=await k(h);try{e=h.name.toLowerCase().endsWith(".json")?JSON.parse(t):h.name.toLowerCase().endsWith(".csv")?await I(t,c):JSON.parse(t)}catch(e){throw Error("Error al parsear el archivo. Aseg\xfarate de que el formato sea v\xe1lido.")}let a={data_type:c,file_name:h.name,file_size:h.size,...e},r=await s(a);console.log("Ingestion started:",r),w(!0),setTimeout(()=>{n(!1),w(!1),u(null),x("batch")},2e3)}catch(e){v(e instanceof Error?e.message:"Error iniciando ingesta de datos")}finally{p(!1)}},k=e=>new Promise((s,t)=>{let a=new FileReader;a.onload=e=>{var t;return s(null==(t=e.target)?void 0:t.result)},a.onerror=()=>t(Error("Error reading file")),a.readAsText(e)}),I=async(e,s)=>{let t=e.trim().split("\n"),a=t[0].split(",").map(e=>e.trim()),r=[];for(let e=1;e<t.length;e++){let s=t[e].split(",").map(e=>e.trim()),i={};a.forEach((e,t)=>{i[e]=s[t]}),r.push(i)}switch(s){case"users":return{users:r};case"products":return{products:r};case"interactions":return{interactions:r};case"batch":return{users:r.filter(e=>e.external_id&&!e.product_id),products:r.filter(e=>e.external_id&&e.name&&!e.end_user_external_id),interactions:r.filter(e=>e.end_user_external_id&&e.product_external_id)};default:return{data:r}}};return(0,a.jsxs)(y.lG,{open:i,onOpenChange:n,children:[(0,a.jsx)(y.zM,{asChild:!0,children:t||(0,a.jsxs)(l.Button,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})}),(0,a.jsxs)(y.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(y.c7,{children:[(0,a.jsx)(y.L3,{children:"Nueva Ingesta de Datos"}),(0,a.jsx)(y.rr,{children:"Sube un archivo CSV o JSON con tus datos de usuarios, productos o interacciones"})]}),N?(0,a.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,a.jsx)(S.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Ingesta iniciada!"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu archivo est\xe1 siendo procesado"})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(b.J,{htmlFor:"dataType",children:"Tipo de datos"}),(0,a.jsxs)(_.l6,{value:c,onValueChange:e=>x(e),children:[(0,a.jsx)(_.bq,{children:(0,a.jsx)(_.yv,{placeholder:"Selecciona el tipo de datos"})}),(0,a.jsxs)(_.gC,{children:[(0,a.jsx)(_.eb,{value:"batch",children:"Lote completo (usuarios, productos, interacciones)"}),(0,a.jsx)(_.eb,{value:"users",children:"Solo usuarios"}),(0,a.jsx)(_.eb,{value:"products",children:"Solo productos"}),(0,a.jsx)(_.eb,{value:"interactions",children:"Solo interacciones"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(b.J,{htmlFor:"file",children:"Archivo"}),(0,a.jsx)(f.p,{id:"file",type:"file",accept:".csv,.json,.txt",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){let e=t.name.toLowerCase().substr(t.name.lastIndexOf("."));if(!["text/csv","application/json","text/plain"].includes(t.type)&&![".csv",".json",".txt"].includes(e))return void v("Por favor selecciona un archivo CSV o JSON v\xe1lido");if(t.size>0xa00000)return void v("El archivo es demasiado grande. M\xe1ximo 10MB permitido");u(t),v(null)}},disabled:j}),h&&(0,a.jsxs)("div",{className:"mt-2 flex items-center text-sm text-muted-foreground",children:[(0,a.jsx)(C,{className:"h-4 w-4 mr-2"}),(0,a.jsxs)("span",{children:[h.name," (",(h.size/1024).toFixed(1)," KB)"]})]})]}),g&&(0,a.jsxs)(d.Fc,{variant:"destructive",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)(d.TN,{children:g})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,a.jsx)("p",{children:"Formatos soportados: CSV, JSON"}),(0,a.jsx)("p",{children:"Tama\xf1o m\xe1ximo: 10MB"})]})]}),(0,a.jsxs)(y.Es,{children:[(0,a.jsx)(l.Button,{variant:"outline",onClick:()=>{j||(n(!1),u(null),v(null),w(!1),x("batch"))},disabled:j,children:"Cancelar"}),!N&&(0,a.jsx)(l.Button,{onClick:A,disabled:!h||j,children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Subiendo..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Iniciar Ingesta"]})})]})]})]})}var I=t(8338),L=t(7271);function F(){let{jobs:e,isLoading:s,error:t,startBatchIngestion:S}=(0,w.o)(),[C,E]=(0,r.useState)(null),{filteredJobs:F,searchQuery:T,setSearchQuery:z,statusFilter:J,setStatusFilter:P,clearFilters:M}=(0,A.x)(e,(e,s)=>{var t,a;return e.job_id.toString().includes(s)||null!=(a=null==(t=e.file_path)?void 0:t.toLowerCase().includes(s.toLowerCase()))&&a}),B=e=>(0,I.z3)(e),D=e=>"FAILED"===e.status,O=e=>{console.log("Retrying job:",e)},V=e=>{console.log("Downloading file:",e)};return s?(0,a.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,a.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,a.jsx)(n.E,{className:"h-8 w-64 mb-2"}),(0,a.jsx)(n.E,{className:"h-4 w-96"})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(n.E,{className:"h-6 w-48"}),(0,a.jsx)(n.E,{className:"h-4 w-32"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(n.E,{className:"h-64 w-full"})})]})]}):(0,a.jsxs)(L.hI,{title:"Historial de Ingesta de Datos",description:"Seguimiento completo de todos tus procesos de carga de datos",actions:(0,a.jsx)(k,{onIngestionStart:S,trigger:(0,a.jsxs)(l.Button,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})}),children:[(0,a.jsx)(L.os,{title:"Resumen",icon:(0,a.jsx)(x.A,{className:"h-6 w-6 text-green-500"}),children:(0,a.jsxs)("div",{className:"flex gap-4 text-sm text-muted-foreground p-6",children:[(0,a.jsxs)("span",{children:["Total: ",e.length]}),(0,a.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,a.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,a.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})}),t&&(0,a.jsxs)(d.Fc,{variant:"destructive",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)(d.XL,{children:"Error"}),(0,a.jsx)(d.TN,{children:t})]}),(0,a.jsx)(L.os,{title:"Filtros",icon:(0,a.jsx)(h.A,{className:"h-5 w-5"}),children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col gap-6 sm:flex-row sm:items-center sm:gap-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(f.p,{placeholder:"Buscar por ID o archivo...",value:T,onChange:e=>z(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(b.J,{htmlFor:"statusFilter",className:"text-sm whitespace-nowrap",children:"Estado:"}),(0,a.jsxs)(_.l6,{value:J,onValueChange:e=>P(e),children:[(0,a.jsx)(_.bq,{className:"w-32",children:(0,a.jsx)(_.yv,{})}),(0,a.jsxs)(_.gC,{children:[(0,a.jsx)(_.eb,{value:"all",children:"Todos"}),(0,a.jsx)(_.eb,{value:"pending",children:"Pendiente"}),(0,a.jsx)(_.eb,{value:"processing",children:"Procesando"}),(0,a.jsx)(_.eb,{value:"completed",children:"Completado"}),(0,a.jsx)(_.eb,{value:"failed",children:"Fallido"})]})]})]}),(0,a.jsx)(l.Button,{variant:"outline",size:"sm",onClick:M,children:"Limpiar"})]})})}),(0,a.jsx)(L.os,{title:"Trabajos de Ingesta",description:"Lista completa de procesos de carga de datos con detalles y estad\xedsticas",children:(0,a.jsx)(i.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-hidden",children:(0,a.jsxs)(c.XI,{children:[(0,a.jsx)(c.A0,{className:"bg-muted/10",children:(0,a.jsxs)(c.Hj,{className:"border-b border-border/30",children:[(0,a.jsx)(c.nd,{className:"font-semibold",children:"Job ID"}),(0,a.jsx)(c.nd,{className:"font-semibold",children:"Estado"}),(0,a.jsx)(c.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,a.jsx)(c.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,a.jsx)(c.nd,{className:"font-semibold",children:"Registros Procesados"}),(0,a.jsx)(c.nd,{className:"font-semibold",children:"Archivo"}),(0,a.jsx)(c.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,a.jsx)(c.BF,{children:F.length>0?F.map((e,s)=>{var t;return(0,a.jsxs)(L.AP,{index:s,children:[(0,a.jsxs)(c.nA,{className:"font-medium py-4",children:["#",e.job_id]}),(0,a.jsx)(c.nA,{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,I.cR)(e.status),(0,I.KC)(e.status)]})}),(0,a.jsx)(c.nA,{className:"py-4",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{children:(0,v.GP)(new Date(e.created_at),"dd/MM/yyyy",{locale:N.es})}),(0,a.jsx)("div",{className:"text-muted-foreground",children:(0,v.GP)(new Date(e.created_at),"HH:mm",{locale:N.es})})]})}),(0,a.jsx)(c.nA,{className:"py-4",children:e.duration?(0,a.jsx)("span",{className:"text-sm font-medium",children:(0,I.a3)(e.duration)}):"PROCESSING"===e.status?(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"En curso"}):(0,a.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,a.jsx)(c.nA,{className:"py-4",children:e.records_processed?(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"font-medium",children:["Total: ",(null==(t=e.records_processed.total)?void 0:t.toLocaleString())||"—"]}),(0,a.jsxs)("div",{className:"text-muted-foreground text-xs",children:[e.records_processed.users&&"".concat(e.records_processed.users.toLocaleString()," usuarios"),e.records_processed.products&&", ".concat(e.records_processed.products.toLocaleString()," productos"),e.records_processed.interactions&&", ".concat(e.records_processed.interactions.toLocaleString()," interacciones")]})]}):(0,a.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,a.jsx)(c.nA,{className:"py-4",children:e.file_path?(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-medium truncate max-w-32",title:e.file_path,children:e.file_path.split("/").pop()}),e.file_size&&(0,a.jsx)("div",{className:"text-muted-foreground text-xs",children:B(e.file_size)})]}):(0,a.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,a.jsx)(c.nA,{className:"text-right py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,a.jsxs)(y.lG,{children:[(0,a.jsx)(y.zM,{asChild:!0,children:(0,a.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>E(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(y.Cf,{className:"max-w-2xl",children:[(0,a.jsxs)(y.c7,{children:[(0,a.jsxs)(y.L3,{children:["Detalles del Job #",e.job_id]}),(0,a.jsx)(y.rr,{children:"Informaci\xf3n completa del trabajo de ingesta de datos"})]}),C&&(0,a.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(b.J,{className:"text-sm font-medium",children:"Estado"}),(0,a.jsx)("div",{className:"mt-1",children:(0,I.KC)(C.status)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(b.J,{className:"text-sm font-medium",children:"Duraci\xf3n"}),(0,a.jsx)("p",{className:"text-sm",children:C.duration?(0,I.a3)(C.duration):"En curso"})]})]}),C.file_path&&(0,a.jsxs)("div",{children:[(0,a.jsx)(b.J,{className:"text-sm font-medium",children:"Archivo"}),(0,a.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,a.jsx)("code",{className:"text-xs bg-muted p-2 rounded flex-1",children:C.file_path}),(0,a.jsx)(l.Button,{size:"sm",variant:"outline",onClick:()=>V(C.file_path),disabled:!0,children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})]}),C.file_size&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Tama\xf1o: ",B(C.file_size)]})]}),C.records_processed&&(0,a.jsxs)("div",{children:[(0,a.jsx)(b.J,{className:"text-sm font-medium",children:"Registros Procesados"}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-2 mt-2",children:[C.records_processed.users&&(0,a.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs font-medium",children:"Usuarios"}),(0,a.jsx)("div",{className:"text-sm font-bold",children:C.records_processed.users.toLocaleString()})]}),C.records_processed.products&&(0,a.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs font-medium",children:"Productos"}),(0,a.jsx)("div",{className:"text-sm font-bold",children:C.records_processed.products.toLocaleString()})]}),C.records_processed.interactions&&(0,a.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs font-medium",children:"Interacciones"}),(0,a.jsx)("div",{className:"text-sm font-bold",children:C.records_processed.interactions.toLocaleString()})]}),C.records_processed.total&&(0,a.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs font-medium",children:"Total"}),(0,a.jsx)("div",{className:"text-sm font-bold",children:C.records_processed.total.toLocaleString()})]})]})]}),C.error_message&&(0,a.jsxs)("div",{children:[(0,a.jsx)(b.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,a.jsxs)(d.Fc,{variant:"destructive",className:"mt-1",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)(d.TN,{className:"text-sm",children:C.error_message})]})]}),C.task_id&&(0,a.jsxs)("div",{children:[(0,a.jsx)(b.J,{className:"text-sm font-medium",children:"Task ID"}),(0,a.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:C.task_id})]})]})]})]}),D(e)&&(0,a.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>O(e.job_id),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})})]},e.job_id)}):(0,a.jsx)(L.AP,{index:0,children:(0,a.jsx)(c.nA,{colSpan:7,className:"text-center py-8",children:(0,a.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-8 w-8"}),(0,a.jsx)("p",{children:"No hay trabajos de ingesta a\xfan"}),(0,a.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando subas datos"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"h-8 w-8"}),(0,a.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,a.jsx)(l.Button,{variant:"outline",size:"sm",onClick:M,children:"Limpiar filtros"})]})})})})})]})})})}),(0,a.jsxs)(d.Fc,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)(d.XL,{children:"Informaci\xf3n sobre ingesta de datos"}),(0,a.jsx)(d.TN,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,a.jsx)("p",{children:"Los trabajos de ingesta procesan archivos de datos (CSV, JSON) para actualizar usuarios, productos e interacciones en el sistema."}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Formatos soportados:"})," CSV, JSON"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Tipos de datos:"})," Usuarios, Productos, Interacciones"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Validaci\xf3n:"})," Se valida formato y campos obligatorios"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Procesamiento:"})," Los datos se procesan de forma as\xedncrona"]})]})]})})]})]})}},4213:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},5947:(e,s,t)=>{"use strict";t.d(s,{o:()=>i});var a=t(2115),r=t(2656);function i(){let[e,s]=(0,a.useState)([]),[t,i]=(0,a.useState)(!0),[l,n]=(0,a.useState)(null),c=async()=>{try{i(!0),n(null);try{let e=[].map(e=>{let s={...e,status:e.status.toUpperCase(),records_processed:e.processed_count?{users:e.processed_count.users,products:e.processed_count.products,interactions:e.processed_count.interactions,total:e.processed_count.total}:void 0};if(s.started_at&&s.completed_at){let e=new Date(s.started_at).getTime(),t=new Date(s.completed_at).getTime();s.duration=Math.round((t-e)/1e3)}return s});s(e);return}catch(e){n("Error fetching ingestion jobs"),console.error("Error fetching ingestion jobs:",e)}}catch(e){n(e instanceof Error?e.message:"Error loading ingestion jobs"),console.error("Error loading ingestion jobs:",e)}finally{i(!1)}},d=async e=>{try{return(await (0,r._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data}catch(e){throw console.error("Error fetching job status:",e),e}},o=async e=>{try{let s=await (0,r._C)().batchDataIngestionApiV1IngestionBatchPost(e);return await c(),s.data}catch(e){throw console.error("Error starting batch ingestion:",e),e}};return(0,a.useEffect)(()=>{c()},[]),{jobs:e,isLoading:t,error:l,fetchJobs:c,getJobStatus:d,startBatchIngestion:o}}},9869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[9352,1445,5674,4214,3843,8034,5813,2092,4485,2971,8441,1684,7358],()=>s(2620)),_N_E=e.O()}]);