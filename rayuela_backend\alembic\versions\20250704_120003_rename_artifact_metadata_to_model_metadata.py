"""Rename artifact_metadata table to model_metadata and add compatibility view

Revision ID: 20250704_120003
Revises: 20250704_120002
Create Date: 2025-07-04 12:30:00
"""

from alembic import op
import sqlalchemy as sa
from typing import Sequence, Union

revision: str = "20250704_120003"
down_revision: Union[str, None] = "20250704_120002"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Rename table
    op.rename_table("artifact_metadata", "model_metadata")

    # Optional: create a compatibility view so legacy SQL still works
    op.execute(
        """
        CREATE OR REPLACE VIEW artifact_metadata AS SELECT * FROM model_metadata;
        """
    )


def downgrade() -> None:
    # Drop compatibility view first
    op.execute("DROP VIEW IF EXISTS artifact_metadata;")

    # Rename table back
    op.rename_table("model_metadata", "artifact_metadata") 