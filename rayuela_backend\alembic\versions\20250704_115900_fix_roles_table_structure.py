"""Fix roles table structure

Revision ID: 20250704_115900
Revises: 20250704_115000
Create Date: 2025-07-04 11:59:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = '20250704_115900'
down_revision: Union[str, None] = '20250704_115000'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _constraint_exists(constraint_name: str, table_name: str) -> bool:
    """Check if a constraint exists."""
    connection = op.get_bind()
    result = connection.execute(
        text("""
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = :constraint_name 
        AND table_name = :table_name
        """),
        {"constraint_name": constraint_name, "table_name": table_name}
    )
    return result.fetchone() is not None


def _column_exists(table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table."""
    connection = op.get_bind()
    result = connection.execute(
        text("""
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = :table_name 
        AND column_name = :column_name
        """),
        {"table_name": table_name, "column_name": column_name}
    )
    return result.fetchone() is not None


def upgrade() -> None:
    """Fix roles table structure to match SQLAlchemy model."""
    
    print("Fixing roles table structure...")
    
    # Step 1: Drop existing foreign key constraints that reference roles
    print("  Dropping foreign key constraints that reference roles...")
    
    # Drop foreign key from system_user_roles to roles if it exists
    if _constraint_exists("fk_system_user_role_role", "system_user_roles"):
        op.drop_constraint("fk_system_user_role_role", "system_user_roles", type_="foreignkey")
    
    # Step 2: Drop existing primary key constraint from roles
    print("  Dropping existing primary key constraint from roles...")
    if _constraint_exists("roles_pkey", "roles"):
        op.drop_constraint("roles_pkey", "roles", type_="primary")
    
    # Step 3: Rename roles.role_id to roles.id
    print("  Renaming roles.role_id to roles.id...")
    if _column_exists("roles", "role_id"):
        op.alter_column("roles", "role_id", new_column_name="id")
        print("    Column renamed successfully")
    else:
        print("    Column role_id does not exist, skipping rename")
    
    # Step 4: Create composite primary key for roles (account_id, id)
    print("  Creating composite primary key for roles (account_id, id)...")
    op.create_primary_key("roles_pkey", "roles", ["account_id", "id"])
    print("    Composite primary key created successfully")
    
    print("Roles table structure fixed successfully!")


def downgrade() -> None:
    """Revert roles table structure changes."""
    
    print("Reverting roles table structure...")
    
    # Step 1: Drop composite primary key
    print("  Dropping composite primary key...")
    if _constraint_exists("roles_pkey", "roles"):
        op.drop_constraint("roles_pkey", "roles", type_="primary")
    
    # Step 2: Rename id back to role_id
    print("  Renaming roles.id back to roles.role_id...")
    if _column_exists("roles", "id"):
        op.alter_column("roles", "id", new_column_name="role_id")
    
    # Step 3: Create original primary key
    print("  Creating original primary key...")
    op.create_primary_key("roles_pkey", "roles", ["role_id"])
    
    print("Roles table structure reverted successfully!")
