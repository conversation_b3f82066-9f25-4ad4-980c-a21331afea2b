"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7571],{467:(e,t,r)=>{r.d(t,{K:()=>l,S:()=>o});var a=r(2656),n=r(5731);let i=(0,a._C)();async function o(e,t){try{let r={};return e&&(r.model_id=e),t&&(r.metric_type=t),await i.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(r)}catch(a){let e=a instanceof Error?a.message:"Error al obtener m\xe9tricas de rendimiento de recomendaciones",t=a.status||500,r=a.body;throw new n.hD(e,t,r)}}async function l(){try{return await i.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet()}catch(a){let e=a instanceof Error?a.message:"Error al obtener m\xe9tricas de confianza",t=a.status||500,r=a.body;throw new n.hD(e,t,r)}}},849:(e,t,r)=>{r.d(t,{G:()=>i});var a=r(2115),n=r(2656);function i(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[o,l]=(0,a.useState)(null),s=async()=>{try{i(!0),l(null);let e=(await (0,n._C)().listModelsApiV1PipelineModelsGet()).data.map(e=>({id:e.id,artifact_name:e.artifact_name,artifact_version:e.artifact_version,description:e.description||void 0,training_date:e.training_date,performance_metrics:e.performance_metrics||void 0,parameters:e.parameters||void 0}));t(e)}catch(e){l(e instanceof Error?e.message:"Error loading models"),console.error("Error loading models:",e),t([])}finally{i(!1)}},c=async e=>{try{return(await (0,n._C)().getModelMetricsApiV1PipelineModelsModelIdMetricsGet(e)).data}catch(e){throw console.error("Error fetching model metrics:",e),e}};return(0,a.useEffect)(()=>{s()},[]),{models:e,isLoading:r,error:o,fetchModels:s,getModelMetrics:c}}},3365:(e,t,r)=>{r.d(t,{G:()=>i});var a=r(2115),n=r(2656);function i(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[o,l]=(0,a.useState)(null),s=async()=>{try{i(!0),l(null);try{let e=(await (0,n._C)().listTrainingJobsApiV1PipelineJobsGet()).data.map(e=>{var t,r,a,n;let i={...e,model_name:null!=(a=null==(t=e.model)?void 0:t.artifact_name)?a:"Recommendation Model",model_version:null!=(n=null==(r=e.model)?void 0:r.artifact_version)?n:"v1.0",status:e.status.toUpperCase(),parameters:e.parameters?Object.fromEntries(Object.entries(e.parameters).filter(e=>{let[,t]=e;return"number"==typeof t||"string"==typeof t})):void 0,metrics:e.metrics?Object.fromEntries(Object.entries(e.metrics).filter(e=>{let[,t]=e;return"number"==typeof t})):void 0};if(i.started_at&&i.completed_at){let e=new Date(i.started_at).getTime(),t=new Date(i.completed_at).getTime();i.duration=Math.round((t-e)/1e3)}return i});t(e);return}catch(e){l("Error fetching training jobs"),console.error("Error fetching training jobs:",e)}}catch(e){l(e instanceof Error?e.message:"Error loading training jobs"),console.error("Error loading training jobs:",e)}finally{i(!1)}},c=async e=>{try{return(await (0,n._C)().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(e)).data}catch(e){throw console.error("Error fetching training job status:",e),e}},u=async e=>{try{let e=await (0,n._C)().trainModelsApiV1PipelineTrainPost();return await s(),e.data}catch(e){throw console.error("Error starting training:",e),e}};return(0,a.useEffect)(()=>{s()},[]),{jobs:e,isLoading:r,error:o,fetchJobs:s,getJobStatus:c,startTraining:u}}},4113:(e,t,r)=>{r.d(t,{x:()=>n});var a=r(2115);function n(e,t){let[r,n]=(0,a.useState)(""),[i,o]=(0,a.useState)("all");return{filteredJobs:(0,a.useMemo)(()=>e.filter(e=>{let a="all"===i||e.status.toLowerCase()===i,n=""===r||t(e,r);return a&&n}),[e,i,r,t]),searchQuery:r,setSearchQuery:n,statusFilter:i,setStatusFilter:o,clearFilters:()=>{n(""),o("all")}}}},4556:(e,t,r)=>{r.d(t,{Q:()=>o});var a=r(6072),n=r(5731),i=r(2115);function o(){var e,t,r;let o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[l,s]=(0,i.useState)(!1),[c,u]=(0,i.useState)(!1),[d,g]=(0,i.useState)(!1),[p,f]=(0,i.useState)(!1),[v,m]=(0,i.useState)(null),{data:h,error:b,isLoading:y,isValidating:w,mutate:A}=(0,a.Ay)("api-keys",async()=>await (0,n.PX)(),{revalidateOnFocus:null==(e=o.revalidateOnFocus)||e,refreshInterval:o.refreshInterval,dedupingInterval:null!=(t=o.dedupingInterval)?t:6e4,errorRetryCount:null!=(r=o.errorRetryCount)?r:3,onError:e=>{console.error("Error fetching API keys:",e)}}),_=(null==h?void 0:h.api_keys)&&h.api_keys.length>0?h.api_keys.find(e=>e.is_active)||h.api_keys[0]:null,x=async e=>{s(!0),m(null);try{let t={name:e.name||"",permissions:[]},r=await (0,n.Iq)(t);return await A(),r}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al crear API Key",500);throw m(e),e}finally{s(!1)}},C=async(e,t)=>{u(!0),m(null);try{let r={name:t.name||void 0,permissions:[]},a=await (0,n.XW)(e.toString(),r);return await A(),a}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al actualizar API Key",500);throw m(e),e}finally{u(!1)}},S=async e=>{g(!0),m(null);try{return await (0,n.mA)(e),await A(),!0}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al revocar API Key",500);throw m(e),e}finally{g(!1)}},E=async()=>{f(!0),m(null);try{let e=await (0,n.Iq)({name:"API Key ".concat(new Date().toLocaleDateString("es-ES"))});return await A(),e}catch(e){return m(e instanceof n.hD?e:new n.hD("Error al regenerar API Key",500)),null}finally{f(!1)}};return{data:null!=h?h:null,primaryKey:null!=_?_:null,error:null!=b?b:null,isLoading:y,isValidating:w,mutate:A,dataUpdatedAt:0,createApiKey:x,updateApiKey:C,revokeApiKey:S,regenerateApiKey:E,isCreating:l,isUpdating:c,isRevoking:d,isRegenerating:p,operationError:v,getFormattedApiKey:e=>{let t=e||_;return(null==t?void 0:t.prefix)&&(null==t?void 0:t.last_chars)?"".concat(t.prefix,"••••••••").concat(t.last_chars):null}}}},5379:(e,t,r)=>{r.d(t,{T4:()=>l,Qf:()=>s.Q,As:()=>i.A,A$:()=>g,Xn:()=>d,TB:()=>c}),r(3365),r(5947),r(849);var a=r(6072),n=r(5731),i=r(3999),o=r(2656);function l(){var e,t,r;let l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{token:s,apiKey:c}=(0,i.A)(),{data:u,error:d,isLoading:g,isValidating:p,mutate:f}=(0,a.Ay)(s&&c?["account-info",s,c]:null,async()=>await (0,n.Dm)(),{revalidateOnFocus:null!=(e=l.revalidateOnFocus)&&e,refreshInterval:l.refreshInterval,dedupingInterval:null!=(t=l.dedupingInterval)?t:6e4,errorRetryCount:null!=(r=l.errorRetryCount)?r:3}),v=async e=>{if(!s||!c)throw Error("No token or API key available");try{let t=(await (0,o._C)().patchCurrentAccountApiV1AccountsCurrentPatch({onboardingChecklistStatus:e})).data;return await f(t,{revalidate:!1}),t}catch(r){console.error("Error updating checklist status:",r);let t=u?{...u,onboardingChecklistStatus:e}:void 0;throw await f(t,{revalidate:!1}),r}};return{accountData:u,error:d,isLoading:g,isValidating:p,refresh:f,lastUpdated:null,getCreationDate:()=>u&&u.createdAt?new Date(u.createdAt):null,isActive:()=>!!u&&u.isActive,getSubscriptionPlan:()=>u&&u.subscription?u.subscription.plan:null,isSubscriptionActive:()=>!!u&&!!u.subscription&&u.subscription.isActive,getSubscriptionExpiryDate:()=>u&&u.subscription&&u.subscription.expiresAt?new Date(u.subscription.expiresAt):null,getChecklistStatus:()=>u&&u.onboardingChecklistStatus?u.onboardingChecklistStatus:{},updateChecklistStatus:v}}var s=r(4556);function c(){let{token:e,apiKey:t}=(0,i.A)(),{data:r,error:o,isLoading:l,mutate:s}=(0,a.Ay)(e&&t?["usage-summary",e,t]:null,async()=>await (0,n.S3)(),{refreshInterval:3e4,revalidateOnFocus:!0}),c=()=>{var e;return r&&(null==(e=r.apiCalls)?void 0:e.percentage)||0},u=()=>{var e;return r&&(null==(e=r.storage)?void 0:e.percentage)||0};return{usageData:r,error:o,isLoading:l,mutate:s,getApiCallsUsed:()=>{var e;return(null==r||null==(e=r.apiCalls)?void 0:e.used)||0},getApiCallsLimit:()=>{var e;return(null==r||null==(e=r.apiCalls)?void 0:e.limit)||0},getApiCallsRemaining:()=>{var e,t;let a=(null==r||null==(e=r.apiCalls)?void 0:e.used)||0;return Math.max(0,((null==r||null==(t=r.apiCalls)?void 0:t.limit)||0)-a)},getStorageUsed:()=>{var e;return(null==r||null==(e=r.storage)?void 0:e.usedBytes)||0},getStorageLimit:()=>{var e;return(null==r||null==(e=r.storage)?void 0:e.limitBytes)||0},getStorageRemaining:()=>{var e,t;let a=(null==r||null==(e=r.storage)?void 0:e.usedBytes)||0;return Math.max(0,((null==r||null==(t=r.storage)?void 0:t.limitBytes)||0)-a)},hasUsageActivity:()=>{var e;return((null==r||null==(e=r.apiCalls)?void 0:e.used)||0)>0},getApiCallsPercentage:c,getStoragePercentage:u,canTrainNow:()=>{var e;return!!r&&((null==(e=r.training)?void 0:e.canTrainNow)||!1)},getNextTrainingDate:()=>{var e;return r&&(null==(e=r.training)?void 0:e.nextAvailable)?new Date(r.training.nextAvailable):null},getLastStorageMeasurement:()=>{var e;return r&&(null==(e=r.storage)?void 0:e.lastMeasured)||null},getNextApiCallsReset:()=>{var e;return r&&(null==(e=r.apiCalls)?void 0:e.resetDate)?new Date(r.apiCalls.resetDate):null},isApiCallsLimitReached:()=>!!r&&c()>=100,isStorageLimitReached:()=>!!r&&u()>=100,getStorageUsedFormatted:()=>{var e;if(!r)return"0 B";let t=(null==(e=r.storage)?void 0:e.usedBytes)||0;if(0===t)return"0 B";let a=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB","TB"][a]},getStorageLimitFormatted:()=>{var e;if(!r)return"0 B";let t=(null==(e=r.storage)?void 0:e.limitBytes)||0,a=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB","TB"][a]},getApiCallsUsedFormatted:()=>{var e;return r?((null==(e=r.apiCalls)?void 0:e.used)||0).toLocaleString():"0"},getApiCallsLimitFormatted:()=>{var e;return r?((null==(e=r.apiCalls)?void 0:e.limit)||0).toLocaleString():"0"},getAvailableModels:()=>{var e;return r&&(null==(e=r.planLimits)?void 0:e.availableModels)||[]},getMaxRequestsPerMinute:()=>{var e;return r&&(null==(e=r.planLimits)?void 0:e.maxRequestsPerMinute)||0}}}function u(e){return!!e&&"object"==typeof e&&"string"==typeof e.date&&"number"==typeof e.api_calls&&"number"==typeof e.storage}function d(e,t){let{token:r,apiKey:o}=(0,i.A)(),l=null==e?void 0:e.toISOString().split("T")[0],s=null==t?void 0:t.toISOString().split("T")[0],{data:c,error:d,isLoading:g,mutate:p}=(0,a.Ay)(r&&o&&l&&s?["usage-history",r,o,l,s]:null,async()=>await (0,n.M2)(),{refreshInterval:6e4,revalidateOnFocus:!0,onError:e=>{e instanceof n.hD?console.error("Error fetching usage history:",e.message,e.body):console.error("Unexpected error:",e)}});return{data:c,error:d,isLoading:g,mutate:p,getTotalApiCalls:()=>c?c.reduce((e,t)=>u(t)?e+t.api_calls:e,0):0,getPeakUsageDay:()=>{if(!c||0===c.length)return null;let e=c.filter(u);if(0===e.length)return null;let t=e.reduce((e,t)=>t.api_calls>e.api_calls?t:e,e[0]);return{date:new Date(t.date),apiCalls:t.api_calls}},getLatestStorageUsage:()=>{if(!c||0===c.length)return 0;let e=c.filter(u);return 0===e.length?0:e.sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())[0].storage},getChartData:()=>c?c.filter(u).sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()).map(e=>({date:new Date(e.date),apiCalls:e.api_calls,storage:e.storage})):[],getGrowthRate:e=>{if(!c||c.length<2)return 0;let t=c.filter(u);if(t.length<2)return 0;let r=t.sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()),a="apiCalls"===e?r[0].api_calls:r[0].storage,n="apiCalls"===e?r[r.length-1].api_calls:r[r.length-1].storage;return 0===a?100*(n>0):(n-a)/a*100}}}r(467),r(2115);function g(){let{token:e,apiKey:t}=(0,i.A)(),{data:r,error:o,isLoading:l,mutate:s}=(0,a.Ay)(e&&t?["plans",e,t]:null,async()=>await (0,n.T9)(),{refreshInterval:3e5,revalidateOnFocus:!1});return{plans:r||{},error:o,isLoading:l,refresh:s,getPlanLimits:e=>{if(!r)return null;let t=r[e];return(null==t?void 0:t.limits)||null},getPlanById:e=>r&&r[e]||null,getAllPlans:()=>r?Object.values(r):[],getPlanName:e=>{if(!r)return e;let t=r[e];return(null==t?void 0:t.name)||e}}}r(4113)},5947:(e,t,r)=>{r.d(t,{o:()=>i});var a=r(2115),n=r(2656);function i(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[o,l]=(0,a.useState)(null),s=async()=>{try{i(!0),l(null);try{let e=(await (0,n._C)().listBatchJobsApiV1IngestionBatchGet()).data.map(e=>{let t={...e,status:e.status.toUpperCase(),records_processed:e.processed_count?{users:e.processed_count.users,products:e.processed_count.products,interactions:e.processed_count.interactions,total:e.processed_count.total}:void 0};if(t.started_at&&t.completed_at){let e=new Date(t.started_at).getTime(),r=new Date(t.completed_at).getTime();t.duration=Math.round((r-e)/1e3)}return t});t(e);return}catch(e){l("Error fetching ingestion jobs"),console.error("Error fetching ingestion jobs:",e)}}catch(e){l(e instanceof Error?e.message:"Error loading ingestion jobs"),console.error("Error loading ingestion jobs:",e)}finally{i(!1)}},c=async e=>{try{return(await (0,n._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data}catch(e){throw console.error("Error fetching job status:",e),e}},u=async e=>{try{let t=await (0,n._C)().batchDataIngestionApiV1IngestionBatchPost(e);return await s(),t.data}catch(e){throw console.error("Error starting batch ingestion:",e),e}};return(0,a.useEffect)(()=>{s()},[]),{jobs:e,isLoading:r,error:o,fetchJobs:s,getJobStatus:c,startBatchIngestion:u}}},6126:(e,t,r)=>{r.d(t,{E:()=>l});var a=r(5155);r(2115);var n=r(2085),i=r(9434);let o=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{className:(0,i.cn)(o({variant:r}),t),...n})}},6695:(e,t,r)=>{r.d(t,{BT:()=>s,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var a=r(5155);r(2115);var n=r(9434);function i(e){var t;let{className:r,elevation:i="soft",...o}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border",null!=(t=({none:"shadow-none",sm:"shadow-sm",soft:"shadow-soft",medium:"shadow-medium",glow:"shadow-glow"})[i])?t:"shadow-soft","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",r),...o})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("text-subheading rayuela-accent",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-caption",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}function u(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},8856:(e,t,r)=>{r.d(t,{E:()=>i});var a=r(5155),n=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)("animate-pulse rounded-lg bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-shimmer",t),...r})}}}]);