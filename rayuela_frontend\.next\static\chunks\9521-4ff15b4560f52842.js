"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9521],{646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3815:(e,t,n)=>{n.d(t,{i3:()=>eo,UC:()=>er,ZL:()=>en,Kq:()=>Q,bL:()=>ee,l9:()=>et});var r,o=n(2115),i=n(5185),l=n(6101),a=n(6081),s=n(7650),u=n(9708),c=n(5155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),p=n(9033),f=n(1595),v="dismissableLayer.update",m=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=o.forwardRef((e,t)=>{var n,a;let{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:u,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:x,onDismiss:w,...E}=e,C=o.useContext(m),[T,L]=o.useState(null),k=null!=(a=null==T?void 0:T.ownerDocument)?a:null==(n=globalThis)?void 0:n.document,[,N]=o.useState({}),M=(0,l.s)(t,e=>L(e)),O=Array.from(C.layers),[P]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),R=O.indexOf(P),D=T?O.indexOf(T):-1,A=C.layersWithOutsidePointerEventsDisabled.size>0,j=D>=R,_=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,p.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){g("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));j&&!n&&(null==y||y(e),null==x||x(e),e.defaultPrevented||null==w||w())},k),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,p.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&g("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==b||b(e),null==x||x(e),e.defaultPrevented||null==w||w())},k);return(0,f.U)(e=>{D===C.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},k),o.useEffect(()=>{if(T)return s&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(T)),C.layers.add(T),h(),()=>{s&&1===C.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=r)}},[T,k,s,C]),o.useEffect(()=>()=>{T&&(C.layers.delete(T),C.layersWithOutsidePointerEventsDisabled.delete(T),h())},[T,C]),o.useEffect(()=>{let e=()=>N({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,c.jsx)(d.div,{...E,ref:M,style:{pointerEvents:A?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,_.onPointerDownCapture)})});function h(){let e=new CustomEvent(v);document.dispatchEvent(e)}function g(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&s.flushSync(()=>i.dispatchEvent(l));else i.dispatchEvent(l)}y.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(m),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(d.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var b=n(1285),x=n(5773),w=n(2712),E=o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,u]=o.useState(!1);(0,w.N)(()=>u(!0),[]);let p=i||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return p?s.createPortal((0,c.jsx)(d.div,{...l,ref:t}),p):null});E.displayName="Portal";var C=n(8905),T=n(5845),L=n(3601),[k,N]=(0,a.A)("Tooltip",[x.Bk]),M=(0,x.Bk)(),O="TooltipProvider",P="tooltip.open",[R,D]=k(O),A=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:i=!1,children:l}=e,a=o.useRef(!0),s=o.useRef(!1),u=o.useRef(0);return o.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,c.jsx)(R,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:o.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:o.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,r)},[r]),isPointerInTransitRef:s,onPointerInTransitChange:o.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};A.displayName=O;var j="Tooltip",[_,I]=k(j),S=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:s}=e,u=D(j,e.__scopeTooltip),d=M(t),[p,f]=o.useState(null),v=(0,b.B)(),m=o.useRef(0),y=null!=a?a:u.disableHoverableContent,h=null!=s?s:u.delayDuration,g=o.useRef(!1),[w,E]=(0,T.i)({prop:r,defaultProp:null!=i&&i,onChange:e=>{e?(u.onOpen(),document.dispatchEvent(new CustomEvent(P))):u.onClose(),null==l||l(e)},caller:j}),C=o.useMemo(()=>w?g.current?"delayed-open":"instant-open":"closed",[w]),L=o.useCallback(()=>{window.clearTimeout(m.current),m.current=0,g.current=!1,E(!0)},[E]),k=o.useCallback(()=>{window.clearTimeout(m.current),m.current=0,E(!1)},[E]),N=o.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{g.current=!0,E(!0),m.current=0},h)},[h,E]);return o.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,c.jsx)(x.bL,{...d,children:(0,c.jsx)(_,{scope:t,contentId:v,open:w,stateAttribute:C,trigger:p,onTriggerChange:f,onTriggerEnter:o.useCallback(()=>{u.isOpenDelayedRef.current?N():L()},[u.isOpenDelayedRef,N,L]),onTriggerLeave:o.useCallback(()=>{y?k():(window.clearTimeout(m.current),m.current=0)},[k,y]),onOpen:L,onClose:k,disableHoverableContent:y,children:n})})};S.displayName=j;var U="TooltipTrigger",B=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,a=I(U,n),s=D(U,n),u=M(n),p=o.useRef(null),f=(0,l.s)(t,p,a.onTriggerChange),v=o.useRef(!1),m=o.useRef(!1),y=o.useCallback(()=>v.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,c.jsx)(x.Mz,{asChild:!0,...u,children:(0,c.jsx)(d.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...r,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(m.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),m.current=!0))}),onPointerLeave:(0,i.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),m.current=!1}),onPointerDown:(0,i.m)(e.onPointerDown,()=>{a.open&&a.onClose(),v.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,i.m)(e.onFocus,()=>{v.current||a.onOpen()}),onBlur:(0,i.m)(e.onBlur,a.onClose),onClick:(0,i.m)(e.onClick,a.onClose)})})});B.displayName=U;var F="TooltipPortal",[W,z]=k(F,{forceMount:void 0}),V=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=I(F,t);return(0,c.jsx)(W,{scope:t,forceMount:n,children:(0,c.jsx)(C.C,{present:n||i.open,children:(0,c.jsx)(E,{asChild:!0,container:o,children:r})})})};V.displayName=F;var H="TooltipContent",q=o.forwardRef((e,t)=>{let n=z(H,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=I(H,e.__scopeTooltip);return(0,c.jsx)(C.C,{present:r||l.open,children:l.disableHoverableContent?(0,c.jsx)(Z,{side:o,...i,ref:t}):(0,c.jsx)(X,{side:o,...i,ref:t})})}),X=o.forwardRef((e,t)=>{let n=I(H,e.__scopeTooltip),r=D(H,e.__scopeTooltip),i=o.useRef(null),a=(0,l.s)(t,i),[s,u]=o.useState(null),{trigger:d,onClose:p}=n,f=i.current,{onPointerInTransitChange:v}=r,m=o.useCallback(()=>{u(null),v(!1)},[v]),y=o.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),v(!0)},[v]);return o.useEffect(()=>()=>m(),[m]),o.useEffect(()=>{if(d&&f){let e=e=>y(e,f),t=e=>y(e,d);return d.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[d,f,y,m]),o.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==d?void 0:d.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-s)*(r-u)/(d-u)+s&&(o=!o)}return o}(n,s);r?m():o&&(m(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[d,f,s,p,m]),(0,c.jsx)(Z,{...e,ref:a})}),[Y,$]=k(j,{isInside:!1}),K=(0,u.Dc)("TooltipContent"),Z=o.forwardRef((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:a,...s}=e,u=I(H,n),d=M(n),{onClose:p}=u;return o.useEffect(()=>(document.addEventListener(P,p),()=>document.removeEventListener(P,p)),[p]),o.useEffect(()=>{if(u.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(u.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[u.trigger,p]),(0,c.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,c.jsxs)(x.UC,{"data-state":u.stateAttribute,...d,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,c.jsx)(K,{children:r}),(0,c.jsx)(Y,{scope:n,isInside:!0,children:(0,c.jsx)(L.bL,{id:u.contentId,role:"tooltip",children:i||r})})]})})});q.displayName=H;var G="TooltipArrow",J=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=M(n);return $(G,n).isInside?null:(0,c.jsx)(x.i3,{...o,...r,ref:t})});J.displayName=G;var Q=A,ee=S,et=B,en=V,er=q,eo=J},3904:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4213:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},8905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(2115),o=n(6101),i=n(2712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),s=r.useRef({}),u=r.useRef(e),c=r.useRef("none"),[d,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(s.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=s.current,n=u.current;if(n!==e){let r=c.current,o=a(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(s.current).includes(e.animationName);if(e.target===o&&r&&(p("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=a(s.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),l(e)},[])}}(t),s="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),u=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||l.isPresent?r.cloneElement(s,{ref:u}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"}}]);