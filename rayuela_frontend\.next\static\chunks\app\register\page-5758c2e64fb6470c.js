(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{2972:(e,r,a)=>{"use strict";a.d(r,{default:()=>g});var t=a(5155),s=a(2115),o=a(5695),n=a(6874),l=a.n(n),i=a(2177),d=a(221),c=a(5594),m=a(6671),u=a(285),p=a(7759),h=a(2523),f=a(3999),x=a(7588),E=a(2193);let v=c.Ik({accountName:c.Yj().min(1,{message:"Nombre de cuenta es requerido"}),email:c.Yj().email({message:"Email inv\xe1lido"}),password:c.Yj().min(8,{message:"La contrase\xf1a debe tener al menos 8 caracteres"}).max(100).regex(/[A-Z]/,{message:"La contrase\xf1a debe tener al menos una may\xfascula"}).regex(/[a-z]/,{message:"La contrase\xf1a debe tener al menos una min\xfascula"}).regex(/[0-9]/,{message:"La contrase\xf1a debe tener al menos un n\xfamero"}),confirmPassword:c.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]});function g(){let{register:e}=(0,f.A)(),r=(0,o.useRouter)(),[a,n]=(0,s.useState)(!1),[c,g]=(0,s.useState)(null),j=(0,i.mN)({resolver:(0,d.u)(v),defaultValues:{accountName:"",email:"",password:"",confirmPassword:""}}),I=async a=>{try{n(!0);let t=await e(a.accountName,a.email,a.password);(null==t?void 0:t.apiKey)?g(t.apiKey):(r.push("/dashboard"),m.o.success("Registro exitoso. \xa1Bienvenido a Rayuela!"))}catch(e){(0,x.h)(e,"Error al registrarse")}finally{n(!1)}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2 text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Crear Cuenta"}),(0,t.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Ingresa tus datos para registrarte"})]}),(0,t.jsx)(p.lV,{...j,children:(0,t.jsxs)("form",{onSubmit:j.handleSubmit(I),className:"space-y-4",children:[(0,t.jsx)(p.zB,{control:j.control,name:"accountName",render:e=>{let{field:r}=e;return(0,t.jsxs)(p.eI,{children:[(0,t.jsx)(p.lR,{children:"Nombre de la Cuenta"}),(0,t.jsx)(p.MJ,{children:(0,t.jsx)(h.p,{placeholder:"Mi Empresa Inc.",type:"text",disabled:a,...r})}),(0,t.jsx)(p.C5,{})]})}}),(0,t.jsx)(p.zB,{control:j.control,name:"email",render:e=>{let{field:r}=e;return(0,t.jsxs)(p.eI,{children:[(0,t.jsx)(p.lR,{children:"Email"}),(0,t.jsx)(p.MJ,{children:(0,t.jsx)(h.p,{placeholder:"<EMAIL>",type:"email",autoComplete:"email",disabled:a,...r})}),(0,t.jsx)(p.C5,{})]})}}),(0,t.jsx)(p.zB,{control:j.control,name:"password",render:e=>{let{field:r}=e;return(0,t.jsxs)(p.eI,{children:[(0,t.jsx)(p.lR,{children:"Contrase\xf1a"}),(0,t.jsx)(p.MJ,{children:(0,t.jsx)(h.p,{placeholder:"••••••••",type:"password",autoComplete:"new-password",disabled:a,...r})}),(0,t.jsx)(p.C5,{})]})}}),(0,t.jsx)(p.zB,{control:j.control,name:"confirmPassword",render:e=>{let{field:r}=e;return(0,t.jsxs)(p.eI,{children:[(0,t.jsx)(p.lR,{children:"Confirmar Contrase\xf1a"}),(0,t.jsx)(p.MJ,{children:(0,t.jsx)(h.p,{placeholder:"••••••••",type:"password",autoComplete:"new-password",disabled:a,...r})}),(0,t.jsx)(p.C5,{})]})}}),(0,t.jsx)(u.Button,{type:"submit",className:"w-full",disabled:a,children:a?"Registrando...":"Registrarse"})]})}),(0,t.jsxs)("div",{className:"text-center text-sm",children:["\xbfYa tienes una cuenta?"," ",(0,t.jsx)(l(),{href:"/login",className:"underline underline-offset-4 hover:text-primary",children:"Iniciar Sesi\xf3n"})]})]}),c&&(0,t.jsx)(E.A,{apiKey:c,onClose:()=>{g(null),r.push("/dashboard")}})]})}},5057:(e,r,a)=>{"use strict";a.d(r,{J:()=>n});var t=a(5155);a(2115);var s=a(968),o=a(9434);function n(e){let{className:r,...a}=e;return(0,t.jsx)(s.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...a})}},7588:(e,r,a)=>{"use strict";a.d(r,{h:()=>c});var t=a(6671),s=a(3464);class o extends Error{static isApiError(e){return e instanceof o}static fromResponse(e){return new o(e.message,e.status_code,e.error_code,e.details)}constructor(e,r,a,t){super(e),this.status=r,this.errorCode=a,this.details=t,this.name="ApiError"}}let n=s.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>{{var r,a,t;let s=localStorage.getItem("rayuela-token"),o=localStorage.getItem("rayuela-apiKey");e.headers=null!=(t=e.headers)?t:{},s&&(e.headers.Authorization="Bearer ".concat(s)),!o||(null==(r=e.url)?void 0:r.includes("/auth/token"))||(null==(a=e.url)?void 0:a.includes("/auth/register"))||(e.headers["X-API-Key"]=o)}return e}),n.interceptors.response.use(e=>e,e=>{if(e.response){let r=e.response.data;throw o.fromResponse(r)}if(e.request)throw new o("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new o(e.message,0,"REQUEST_ERROR",null)});var l=a(6874),i=a.n(l),d=a(2115);function c(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Ha ocurrido un error";return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof o)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(d.createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",d.createElement(i(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(d.createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",d.createElement(i(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void t.o.error(d.createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",d.createElement(i(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void t.o.error(d.createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",d.createElement(i(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void t.o.error(d.createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",d.createElement(i(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void t.o.error(d.createElement("div",{},"Error de validacion: "+e.message+". ",d.createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void t.o.error(d.createElement("div",{},"Datos insuficientes para generar recomendaciones. ",d.createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void t.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(t.o.error(e.message||r),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void t.o.error(e.message||r):void(t.o.error(r),console.groupEnd())}},7759:(e,r,a)=>{"use strict";a.d(r,{C5:()=>E,MJ:()=>x,eI:()=>h,lR:()=>f,lV:()=>d,zB:()=>m});var t=a(5155),s=a(2115),o=a(9708),n=a(2177),l=a(9434),i=a(5057);let d=n.Op,c=s.createContext({}),m=e=>{let{...r}=e;return(0,t.jsx)(c.Provider,{value:{name:r.name},children:(0,t.jsx)(n.xI,{...r})})},u=()=>{let e=s.useContext(c),r=s.useContext(p),{getFieldState:a,formState:t}=(0,n.xW)(),o=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...o}},p=s.createContext({}),h=s.forwardRef((e,r)=>{let{className:a,...o}=e,n=s.useId();return(0,t.jsx)(p.Provider,{value:{id:n},children:(0,t.jsx)("div",{ref:r,className:(0,l.cn)("space-y-2",a),...o})})});h.displayName="FormItem";let f=s.forwardRef((e,r)=>{let{className:a,...s}=e,{error:o,formItemId:n}=u();return(0,t.jsx)(i.J,{ref:r,className:(0,l.cn)(o&&"text-destructive",a),htmlFor:n,...s})});f.displayName="FormLabel";let x=s.forwardRef((e,r)=>{let{...a}=e,{error:s,formItemId:n,formDescriptionId:l,formMessageId:i}=u();return(0,t.jsx)(o.DX,{ref:r,id:n,"aria-describedby":s?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!s,...a})});x.displayName="FormControl",s.forwardRef((e,r)=>{let{className:a,...s}=e,{formDescriptionId:o}=u();return(0,t.jsx)("p",{ref:r,id:o,className:(0,l.cn)("text-sm text-muted-foreground",a),...s})}).displayName="FormDescription";let E=s.forwardRef((e,r)=>{let{className:a,children:s,...o}=e,{error:n,formMessageId:i}=u(),d=n?String(null==n?void 0:n.message):s;return d?(0,t.jsx)("p",{ref:r,id:i,className:(0,l.cn)("text-sm font-medium text-destructive",a),...o,children:d}):null});E.displayName="FormMessage"},9666:(e,r,a)=>{Promise.resolve().then(a.bind(a,2972))}},e=>{var r=r=>e(e.s=r);e.O(0,[9352,6874,1445,5674,3753,5352,2092,3999,8441,1684,7358],()=>r(9666)),_N_E=e.O()}]);