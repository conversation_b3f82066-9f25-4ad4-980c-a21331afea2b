(()=>{var e={};e.id=2032,e.ids=[2032],e.modules={2274:(e,r,a)=>{Promise.resolve().then(a.bind(a,21849))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,r,a)=>{"use strict";a.d(r,{A0:()=>o,BF:()=>l,Hj:()=>d,XI:()=>i,nA:()=>m,nd:()=>c});var s=a(60687),t=a(43210),n=a(4780);let i=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto rounded-lg border bg-card shadow-sm",children:(0,s.jsx)("table",{ref:a,className:(0,n.cn)("w-full caption-bottom text-sm",e),...r})}));i.displayName="Table";let o=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("thead",{ref:a,className:(0,n.cn)("bg-muted/30 [&_tr]:border-b-0 [&_tr]:shadow-sm",e),...r}));o.displayName="TableHeader";let l=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("tbody",{ref:a,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...r}));l.displayName="TableBody",t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("tfoot",{ref:a,className:(0,n.cn)("bg-muted/30 font-medium shadow-sm [&>tr]:last:border-b-0",e),...r})).displayName="TableFooter";let d=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("tr",{ref:a,className:(0,n.cn)("border-b border-border/50 transition-all hover:bg-muted/30 hover:shadow-xs data-[state=selected]:bg-muted/50 data-[state=selected]:shadow-xs",e),...r}));d.displayName="TableRow";let c=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("th",{ref:a,className:(0,n.cn)("h-12 px-4 text-left align-middle font-semibold text-foreground uppercase tracking-wide text-xs sm:text-sm [&:has([role=checkbox])]:pr-0",e),...r}));c.displayName="TableHead";let m=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("td",{ref:a,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...r}));m.displayName="TableCell",t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("caption",{ref:a,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...r})).displayName="TableCaption"},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13972:(e,r,a)=>{"use strict";a.d(r,{Lt:()=>K,Rx:()=>B,Zr:()=>O,EO:()=>M,$v:()=>F,ck:()=>S,wd:()=>T,r7:()=>L,tv:()=>D});var s=a(60687),t=a(43210),n=a(11273),i=a(98599),o=a(4590),l=a(70569),d=Symbol("radix.slottable"),c="AlertDialog",[m,u]=(0,n.A)(c,[o.Hs]),p=(0,o.Hs)(),x=e=>{let{__scopeAlertDialog:r,...a}=e,t=p(r);return(0,s.jsx)(o.bL,{...t,...a,modal:!0})};x.displayName=c;var h=t.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...t}=e,n=p(a);return(0,s.jsx)(o.l9,{...n,...t,ref:r})});h.displayName="AlertDialogTrigger";var f=e=>{let{__scopeAlertDialog:r,...a}=e,t=p(r);return(0,s.jsx)(o.ZL,{...t,...a})};f.displayName="AlertDialogPortal";var v=t.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...t}=e,n=p(a);return(0,s.jsx)(o.hJ,{...n,...t,ref:r})});v.displayName="AlertDialogOverlay";var y="AlertDialogContent",[g,j]=m(y),b=function(e){let r=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=d,r}("AlertDialogContent"),N=t.forwardRef((e,r)=>{let{__scopeAlertDialog:a,children:n,...d}=e,c=p(a),m=t.useRef(null),u=(0,i.s)(r,m),x=t.useRef(null);return(0,s.jsx)(o.G$,{contentName:y,titleName:A,docsSlug:"alert-dialog",children:(0,s.jsx)(g,{scope:a,cancelRef:x,children:(0,s.jsxs)(o.UC,{role:"alertdialog",...c,...d,ref:u,onOpenAutoFocus:(0,l.m)(d.onOpenAutoFocus,e=>{e.preventDefault(),x.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(b,{children:n}),(0,s.jsx)(C,{contentRef:m})]})})})});N.displayName=y;var A="AlertDialogTitle",w=t.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...t}=e,n=p(a);return(0,s.jsx)(o.hE,{...n,...t,ref:r})});w.displayName=A;var k="AlertDialogDescription",E=t.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...t}=e,n=p(a);return(0,s.jsx)(o.VY,{...n,...t,ref:r})});E.displayName=k;var I=t.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...t}=e,n=p(a);return(0,s.jsx)(o.bm,{...n,...t,ref:r})});I.displayName="AlertDialogAction";var _="AlertDialogCancel",P=t.forwardRef((e,r)=>{let{__scopeAlertDialog:a,...t}=e,{cancelRef:n}=j(_,a),l=p(a),d=(0,i.s)(r,n);return(0,s.jsx)(o.bm,{...l,...t,ref:d})});P.displayName=_;var C=({contentRef:e})=>{let r=`\`${y}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${y}\` by passing a \`${k}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${y}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return t.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(r)},[r,e]),null},R=a(4780),z=a(29523);let K=x,D=h,q=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(v,{className:(0,R.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...r,ref:a}));q.displayName=v.displayName;let M=t.forwardRef(({className:e,...r},a)=>(0,s.jsxs)(f,{children:[(0,s.jsx)(q,{}),(0,s.jsx)(N,{ref:a,className:(0,R.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r})]}));M.displayName=N.displayName;let T=({className:e,...r})=>(0,s.jsx)("div",{className:(0,R.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...r});T.displayName="AlertDialogHeader";let S=({className:e,...r})=>(0,s.jsx)("div",{className:(0,R.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...r});S.displayName="AlertDialogFooter";let L=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(w,{ref:a,className:(0,R.cn)("text-lg font-semibold",e),...r}));L.displayName=w.displayName;let F=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(E,{ref:a,className:(0,R.cn)("text-sm text-muted-foreground",e),...r}));F.displayName=E.displayName;let B=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(I,{ref:a,className:(0,R.cn)((0,z.r)(),e),...r}));B.displayName=I.displayName;let O=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(P,{ref:a,className:(0,R.cn)((0,z.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...r}));O.displayName=P.displayName},15079:(e,r,a)=>{"use strict";a.d(r,{bq:()=>u,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>m});var s=a(60687),t=a(43210),n=a(25957),i=a(78272),o=a(3589),l=a(13964),d=a(4780);let c=n.bL;n.YJ;let m=n.WT,u=t.forwardRef(({className:e,children:r,...a},t)=>(0,s.jsxs)(n.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-lg border border-input bg-background px-3 py-2 text-sm shadow-soft rayuela-interactive rayuela-focus-ring hover:border-ring/50 ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[r,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"h-4 w-4 opacity-50 transition-transform group-data-[state=open]:rotate-180"})})]}));u.displayName=n.l9.displayName;let p=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(n.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,s.jsx)(o.A,{className:"h-4 w-4"})}));p.displayName=n.PP.displayName;let x=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(n.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));x.displayName=n.wn.displayName;let h=t.forwardRef(({className:e,children:r,position:a="popper",...t},i)=>(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-lg border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...t,children:[(0,s.jsx)(p,{}),(0,s.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,s.jsx)(x,{})]})}));h.displayName=n.UC.displayName,t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(n.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...r})).displayName=n.JU.displayName;let f=t.forwardRef(({className:e,children:r,...a},t)=>(0,s.jsxs)(n.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-md py-1.5 pl-8 pr-2 text-sm outline-none transition-colors hover:bg-accent/50 focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})}),(0,s.jsx)(n.p4,{children:r})]}));f.displayName=n.q7.displayName,t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(n.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...r})).displayName=n.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21849:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>B});var s=a(60687),t=a(43210),n=a(29523),i=a(44493),o=a(85726),l=a(6211),d=a(96834),c=a(91821),m=a(44957),u=a(56184),p=a(52581),x=a(59327),h=a(85650),f=a(41585),v=a(93613),y=a(19959),g=a(62688);let j=(0,g.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var b=a(80462),N=a(99270),A=a(70615),w=a(53411);let k=(0,g.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var E=a(88233),I=a(96882);let _=(0,g.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),P=(0,g.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var C=a(41312),R=a(85814),z=a.n(R),K=a(81079),D=a(58133),q=a(53520),M=a(13972),T=a(63503),S=a(89667),L=a(80013),F=a(15079);function B(){let{token:e,user:r,isLoading:a}=(0,m.A)(),[g,R]=(0,t.useState)(!1),[B,O]=(0,t.useState)(null),[H,$]=(0,t.useState)(!1),[U,G]=(0,t.useState)(!1),[V,X]=(0,t.useState)(null),[Z,J]=(0,t.useState)(""),[W,Y]=(0,t.useState)(""),[Q,ee]=(0,t.useState)(""),[er,ea]=(0,t.useState)("all"),{data:es,error:et,isLoading:en,createApiKey:ei,updateApiKey:eo,revokeApiKey:el,isCreating:ed,isUpdating:ec,isRevoking:em,getFormattedApiKey:eu}=(0,u.Q)({revalidateOnFocus:!0,refreshInterval:3e4,dedupingInterval:5e3,errorRetryCount:3}),ep=(0,t.useMemo)(()=>es?.api_keys?es.api_keys.filter(e=>{let r="all"===er||"active"===er&&e.is_active||"revoked"===er&&!e.is_active,a=""===Q||e.name&&e.name.toLowerCase().includes(Q.toLowerCase())||e.id.toString().includes(Q);return r&&a}):[],[es?.api_keys,er,Q]),ex=e=>e.prefix&&e.last_chars?`${e.prefix}••••••••••••••••${e.last_chars}`:"ray_••••••••••••••••••••",eh=e=>eu(e),ef=async()=>{try{let e=await ei({name:Z.trim()||`API Key ${new Date().toLocaleDateString("es-ES")}`});e?.api_key&&(O(e.api_key),R(!0),$(!1),J(""),p.o.success("API Key creada con \xe9xito"))}catch(e){(0,x.h)(e,"Error al crear la API Key")}},ev=async()=>{if(V)try{await eo(V.id,{name:W.trim()})&&(G(!1),X(null),Y(""),p.o.success("API Key actualizada con \xe9xito"))}catch(e){(0,x.h)(e,"Error al actualizar la API Key")}},ey=async(e,r)=>{try{await el(e),p.o.success(`API Key ${r?`"${r}"`:""} revocada con \xe9xito`)}catch(e){(0,x.h)(e,"Error al revocar la API Key")}},eg=e=>{let r=eh(e);r&&(navigator.clipboard.writeText(r),p.o.success("API Key copiada al portapapeles"))},ej=e=>{X(e),Y(e.name||""),G(!0)},eb=()=>{ee(""),ea("all")};if(et)return(0,s.jsx)("div",{className:"container mx-auto py-8",children:(0,s.jsxs)(c.Fc,{variant:"destructive",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),(0,s.jsx)(c.XL,{children:"Error al cargar las API Keys"}),(0,s.jsxs)(c.TN,{children:[et.message,(0,s.jsx)("br",{}),"Intenta refrescar la p\xe1gina o contacta a soporte."]})]})});if(!e||!r)return(0,s.jsx)("div",{className:"container mx-auto py-8",children:(0,s.jsxs)(c.Fc,{variant:"warning",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),(0,s.jsx)(c.XL,{children:"Sesi\xf3n no verificada"}),(0,s.jsxs)(c.TN,{children:["No se pudo verificar tu sesi\xf3n. Por favor, intenta ",(0,s.jsx)(z(),{href:"/login",className:"underline",children:"iniciar sesi\xf3n"})," de nuevo."]})]})});if(a||en)return(0,s.jsx)("div",{className:"container mx-auto py-8 space-y-8",children:(0,s.jsxs)("div",{className:"bg-card/30 border border-border/50 rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"API Keys"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"Gestiona m\xfaltiples claves para tus proyectos y entornos"})]}),(0,s.jsx)(o.E,{className:"h-10 w-32"})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(o.E,{className:"h-6 w-6"}),(0,s.jsx)(o.E,{className:"h-6 w-48"})]}),(0,s.jsx)(i.BT,{children:(0,s.jsx)(o.E,{className:"h-4 w-64"})})]}),(0,s.jsx)(i.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.E,{className:"h-10 w-full"}),(0,s.jsx)(o.E,{className:"h-10 w-full"}),(0,s.jsx)(o.E,{className:"h-10 w-full"})]})})]})]})});let eN=es?.api_keys?.length||0,eA=es?.api_keys?.filter(e=>e.is_active)?.length||0,ew=eN-eA;return(0,s.jsxs)(D.hI,{title:"API Keys",description:"Gestiona m\xfaltiples claves de API para acceder a los servicios de Rayuela",actions:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex gap-6 text-sm text-muted-foreground mr-4",children:[(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)(q.mm,{icon:y.A,size:"sm",context:"success"}),eA," activas"]}),ew>0&&(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)(q.mm,{icon:v.A,size:"sm",context:"error"}),ew," revocadas"]}),(0,s.jsxs)("span",{className:"font-medium",children:["Total: ",eN]})]}),(0,s.jsxs)(T.lG,{open:H,onOpenChange:$,children:[(0,s.jsx)(T.zM,{asChild:!0,children:(0,s.jsxs)(n.Button,{className:"flex items-center gap-2",children:[(0,s.jsx)(q.mm,{icon:j,size:"sm",context:"neutral"}),"Nueva API Key"]})}),(0,s.jsxs)(T.Cf,{children:[(0,s.jsxs)(T.c7,{children:[(0,s.jsx)(T.L3,{children:"Crear Nueva API Key"}),(0,s.jsx)(T.rr,{children:"Crea una nueva API Key para acceder a los servicios de Rayuela. Puedes asignarle un nombre descriptivo."})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(L.J,{htmlFor:"newKeyName",children:"Nombre de la API Key (opcional)"}),(0,s.jsx)(S.p,{id:"newKeyName",placeholder:"ej. Producci\xf3n, Desarrollo, Equipo Frontend...",value:Z,onChange:e=>J(e.target.value)})]})}),(0,s.jsxs)(T.Es,{children:[(0,s.jsx)(n.Button,{variant:"outline",onClick:()=>$(!1),children:"Cancelar"}),(0,s.jsx)(n.Button,{onClick:ef,disabled:ed,children:ed?"Creando...":"Crear API Key"})]})]})]})]}),children:[(0,s.jsx)(D.os,{title:"Filtros y B\xfasqueda",icon:(0,s.jsx)(q.mm,{icon:b.A,size:"md",context:"primary"}),description:"Encuentra r\xe1pidamente las API Keys que necesitas",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-6 sm:flex-row sm:items-center sm:gap-6",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(q.mm,{icon:N.A,size:"sm",context:"muted",className:"absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,s.jsx)(S.p,{placeholder:"Buscar por nombre o ID...",value:Q,onChange:e=>ee(e.target.value),className:"pl-10"})]})}),(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,s.jsx)(L.J,{htmlFor:"status-filter",className:"text-sm font-medium whitespace-nowrap",children:"Estado:"}),(0,s.jsxs)(F.l6,{value:er,onValueChange:e=>ea(e),children:[(0,s.jsx)(F.bq,{id:"status-filter",className:"w-32",children:(0,s.jsx)(F.yv,{})}),(0,s.jsxs)(F.gC,{children:[(0,s.jsx)(F.eb,{value:"all",children:"Todas"}),(0,s.jsx)(F.eb,{value:"active",children:"Activas"}),(0,s.jsx)(F.eb,{value:"revoked",children:"Revocadas"})]})]}),(Q||"all"!==er)&&(0,s.jsx)(n.Button,{variant:"outline",size:"sm",onClick:eb,children:"Limpiar"})]})]}),(Q||"all"!==er)&&(0,s.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:["Mostrando ",ep.length," de ",eN," API Keys"]})]})}),(0,s.jsx)(D.os,{title:"Tus API Keys",icon:(0,s.jsx)(q.mm,{icon:y.A,size:"md",context:"primary"}),description:"Cada clave funciona independientemente y puede ser usada para diferentes proyectos o entornos.",children:(0,s.jsx)("div",{className:"overflow-hidden",children:(0,s.jsxs)(l.XI,{children:[(0,s.jsx)(l.A0,{className:"bg-muted/10",children:(0,s.jsxs)(l.Hj,{className:"border-b border-border/30",children:[(0,s.jsx)(l.nd,{className:"font-semibold",children:"Nombre"}),(0,s.jsx)(l.nd,{className:"font-semibold",children:"Clave"}),(0,s.jsx)(l.nd,{className:"font-semibold",children:"Estado"}),(0,s.jsx)(l.nd,{className:"font-semibold",children:"Creaci\xf3n"}),(0,s.jsx)(l.nd,{className:"font-semibold",children:"\xdaltimo uso"}),(0,s.jsxs)(l.nd,{className:"font-semibold",children:["Uso ",(0,s.jsx)("span",{className:"text-xs font-normal",children:"(pr\xf3ximamente)"})]}),(0,s.jsx)(l.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,s.jsx)(l.BF,{children:ep&&ep.length>0?ep.map((e,r)=>(0,s.jsxs)(D.AP,{index:r,className:e.is_active?"":"opacity-60",children:[(0,s.jsx)(l.nA,{className:"font-medium py-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(q.mm,{icon:e.is_active?y.A:v.A,size:"xs",context:e.is_active?"success":"error"}),e.name||`API Key ${e.id}`]})}),(0,s.jsx)(l.nA,{className:"py-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"text-sm bg-muted/50 px-2 py-1 rounded border font-mono",children:ex(e)}),e.is_active&&(0,s.jsx)(n.Button,{variant:"ghost",size:"sm",onClick:()=>eg(e),className:"h-6 w-6 p-0 hover:bg-muted/50",title:"Copiar API Key completa",children:(0,s.jsx)(q.mm,{icon:A.A,size:"xs",context:"muted"})})]})}),(0,s.jsx)(l.nA,{className:"py-4",children:(0,s.jsx)(d.E,{variant:e.is_active?"success":"destructive",children:e.is_active?"Activa":"Revocada"})}),(0,s.jsx)(l.nA,{className:"text-muted-foreground py-4",children:e.created_at?(0,h.GP)(new Date(e.created_at),"d 'de' MMMM, yyyy",{locale:f.es}):"N/A"}),(0,s.jsx)(l.nA,{className:"text-muted-foreground py-4",children:e.last_used?(0,h.GP)(new Date(e.last_used),"d 'de' MMMM, yyyy",{locale:f.es}):"Nunca"}),(0,s.jsx)(l.nA,{className:"py-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-1 text-muted-foreground",children:[(0,s.jsx)(q.mm,{icon:w.A,size:"xs",context:"muted"}),(0,s.jsx)("span",{className:"text-xs",children:"--"})]})}),(0,s.jsx)(l.nA,{className:"text-right py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[e.is_active&&(0,s.jsx)(n.Button,{variant:"ghost",size:"sm",onClick:()=>ej(e),className:"h-8 w-8 p-0 hover:bg-muted/50",title:"Editar nombre",children:(0,s.jsx)(q.mm,{icon:k,size:"sm",context:"muted"})}),e.is_active&&(0,s.jsxs)(M.Lt,{children:[(0,s.jsx)(M.tv,{asChild:!0,children:(0,s.jsx)(n.Button,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10",title:"Revocar API Key",children:(0,s.jsx)(q.mm,{icon:E.A,size:"sm",context:"error"})})}),(0,s.jsxs)(M.EO,{children:[(0,s.jsxs)(M.wd,{children:[(0,s.jsx)(M.r7,{children:"\xbfEst\xe1s seguro?"}),(0,s.jsxs)(M.$v,{children:['Esta acci\xf3n no se puede deshacer. Esto revocar\xe1 permanentemente la API Key "',e.name||`API Key ${e.id}`,'" y cualquier aplicaci\xf3n que la use dejar\xe1 de funcionar.']})]}),(0,s.jsxs)(M.ck,{children:[(0,s.jsx)(M.Zr,{children:"Cancelar"}),(0,s.jsx)(M.Rx,{onClick:()=>ey(e.id,e.name||void 0),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:em,children:em?"Revocando...":"Revocar API Key"})]})]})]})]})})]},e.id)):(0,s.jsx)(l.Hj,{children:(0,s.jsx)(l.nA,{colSpan:7,className:"text-center py-8",children:(0,s.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===eN?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(q.mm,{icon:v.A,size:"2xl",context:"muted"}),(0,s.jsx)("p",{children:"No tienes API Keys creadas a\xfan"}),(0,s.jsx)("p",{className:"text-sm",children:"Crea tu primera API Key para comenzar a usar la API"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(q.mm,{icon:N.A,size:"2xl",context:"muted"}),(0,s.jsx)("p",{children:"No se encontraron API Keys con los filtros aplicados"}),(0,s.jsx)(n.Button,{variant:"outline",size:"sm",onClick:eb,children:"Limpiar filtros"})]})})})})})]})})}),(0,s.jsxs)(c.Fc,{variant:"info",children:[(0,s.jsx)(q.mm,{icon:I.A,size:"sm",context:"info"}),(0,s.jsx)(c.XL,{children:"Sistema Multi-API Key: Gesti\xf3n Profesional"}),(0,s.jsx)(c.TN,{children:(0,s.jsxs)("div",{className:"space-y-4 text-sm mt-3",children:[(0,s.jsxs)("p",{children:["Rayuela soporta m\xfaltiples API Keys para ofrecerte m\xe1xima flexibilidad y seguridad. Cada clave funciona de forma independiente y las claves completas ",(0,s.jsx)("strong",{children:"solo se muestran una vez"})," al crearlas."]}),(0,s.jsxs)("div",{className:"grid gap-3 mt-4",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,s.jsx)(q.mm,{icon:_,size:"md",context:"success",className:"mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-foreground",children:"Seguridad Avanzada"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Revoca claves espec\xedficas sin afectar otras integraciones. Cada clave act\xfaa independientemente."})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,s.jsx)(q.mm,{icon:P,size:"md",context:"info",className:"mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-foreground",children:"Organizaci\xf3n por Entorno"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:'Asigna nombres descriptivos: "Producci\xf3n", "Desarrollo", "Testing". Identifica f\xe1cilmente el prop\xf3sito de cada clave.'})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,s.jsx)(q.mm,{icon:C.A,size:"md",context:"primary",className:"mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-foreground",children:"Gesti\xf3n por Equipos"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Crea claves separadas para diferentes equipos o aplicaciones. Control granular sobre el acceso."})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,s.jsx)(q.mm,{icon:w.A,size:"md",context:"metric",className:"mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-foreground",children:"M\xe9tricas Futuras"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Pr\xf3ximamente: an\xe1lisis de uso individual por API Key, incluyendo requests, errores y patrones de consumo."})]})]})]}),(0,s.jsx)("div",{className:"mt-4 p-3 bg-info-light rounded-lg border border-info/20",children:(0,s.jsxs)("p",{className:"text-sm text-info",children:[(0,s.jsx)("strong",{children:"Tip profesional:"})," Mant\xe9n claves separadas para diferentes entornos. Si comprometes una clave en desarrollo, tu producci\xf3n seguir\xe1 segura."]})})]})})]}),g&&B&&(0,s.jsx)(K.A,{apiKey:B,onClose:()=>{R(!1)}}),(0,s.jsx)(T.lG,{open:U,onOpenChange:G,children:(0,s.jsxs)(T.Cf,{children:[(0,s.jsxs)(T.c7,{children:[(0,s.jsx)(T.L3,{children:"Editar API Key"}),(0,s.jsx)(T.rr,{children:"Actualiza el nombre descriptivo de tu API Key. La clave en s\xed no se puede modificar."})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(L.J,{htmlFor:"editKeyName",children:"Nombre de la API Key"}),(0,s.jsx)(S.p,{id:"editKeyName",placeholder:"ej. Producci\xf3n, Desarrollo, Equipo Frontend...",value:W,onChange:e=>Y(e.target.value)})]})}),(0,s.jsxs)(T.Es,{children:[(0,s.jsx)(n.Button,{variant:"outline",onClick:()=>G(!1),children:"Cancelar"}),(0,s.jsx)(n.Button,{onClick:ev,disabled:ec,children:ec?"Actualizando...":"Actualizar"})]})]})})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41312:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},53411:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56184:(e,r,a)=>{"use strict";a.d(r,{Q:()=>i});var s=a(5077),t=a(62185),n=a(43210);function i(e={}){let[r,a]=(0,n.useState)(!1),[o,l]=(0,n.useState)(!1),[d,c]=(0,n.useState)(!1),[m,u]=(0,n.useState)(!1),[p,x]=(0,n.useState)(null),{data:h,error:f,isLoading:v,isValidating:y,mutate:g}=(0,s.Ay)("api-keys",async()=>await (0,t.PX)(),{revalidateOnFocus:e.revalidateOnFocus??!0,refreshInterval:e.refreshInterval,dedupingInterval:e.dedupingInterval??6e4,errorRetryCount:e.errorRetryCount??3,onError:e=>{console.error("Error fetching API keys:",e)}}),j=h?.api_keys&&h.api_keys.length>0?h.api_keys.find(e=>e.is_active)||h.api_keys[0]:null,b=async e=>{a(!0),x(null);try{let r={name:e.name||"",permissions:[]},a=await (0,t.Iq)(r);return await g(),a}catch(r){let e=r instanceof t.hD?r:new t.hD("Error al crear API Key",500);throw x(e),e}finally{a(!1)}},N=async(e,r)=>{l(!0),x(null);try{let a={name:r.name||void 0,permissions:[]},s=await (0,t.XW)(e.toString(),a);return await g(),s}catch(r){let e=r instanceof t.hD?r:new t.hD("Error al actualizar API Key",500);throw x(e),e}finally{l(!1)}},A=async e=>{c(!0),x(null);try{return await (0,t.mA)(e),await g(),!0}catch(r){let e=r instanceof t.hD?r:new t.hD("Error al revocar API Key",500);throw x(e),e}finally{c(!1)}},w=async()=>{u(!0),x(null);try{let e=await (0,t.Iq)({name:`API Key ${new Date().toLocaleDateString("es-ES")}`});return await g(),e}catch(e){return x(e instanceof t.hD?e:new t.hD("Error al regenerar API Key",500)),null}finally{u(!1)}};return{data:h??null,primaryKey:j??null,error:f??null,isLoading:v,isValidating:y,mutate:g,dataUpdatedAt:0,createApiKey:b,updateApiKey:N,revokeApiKey:A,regenerateApiKey:w,isCreating:r,isUpdating:o,isRevoking:d,isRegenerating:m,operationError:p,getFormattedApiKey:e=>{let r=e||j;return r?.prefix&&r?.last_chars?`${r.prefix}••••••••${r.last_chars}`:null}}}},58133:(e,r,a)=>{"use strict";a.d(r,{AP:()=>c,hI:()=>l,os:()=>d});var s=a(60687);a(43210);var t=a(44493),n=a(4780);function i({children:e,variant:r="default",className:a,...t}){return(0,s.jsx)("div",{className:(0,n.cn)({default:"",subtle:"bg-card/30 dark:bg-card/20 border border-border/50 rounded-lg p-6 rayuela-subtle-gradient",elevated:"bg-card border border-border shadow-sm rounded-lg p-6 rayuela-card-gradient"}[r],a),...t,children:e})}function o({variant:e="line",spacing:r="md",className:a,...t}){return(0,s.jsx)("div",{className:(0,n.cn)({sm:"my-4",md:"my-6",lg:"my-8"}[r],{line:"border-t border-border/50",space:"h-px",gradient:"h-px bg-gradient-to-r from-transparent via-border/30 to-transparent"}[e],a),...t})}function l({children:e,title:r,description:a,actions:t}){return(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(i,{variant:"subtle",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:r}),a&&(0,s.jsx)("p",{className:"text-muted-foreground text-lg",children:a})]}),t&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:t})]})}),(0,s.jsx)(o,{variant:"gradient",spacing:"lg"}),(0,s.jsx)("div",{className:"space-y-6",children:e})]})}function d({title:e,description:r,icon:a,children:i,headerActions:o,className:l,...d}){return(0,s.jsxs)(t.Zp,{className:(0,n.cn)("shadow-sm border-border/50 overflow-hidden",l),...d,children:[(0,s.jsx)(t.aR,{className:"border-b border-border/20 bg-muted/10",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(t.ZB,{className:"flex items-center gap-2 text-xl",children:[a&&(0,s.jsx)("span",{className:"text-primary",children:a}),e]}),r&&(0,s.jsx)(t.BT,{className:"text-base",children:r})]}),o&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:o})]})}),(0,s.jsx)(t.Wu,{className:"p-0",children:i})]})}function c({children:e,index:r,className:a,...t}){return(0,s.jsx)("tr",{className:(0,n.cn)("border-b border-border/20 hover:bg-muted/30 transition-colors",r%2==0?"bg-background":"bg-muted/5",a),...t,children:e})}},59327:(e,r,a)=>{"use strict";a.d(r,{h:()=>m});var s=a(52581),t=a(51060);class n extends Error{constructor(e,r,a,s){super(e),this.status=r,this.errorCode=a,this.details=s,this.name="ApiError"}static isApiError(e){return e instanceof n}static fromResponse(e){return new n(e.message,e.status_code,e.error_code,e.details)}}let i=t.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>e),i.interceptors.response.use(e=>e,e=>{if(e.response){let r=e.response.data;throw n.fromResponse(r)}if(e.request)throw new n("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new n(e.message,0,"REQUEST_ERROR",null)});var o=a(85814),l=a.n(o),d=a(43210),c=a.n(d);function m(e,r="Ha ocurrido un error"){return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof n)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void s.o.error(c().createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",c().createElement(l(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void s.o.error(c().createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",c().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void s.o.error(c().createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",c().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void s.o.error(c().createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",c().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void s.o.error(c().createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",c().createElement(l(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void s.o.error(c().createElement("div",{},"Error de validacion: "+e.message+". ",c().createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void s.o.error(c().createElement("div",{},"Datos insuficientes para generar recomendaciones. ",c().createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void s.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(s.o.error(e.message||r),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void s.o.error(e.message||r):void(s.o.error(r),console.groupEnd())}},61709:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=a(65239),t=a(48088),n=a(88170),i=a.n(n),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(r,l);let d={children:["",{children:["(dashboard)",{children:["api-keys",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,88264)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\api-keys\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\api-keys\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/(dashboard)/api-keys/page",pathname:"/api-keys",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62106:(e,r,a)=>{Promise.resolve().then(a.bind(a,88264))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,r,a)=>{"use strict";a.d(r,{b:()=>o});var s=a(43210),t=a(3416),n=a(60687),i=s.forwardRef((e,r)=>(0,n.jsx)(t.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=i},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,a)=>{"use strict";a.d(r,{J:()=>i});var s=a(60687);a(43210);var t=a(78148),n=a(4780);function i({className:e,...r}){return(0,s.jsx)(t.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},80462:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},88264:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\api-keys\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\api-keys\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,r,a)=>{"use strict";a.d(r,{E:()=>o});var s=a(60687);a(43210);var t=a(24224),n=a(4780);let i=(0,t.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...a}){return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:r}),e),...a})}},96882:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},99270:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[4447,2713,5814,5423,1576,7400,5077,6920,2807,5320],()=>a(61709));module.exports=s})();