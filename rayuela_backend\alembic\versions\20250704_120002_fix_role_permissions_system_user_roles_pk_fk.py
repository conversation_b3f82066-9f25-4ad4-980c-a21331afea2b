"""Fix PK and FK for role_permissions and system_user_roles to include account_id

Revision ID: 20250704_120002
Revises: 20250704_120001
Create Date: 2025-07-04 12:20:00
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from typing import Sequence, Union

revision: str = "20250704_120002"
down_revision: Union[str, None] = "20250704_120001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _column_exists(table: str, column: str) -> bool:
    conn = op.get_bind()
    result = conn.execute(
        text(
            """
        SELECT 1 FROM information_schema.columns
        WHERE table_name = :table
          AND column_name = :column
          AND table_schema = 'public'
        """
        ),
        {"table": table, "column": column},
    ).fetchone()
    return result is not None


def _constraint_exists(table: str, name: str) -> bool:
    conn = op.get_bind()
    result = conn.execute(
        text(
            """
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = :table
          AND constraint_name = :name
          AND table_schema = 'public'
        """
        ),
        {"table": table, "name": name},
    ).fetchone()
    return result is not None


def _drop_pk(table: str):
    # Fetch current PK name dynamically
    conn = op.get_bind()
    pk_name = conn.execute(
        text(
            """
        SELECT c.constraint_name
        FROM information_schema.table_constraints c
        WHERE c.table_name = :table AND c.constraint_type = 'PRIMARY KEY'
        """
        ),
        {"table": table},
    ).scalar()
    if pk_name:
        op.drop_constraint(pk_name, table, type_="primary")


def _drop_fk(table: str, fk_name: str):
    if _constraint_exists(table, fk_name):
        op.drop_constraint(fk_name, table, type_="foreignkey")


def upgrade() -> None:
    """Correct PKs and FKs for role_permissions and system_user_roles."""

    # === role_permissions ===
    if not _column_exists("role_permissions", "account_id"):
        op.add_column("role_permissions", sa.Column("account_id", sa.Integer(), nullable=False, server_default="0"))
        # Remove default afterwards to avoid breaking not null constraint
        op.alter_column("role_permissions", "account_id", server_default=None)

    # Ensure composite PK
    _drop_pk("role_permissions")
    op.create_primary_key(
        "role_permissions_pkey",
        "role_permissions",
        ["account_id", "role_id", "permission_id"],
    )

    # Recreate FKs
    _drop_fk("role_permissions", "role_permissions_role_id_fkey")
    _drop_fk("role_permissions", "role_permissions_permission_id_fkey")

    op.create_foreign_key(
        "fk_role_permission_role",
        "role_permissions",
        "roles",
        ["account_id", "role_id"],
        ["account_id", "id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "fk_role_permission_permission",
        "role_permissions",
        "permissions",
        ["account_id", "permission_id"],
        ["account_id", "id"],
        ondelete="CASCADE",
    )

    # === system_user_roles ===
    # Rename user_id -> system_user_id if needed
    if _column_exists("system_user_roles", "user_id") and not _column_exists(
        "system_user_roles", "system_user_id"
    ):
        op.alter_column("system_user_roles", "user_id", new_column_name="system_user_id")

    if not _column_exists("system_user_roles", "account_id"):
        op.add_column("system_user_roles", sa.Column("account_id", sa.Integer(), nullable=False, server_default="0"))
        op.alter_column("system_user_roles", "account_id", server_default=None)

    _drop_pk("system_user_roles")
    op.create_primary_key(
        "system_user_roles_pkey",
        "system_user_roles",
        ["account_id", "system_user_id", "role_id"],
    )

    _drop_fk("system_user_roles", "system_user_roles_role_id_fkey")
    _drop_fk("system_user_roles", "system_user_roles_user_id_fkey")
    _drop_fk("system_user_roles", "system_user_roles_system_user_id_fkey")  # in case name differs

    op.create_foreign_key(
        "fk_system_user_role_user",
        "system_user_roles",
        "system_users",
        ["account_id", "system_user_id"],
        ["account_id", "id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "fk_system_user_role_role",
        "system_user_roles",
        "roles",
        ["account_id", "role_id"],
        ["account_id", "id"],
        ondelete="CASCADE",
    )


def downgrade() -> None:
    """Revert changes (simplified)."""
    # Reverse system_user_roles
    _drop_fk("system_user_roles", "fk_system_user_role_user")
    _drop_fk("system_user_roles", "fk_system_user_role_role")
    _drop_pk("system_user_roles")
    op.create_primary_key("system_user_roles_pkey", "system_user_roles", ["system_user_id", "role_id"])
    if _column_exists("system_user_roles", "account_id"):
        op.drop_column("system_user_roles", "account_id")
    if _column_exists("system_user_roles", "system_user_id") and not _column_exists(
        "system_user_roles", "user_id"
    ):
        op.alter_column("system_user_roles", "system_user_id", new_column_name="user_id")

    # Reverse role_permissions
    _drop_fk("role_permissions", "fk_role_permission_role")
    _drop_fk("role_permissions", "fk_role_permission_permission")
    op.create_primary_key("role_permissions_pkey", "role_permissions", ["role_id", "permission_id"])
    if _column_exists("role_permissions", "account_id"):
        op.drop_column("role_permissions", "account_id")
